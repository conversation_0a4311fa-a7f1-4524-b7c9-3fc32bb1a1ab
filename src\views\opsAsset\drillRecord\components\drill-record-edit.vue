<!-- 演练记录编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑演练记录' : '新建演练记录'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="系统名称" prop="system_name">
              <el-select
                v-model="form.system_name"
                placeholder="请选择系统名称"
                style="width: 100%"
                filterable
                :loading="systemLoading"
              >
                <el-option
                  v-for="system in businessSystems"
                  :key="system.value"
                  :label="system.label"
                  :value="system.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="演练日期" prop="drill_date">
              <el-date-picker
                v-model="form.drill_date"
                type="date"
                placeholder="选择演练日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="所属周期" prop="affiliated_cycle">
              <el-select
                v-model="form.affiliated_cycle"
                placeholder="请选择所属周期"
                style="width: 100%"
              >
                <el-option
                  v-for="cycle in cycleOptions"
                  :key="cycle.value"
                  :label="cycle.label"
                  :value="cycle.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="演练场景" prop="drill_scenario">
              <el-select
                v-model="form.drill_scenario"
                placeholder="请选择演练场景"
                style="width: 100%"
              >
                <el-option label="故障恢复演练" value="故障恢复演练" />
                <el-option label="安全防护演练" value="安全防护演练" />
                <el-option label="性能压力演练" value="性能压力演练" />
                <el-option label="灾备切换演练" value="灾备切换演练" />
                <el-option label="系统升级演练" value="系统升级演练" />
                <el-option label="其他场景" value="其他场景" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="负责人" prop="pic">
              <el-input
                v-model="form.pic"
                placeholder="请输入负责人"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结果" prop="result">
              <el-select
                v-model="form.result"
                placeholder="请选择结果"
                style="width: 100%"
              >
                <el-option label="成功" value="成功" />
                <el-option label="部分成功" value="部分成功" />
                <el-option label="失败" value="失败" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="下次演练" prop="next_drill">
          <el-input
            v-model="form.next_drill"
            placeholder="请输入下次演练计划"
            clearable
            :maxlength="200"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { createInst, updateInst, searchBusiness } from '@/api/cmdb';

  defineOptions({ name: 'DrillRecordEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'drill_record';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 业务系统列表 */
  const businessSystems = ref([]);

  /** 业务系统搜索loading */
  const systemLoading = ref(false);

  /** 获取当前日期计算周期选项 */
  const getCycleOptions = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1; // 月份从0开始，需要+1

    let options = [];

    if (month >= 7) {
      // 如果当前是下半年
      options = [
        {
          label: `${year}年下半年（7-12月）`,
          value: `${year}年下半年（7-12月）`
        },
        {
          label: `${year + 1}年上半年（1-6月）`,
          value: `${year + 1}年上半年（1-6月）`
        },
        {
          label: `${year + 1}年下半年（7-12月）`,
          value: `${year + 1}年下半年（7-12月）`
        }
      ];
    } else {
      // 如果当前是上半年
      options = [
        {
          label: `${year}年上半年（1-6月）`,
          value: `${year}年上半年（1-6月）`
        },
        {
          label: `${year}年下半年（7-12月）`,
          value: `${year}年下半年（7-12月）`
        },
        {
          label: `${year + 1}年上半年（1-6月）`,
          value: `${year + 1}年上半年（1-6月）`
        }
      ];
    }

    return options;
  };

  /** 周期选项 */
  const cycleOptions = ref(getCycleOptions());

  /** 表单数据 */
  const form = ref({
    system_name: '',
    drill_date: '',
    affiliated_cycle: '',
    drill_scenario: '',
    pic: '',
    result: '',
    next_drill: '',
    remark: ''
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {
      system_name: [
        { required: true, message: '请选择系统名称', trigger: 'change' }
      ],
      drill_date: [
        { required: true, message: '请选择演练日期', trigger: 'change' }
      ],
      affiliated_cycle: [
        { required: true, message: '请选择所属周期', trigger: 'change' }
      ],
      drill_scenario: [
        { required: true, message: '请选择演练场景', trigger: 'change' }
      ],
      pic: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
      result: [{ required: true, message: '请选择结果', trigger: 'change' }]
    };
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.system_name}_${data.drill_scenario}_${data.drill_date}`
            }
          });
          EleMessage.success('修改成功');
        } else {
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.system_name}_${data.drill_scenario}_${data.drill_date}`
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    // 不要清空businessSystems，保持数据可用
  };

  /** 加载业务系统数据 */
  const loadBusinessSystems = async () => {
    systemLoading.value = true;
    try {
      const res = await searchBusiness({});
      businessSystems.value = res.rows.map((item) => ({
        label: item.bk_inst_name,
        value: item.bk_biz_name
      }));
    } catch (e) {
      console.error('加载业务系统失败:', e);
    }
    systemLoading.value = false;
  };

  /** 监听弹窗打开状态 */
  watch(
    () => visible.value,
    (newValue) => {
      if (newValue) {
        // 弹窗打开时，确保业务系统数据已加载
        if (businessSystems.value.length === 0) {
          loadBusinessSystems();
        }
      }
    }
  );

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
      } else {
        // 新增模式，重置表单
        Object.assign(form.value, {
          system_name: '',
          drill_date: '',
          affiliated_cycle: '',
          drill_scenario: '',
          pic: '',
          result: '',
          next_drill: '',
          remark: ''
        });
      }
    },
    { immediate: true }
  );

  onMounted(() => {
    // 初始化业务系统列表
    loadBusinessSystems();
  });
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }
</style>
