<template>
  <el-dialog
    :model-value="visible"
    title="关闭工单"
    width="500px"
    :destroy-on-close="true"
    @update:model-value="updateVisible"
  >
    <div class="close-content">
      <div class="warning-info">
        <el-icon class="warning-icon"><WarningFilled /></el-icon>
        <p>确认要关闭以下工单吗？此操作不可逆！</p>
      </div>

      <div class="ticket-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="工单ID">
            {{ ticketData?.ticket_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单类型">
            {{ ticketData?.ticket_type || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag
              v-if="ticketData?.level"
              :type="getLevelType(ticketData.level)"
              size="small"
            >
              {{ ticketData.level }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ ticketData?.create_user_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            {{ ticketData?.comment || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent=""
      >
        <el-form-item label="关闭原因" prop="closeReason">
          <el-input
            v-model="form.closeReason"
            type="textarea"
            :rows="3"
            placeholder="请输入关闭原因"
            :maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="danger" :loading="loading" @click="handleConfirm">
          确认关闭
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { WarningFilled } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';
  import { closeTicket } from '@/api/ticket';

  defineOptions({ name: 'TicketClose' });

  const props = defineProps({
    modelValue: Boolean,
    ticketData: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const visible = computed(() => props.modelValue);

  const formRef = ref(null);
  const loading = ref(false);

  const form = ref({
    closeReason: ''
  });

  const rules = {
    closeReason: [
      { required: true, message: '请输入关闭原因', trigger: 'blur' }
    ]
  };

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleConfirm = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        await closeTicket({
          ticketId: props.ticketData?.ticket_id,
          approval: false, // 关闭工单时传false
          closeReason: form.value.closeReason
        });

        EleMessage.success('工单已关闭');
        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '关闭工单失败');
      }
      loading.value = false;
    });
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const getLevelType = (level) => {
    const levelTypes = {
      p1: 'danger',
      p2: 'warning',
      p3: 'primary',
      p4: 'info'
    };
    return levelTypes[level] || 'info';
  };

  watch(
    () => props.modelValue,
    (value) => {
      if (!value) {
        form.value.closeReason = '';
        formRef.value?.resetFields();
      }
    }
  );
</script>

<style scoped>
  .close-content {
    max-height: 60vh;
    overflow-y: auto;
  }

  .warning-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px;
    background-color: #fef0f0;
    border: 1px solid #fde2e2;
    border-radius: 4px;
  }

  .warning-icon {
    color: #f56c6c;
    font-size: 20px;
    margin-right: 8px;
  }

  .warning-info p {
    margin: 0;
    color: #f56c6c;
    font-weight: 500;
  }

  .ticket-info {
    margin-bottom: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
</style>
