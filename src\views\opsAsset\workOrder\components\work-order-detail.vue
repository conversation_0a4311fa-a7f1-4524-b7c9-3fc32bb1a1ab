<template>
  <el-drawer
    :model-value="visible"
    title="工单详情"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
  >
    <div class="detail-container" v-if="data">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="工单名称">
          {{ data.tickets_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ data.tickets_applicant || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="系统名称">
          {{ data.system_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="部门名称">
          {{ data.department_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="终止时间">
          {{ formatTime(data.tickets_endtime) }}
        </el-descriptions-item>
        <el-descriptions-item label="上线时间">
          {{ formatTime(data.reality_start_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(data.ticket_create_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="工单状态">
          <el-tag :type="getStatusType(data.tickets_status)" size="small">
            {{ data.tickets_status || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="耗时时间">
          {{ formatSpendTime(data.tickets_spendtime) }}
        </el-descriptions-item>
        <el-descriptions-item label="流程ID">
          {{ data.flow_instance_id || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="工单描述" span="2">
          <div class="description-content">
            {{ data.description || '-' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'WorkOrderDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  const visible = computed(() => props.modelValue);

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleClose = () => {
    updateVisible(false);
  };

  /** 获取状态标签类型 */
  const getStatusType = (status) => {
    const statusTypes = {
      0: 'warning', // 待处理
      1: 'primary', // 进行中
      2: 'success', // 已完成
      3: 'danger' // 已关闭
    };
    return statusTypes[status] || 'info';
  };

  /** 格式化时间 */
  const formatTime = (timeValue) => {
    if (!timeValue) return '-';
    try {
      const date = new Date(timeValue);
      if (isNaN(date.getTime())) return '-';
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return '-';
    }
  };

  /** 格式化耗时时间 */
  const formatSpendTime = (spendTime) => {
    if (!spendTime || spendTime <= 0) return '-';

    const hours = Math.floor(spendTime / 3600);
    const minutes = Math.floor((spendTime % 3600) / 60);

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟`;
    } else {
      return `${spendTime}秒`;
    }
  };
</script>

<style scoped>
  .detail-container {
    padding: 0 16px;
  }

  .description-content {
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .drawer-footer {
    text-align: right;
    padding: 16px;
    border-top: 1px solid #e4e7ed;
  }
</style>
