import request from '@/utils/request';
const config = {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
};
/**
 * 业务查询
 */
export async function searchBusiness() {
  const res = await request.get('/search/business');
  if (res.data.code === 200) {
    return {
      code: res.data.code,
      msg: res.data.msg,
      rows: res.data.data.info, // 将 info 数组作为数据
      total: res.data.data.count // 总数
    };
  }
  return Promise.reject(new Error(res.data.msg));
}

// 查询业务（条件查询）
export async function searchBusinessByCondition(data) {
  const res = await request.post('/searchBusiness', data);

  if (res.data.code === 200) {
    return {
      code: res.data.code,
      msg: res.data.msg,
      rows: res.data.data.info, // 将 info 数组作为数据
      total: res.data.data.count // 总数
    };
  }
  return Promise.reject(new Error(res.data.msg));
}

// 业务的修改
export async function updateBusiness(data) {
  const res = await request.post('/updateBusiness', data);
  if (res.data.code === 200) {
    return {
      code: res.data.code,
      msg: res.data.msg
    };
  }
  return Promise.reject(new Error(res.data.msg));
}

// 业务的新增
export async function createBusiness(data) {
  const res = await request.post('/createBusiness', data);
  if (res.data.code === 200) {
    return {
      code: res.data.code,
      msg: res.data.msg
    };
  }
  return Promise.reject(new Error(res.data.msg));
}

// 业务的删除
export async function updateBusinessStatus(data) {
  const res = await request.post('/updateBusinessStatus', data);
  if (res.data.code === 200) {
    return {
      code: res.data.code,
      msg: res.data.msg
    };
  }
  return Promise.reject(new Error(res.data.msg));
}
/**
 * 分页查询配置
 */
export async function searchAllInst(data) {
  const res = await request.post('/searchAllInst', data, config);
  if (res.data.code === 200) {
    return {
      code: res.data.code,
      msg: res.data.msg,
      rows: res.data.data.info, // 将 info 数组作为数据
      total: res.data.data.count // 总数
    };
  }
  return Promise.reject(new Error(res.data.msg));
}

export async function searchInst(data) {
  const res = await request.post('/searchInst', data);
  if (res.data.code === 200) {
    if (res.data.code === 200) {
      return {
        code: res.data.code,
        msg: res.data.msg,
        rows: res.data.data.info, // 将 info 数组作为数据
        total: res.data.data.count // 总数
      };
    }
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增实例
 */
export async function createInst(data) {
  const res = await request.post('/createInst', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 编辑实例
 */
export async function updateInst(data) {
  const res = await request.post('/updateInst', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 编辑实例
 */
export async function batchUpdateInst(data) {
  const res = await request.post('/batchUpdateInst', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 删除实例
 */
export async function deleteInst(data) {
  const res = await request.post('/deleteInst', data, config);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 批量删除实例
 */
export async function batchDeleteInst(data) {
  const res = await request.post('/batchDeleteInst', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出实例
 */
export async function exportInst(data) {
  const res = await request.post('/cmdb/export', data, config);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 下载模板
 */
export async function importTemplate(data) {
  const res = await request.post('/cmdb/importTemplate', data, {
    ...config,
    responseType: 'blob'
  });
  return res.data;
}
/**
 * 导入
 */
export async function importExcel(data) {
  const res = await request.post('/cmdb/importExcel', data, config);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
