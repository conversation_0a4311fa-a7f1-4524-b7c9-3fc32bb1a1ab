<template>
  <ele-drawer
    v-model="visible"
    :size="1000"
    title="监管法规详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="regulation-detail" v-if="data">
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '120px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="文件名称" :span="2">
          {{ data?.file_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="发布机构">
          {{ data?.issuing_authority || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="发布时间">
          {{ data?.issue_time || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="生效时间">
          {{ data?.effective_time || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="失效时间">
          {{ data?.expiration_time || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="管理领域">
          {{ data?.management_domain || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="文件状态">
          <el-tag
            :type="data?.file_status === '生效中' ? 'success' : 'danger'"
            size="small"
          >
            {{ data?.file_status || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="上传人员">
          {{ data?.uploader || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="附件" :span="2">
          <div
            v-if="getAttachments(data?.attachment).length > 0"
            class="file-list"
          >
            <div
              v-for="(attachment, index) in getAttachments(data?.attachment)"
              :key="index"
              class="file-item document-item"
            >
              <el-icon class="file-icon document-icon">
                <component
                  :is="
                    getDocumentIcon(
                      attachment.fileName || getFileName(attachment.url)
                    )
                  "
                />
              </el-icon>
              <span class="file-name">{{
                attachment.fileName || getFileName(attachment.url)
              }}</span>
              <div class="file-actions">
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="
                    previewDocument(
                      attachment.url,
                      attachment.fileName || getFileName(attachment.url)
                    )
                  "
                >
                  预览
                </el-button>
                <el-button
                  type="default"
                  size="small"
                  link
                  @click="
                    downloadFile(
                      attachment.url,
                      attachment.fileName || getFileName(attachment.url)
                    )
                  "
                >
                  下载
                </el-button>
              </div>
            </div>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 文件预览组件 -->
    <FilePreview ref="filePreviewRef" v-model="showPreview" />
  </ele-drawer>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import { Document, DocumentCopy, Reading } from '@element-plus/icons-vue';
  import FilePreview from '@/components/FilePreview/index.vue';

  defineOptions({ name: 'RegulatoryLawsDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  /** 文件预览相关 */
  const filePreviewRef = ref(null);
  const showPreview = ref(false);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 获取附件列表 */
  const getAttachments = (attachmentData) => {
    if (!attachmentData) return [];

    if (typeof attachmentData === 'string') {
      try {
        // 尝试解析JSON字符串
        const parsed = JSON.parse(attachmentData);
        if (Array.isArray(parsed)) {
          return parsed.map((item) => ({
            url: item.url,
            fileName: item.fileName,
            ossId: item.ossId
          }));
        }
      } catch {
        // 如果解析失败，按逗号分隔的URL字符串处理（兼容旧格式）
        return attachmentData
          .split(',')
          .filter((url) => url.trim())
          .map((url) => ({
            url: url,
            fileName: getFileName(url),
            ossId: ''
          }));
      }
    } else if (Array.isArray(attachmentData)) {
      // 数组格式（兼容）
      return attachmentData.map((item) => ({
        url: item.url,
        fileName: item.fileName,
        ossId: item.ossId
      }));
    }

    return [];
  };

  /** 获取文件名 */
  const getFileName = (url) => {
    if (!url) return '';
    const filename = url.split('/').pop() || '附件文件';
    return filename.includes('.') ? filename : '附件文件.pdf';
  };

  /** 获取文档图标 */
  const getDocumentIcon = (filename) => {
    const lowerName = filename.toLowerCase();
    if (lowerName.endsWith('.pdf')) {
      return Reading;
    } else if (lowerName.endsWith('.doc') || lowerName.endsWith('.docx')) {
      return DocumentCopy;
    }
    return Document;
  };

  /** 下载文件 */
  const downloadFile = async (url, fileName) => {
    try {
      // 尝试使用 fetch 获取文件
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();

      // 创建 blob URL 并下载
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName || '附件文件';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 释放 blob URL
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('下载失败:', error);
      // 如果 fetch 失败，回退到直接下载方式
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || '附件文件';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  /** 预览文档 */
  const previewDocument = (url, fileName) => {
    const attachment = {
      url: url,
      fileName: fileName
    };
    filePreviewRef.value?.previewFile(attachment);
  };

  /** 关闭抽屉 */
  const handleClose = () => {
    visible.value = false;
  };
</script>

<style scoped>
  .regulation-detail {
    padding: 0 8px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0;
  }

  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    transition: all 0.3s ease;
    background: var(--el-fill-color-lighter);
    min-width: 80px;
    position: relative;
  }

  .file-item:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-icon {
    font-size: 18px;
    flex-shrink: 0;
  }

  .document-icon {
    color: var(--el-color-primary);
  }

  .file-name {
    font-size: 12px;
    color: var(--el-text-color-regular);
    word-break: break-all;
    line-height: 1.2;
    flex: 1;
    min-width: 0;
  }

  .file-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }
</style>
