<template>
  <ele-page>
    <!-- 搜索组件 -->
    <simple-search
      :fields="searchFields"
      :default-field="defaultField"
      :advanced-conditions="advancedConditions"
      @search="handleSimpleSearch"
      @reset="handleSearchReset"
      @advanced-search="showAdvancedModal"
      @clear-advanced="clearAdvancedConditions"
      @remove-advanced-condition="removeAdvancedCondition"
    />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <cmdb-pro-table
        ref="tableRef"
        row-key="bk_inst_id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '系统事件' }"
        cache-key="regulatoryLawsTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="success"
            class="ele-btn-icon"
            :icon="UploadOutlined"
            @click="openImport()"
          >
            导入
          </el-button>
          <el-button
            type="warning"
            class="ele-btn-icon"
            :icon="EditOutlined"
            :disabled="!selections.length"
            @click="openBatchEdit()"
          >
            批量编辑
          </el-button>
          <el-button
            type="danger"
            class="ele-btn-icon hidden-sm-and-down"
            :icon="DeleteOutlined"
            :disabled="!selections.length"
            @click="removeBatch()"
          >
            批量删除
          </el-button>
        </template>
        <template #event_screenshot="{ row }">
<<<<<<< HEAD
          <div v-if="hasAttachments(row.event_screenshot)" class="image-list">
            <el-popover placement="bottom" width="auto" trigger="click">
              <template #reference>
                <div class="image-item">
                  <el-icon class="image-icon">
                    <Picture />
                  </el-icon>
                  <span
                    v-if="getAttachmentCount(row.event_screenshot) > 1"
                    class="image-count-text"
                  >
                    【{{ getAttachmentCount(row.event_screenshot) }}】
                  </span>
                </div>
              </template>
              <div class="image-gallery">
                <div
                  v-for="(url, index) in getImageUrls(row.event_screenshot)"
                  :key="index"
                  class="gallery-item"
                >
                  <img
                    :src="url"
                    :alt="`事件截图${index + 1}`"
                    class="gallery-thumbnail"
                    @click="openImagePreview(url)"
                  />
                </div>
              </div>
            </el-popover>
=======
          <div
            v-if="getScreenshots(row.event_screenshot).length > 0"
            class="file-list"
          >
            <div
              class="file-item image-item"
              @click="openImageGallery(getScreenshots(row.event_screenshot))"
            >
              <el-icon class="file-icon image-icon"><Picture /></el-icon>
              <span
                v-if="getScreenshots(row.event_screenshot).length > 1"
                class="image-count-text"
              >
                {{ getScreenshots(row.event_screenshot).length }}
              </span>
            </div>
>>>>>>> dev0920
          </div>
          <span v-else>-</span>
        </template>
        <template #event_report="{ row }">
          <div v-if="hasDocuments(row.event_report)" class="file-list">
            <el-popover placement="bottom" width="auto" trigger="click">
              <template #reference>
                <div class="file-item document-item">
                  <el-icon class="file-icon document-icon">
                    <component
                      :is="
                        getDocumentIcon(
                          getDocuments(row.event_report)[0]?.fileName ||
                            '事件报告'
                        )
                      "
                    />
                  </el-icon>
                  <span
                    v-if="getDocumentCount(row.event_report) > 1"
                    class="file-count-text"
                  >
                    【{{ getDocumentCount(row.event_report) }}】
                  </span>
                </div>
              </template>
              <div class="attachment-options">
                <div
                  v-for="(document, index) in getDocuments(row.event_report)"
                  :key="index"
                  class="attachment-item"
                >
                  <div class="attachment-info">
                    <el-icon class="attachment-icon">
                      <component
                        :is="getDocumentIcon(document.fileName || '事件报告')"
                      />
                    </el-icon>
                    <span class="attachment-name">{{
                      document.fileName || `事件报告${index + 1}`
                    }}</span>
                  </div>
                  <div class="attachment-actions">
                    <el-button
                      type="primary"
                      size="small"
                      link
                      @click="previewDocument(document)"
                    >
                      预览
                    </el-button>
                    <el-button
                      type="default"
                      size="small"
                      link
                      @click="downloadDocument(document)"
                    >
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </el-popover>
          </div>
          <span v-else>-</span>
        </template>
        <template #action="{ row }">
          <el-link type="info" underline="never" @click="openDetail(row)">
            详情
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            v-permission="'system:config:edit'"
            type="primary"
            underline="never"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['system:config:edit', 'system:config:remove']"
            direction="vertical"
          />
          <el-link
            v-permission="'system:config:remove'"
            type="danger"
            underline="never"
            @click="removeSingle(row)"
          >
            删除
          </el-link>
        </template>
      </cmdb-pro-table>
    </ele-card>

    <!-- 详情弹窗 -->
    <system-event-detail :data="currentDetail" v-model="showDetail" />

    <!-- 高级搜索弹窗 -->
    <advanced-search
      v-model="showAdvanced"
      :search-fields="searchFields"
      :initial-conditions="advancedConditions"
      @search="handleAdvancedSearch"
      @close="closeAdvancedModal"
    />

    <!-- 编辑弹窗 -->
    <system-event-edit :data="current" v-model="showEdit" @done="reload" />

    <!-- 导入弹窗 -->
    <system-event-import v-model="showImport" @done="reload" />

    <!-- 批量编辑弹窗 -->
    <system-event-batch-edit
      v-model="showBatchEdit"
      v-model:selectedRecords="selections"
      @done="reload"
    />

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="80%"
      :destroy-on-close="true"
    >
      <div class="image-gallery-container">
        <div class="image-preview-main">
          <img :src="currentImageUrl" alt="预览图片" class="preview-image" />
        </div>

        <!-- 多图片时显示切换控件 -->
        <div v-if="imageGallery.length > 1" class="image-controls">
          <div class="image-thumbnails">
            <div
              v-for="(image, index) in imageGallery"
              :key="index"
              class="thumbnail-item"
              :class="{ active: currentImageIndex === index }"
              @click="switchToImage(index)"
            >
              <img
                :src="image"
                :alt="`缩略图 ${index + 1}`"
                class="thumbnail-image"
              />
            </div>
          </div>

          <div class="image-navigation">
            <el-button
              :disabled="currentImageIndex === 0"
              @click="previousImage"
              size="small"
            >
              <el-icon><ArrowLeft /></el-icon>
              上一张
            </el-button>

            <span class="image-counter">
              {{ currentImageIndex + 1 }} / {{ imageGallery.length }}
            </span>

            <el-button
              :disabled="currentImageIndex === imageGallery.length - 1"
              @click="nextImage"
              size="small"
            >
              下一张
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 文件预览组件 -->
    <FilePreview ref="filePreviewRef" v-model="showFilePreview" />
  </ele-page>
</template>

<script setup>
  import { ref, computed, h } from 'vue';
  import { ElMessageBox, ElDialog } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import {
    PlusOutlined,
    DeleteOutlined,
    UploadOutlined,
    EditOutlined,
    ArrowLeft,
    ArrowRight
  } from '@/components/icons';
  import {
    Picture,
    Document,
    DocumentCopy,
    Reading
  } from '@element-plus/icons-vue';
  import { searchInst, deleteInst, batchDeleteInst } from '@/api/cmdb';
  import SimpleSearch from '@/components/Search/simple-search.vue';
  import AdvancedSearch from '@/components/Search/advanced-search.vue';
  import SystemEventEdit from './components/system-event-edit.vue';
  import SystemEventDetail from './components/system-event-detail.vue';
  import SystemEventImport from './components/system-event-import.vue';
  import SystemEventBatchEdit from './components/system-event-batch-edit.vue';
  import FilePreview from '@/components/FilePreview/index.vue';
  /** 模型实例ID */
  const bkObjId = 'system_events_test';
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'event_name',
        columnKey: 'event_name',
        label: '事件标题',
        align: 'center',
        width: 160,
        fixed: 'left'
      },
      {
        prop: 'recorder',
        label: '响应/ 记录人',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'event_desc',
        label: '事件现象描述',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'occurrence_time',
        label: '发生时间',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'event_source',
        label: '事件来源',
        align: 'center',
        minWidth: 100
      },
      { prop: 'event_level', label: '事件级别', align: 'center', minWidth: 80 },
      {
        prop: 'impact_scope',
        label: '影响范围',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'affected_business',
        label: '影响业务',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'event_screenshot',
        columnKey: 'event_screenshot',
        label: '事件现象截图',
        align: 'center',
        minWidth: 110,
        slot: 'event_screenshot'
      },
      {
        prop: 'fault_category',
        label: '故障类别',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'root_cause_fault_system',
        label: '根因故障系统',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'system_level',
        label: '系统级别',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'event_cause',
        label: '事件原因',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'dev_handler',
        label: '科创处置人员',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'ops_handler',
        label: '运维处置人员',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'event_handling_process',
        label: '事件处理过程',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'event_handling_results_and_basis',
        label: '事件处理结果和依据',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'resolution_time',
        label: '解决时间',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'solution_and_whether_production_change_is_involved',
        label: '解决方案是否涉及生产变更',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'solution_and_whether_oa_process_is_involved',
        label: '解决方案和是否涉及OA流程',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'change_order_no_causing_fault',
        label: '引发故障的变更单号',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'change_requester',
        label: '变更申请人',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'development_supplier',
        label: '开发供应商',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'whether_review_is_conducted',
        label: '是否进行复盘',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'review_meeting_date',
        label: '复盘会召开日期',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'whether_event_report_is_formed',
        label: '是否形成事件报告',
        align: 'center',
        minWidth: 140
      },
      {
        prop: 'event_report',
        columnKey: 'event_report',
        label: '事件报告',
        align: 'center',
        slot: 'event_report'
      },
      { prop: 'remarks', label: '备注', align: 'center', minWidth: 140 },
      {
        columnKey: 'action',
        label: '操作',
        width: 160,
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true,
        fixed: 'right'
      }
    ];
  });
  /** 表格选中数据 */
  const selections = ref([]);
  /** 当前编辑数据 */
  const current = ref(null);

  /** 搜索字段配置 */
  const searchFields = ref([
    { prop: 'event_name', label: '事件标题', type: 'text' },
    {
      prop: 'event_level',
      label: '事件级别',
      type: 'select',
      options: [
        { label: 'I级', value: 'I' },
        { label: 'II级', value: 'II' },
        { label: 'III级', value: 'III' },
        { label: 'IV级', value: 'IV' }
      ]
    },
    { prop: 'event_source', label: '事件来源', type: 'text' },
    { prop: 'fault_category', label: '故障类别', type: 'text' },
    { prop: 'recorder', label: '响应记录人', type: 'text' },
    { prop: 'occurrence_time', label: '发生时间', type: 'daterange' },
    { prop: 'event_desc', label: '事件现象描述', type: 'text' },
    { prop: 'impact_scope', label: '影响范围', type: 'text' },
    { prop: 'affected_business', label: '影响业务', type: 'text' },
    { prop: 'root_cause_fault_system', label: '根因故障系统', type: 'text' },
    { prop: 'system_level', label: '系统级别', type: 'text' },
    { prop: 'event_cause', label: '事件原因', type: 'text' },
    { prop: 'dev_handler', label: '科创处置人员', type: 'text' },
    { prop: 'ops_handler', label: '运维处置人员', type: 'text' },
    { prop: 'resolution_time', label: '解决时间', type: 'date' },
    {
      prop: 'change_order_no_causing_fault',
      label: '引发故障的变更单号',
      type: 'text'
    },
    { prop: 'change_requester', label: '变更申请人', type: 'text' },
    { prop: 'development_supplier', label: '开发供应商', type: 'text' }
  ]);

  /** 默认搜索字段 */
  const defaultField = ref({
    prop: 'event_name',
    label: '事件标题'
  });

  /** 高级搜索条件 */
  const advancedConditions = ref([]);

  /** 当前搜索条件 */
  const currentSearchParams = ref({});

  /** 是否显示高级搜索弹窗 */
  const showAdvanced = ref(false);

  /** 是否显示详情弹窗 */
  const showDetail = ref(false);

  /** 当前详情数据 */
  const currentDetail = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示导入弹窗 */
  const showImport = ref(false);

  /** 是否显示批量编辑弹窗 */
  const showBatchEdit = ref(false);

  /** 图片预览相关 */
  const imagePreviewVisible = ref(false);
  const previewImageUrl = ref('');

  /** 文件预览相关 */
  const filePreviewRef = ref(null);
  const showFilePreview = ref(false);

  /** 图片画廊相关 */
  const imageGallery = ref([]);
  const currentImageIndex = ref(0);
  const currentImageUrl = computed(() => {
    return imageGallery.value[currentImageIndex.value] || '';
  });
  /** 表格数据源 */
  const datasource = async ({ pages }) => {
    let conditions = [];

    // 处理简单搜索条件
    if (currentSearchParams.value.condition) {
      Object.keys(currentSearchParams.value.condition).forEach((field) => {
        const value = currentSearchParams.value.condition[field];
        if (typeof value === 'object' && value !== null) {
          // 处理操作符对象格式，例如 { $regex: "keyword" }
          Object.keys(value).forEach((operator) => {
            conditions.push({
              field: field,
              operator: operator,
              value: value[operator]
            });
          });
        } else {
          // 处理直接值格式
          conditions.push({
            field: field,
            operator: '$eq',
            value: value
          });
        }
      });
    }

    // 添加高级搜索条件（已经是正确格式）
    if (advancedConditions.value.length > 0) {
      conditions = conditions.concat(
        advancedConditions.value.filter(
          (condition) =>
            condition.field &&
            condition.operator &&
            condition.value !== '' &&
            condition.value !== null &&
            condition.value !== undefined
        )
      );
    }

    const page = {
      start: (pages.pageNum - 1) * pages.pageSize,
      limit: pages.pageSize,
      sort: 'bk_inst_id'
    };

    // 按照新的参考格式组织条件参数
    const conditionParam =
      conditions.length > 0
        ? {
            [bkObjId]: conditions
          }
        : undefined;

    const res = await searchInst({
      bk_obj_id: bkObjId,
      condition: conditionParam,
      page
    });

    return {
      code: res.code,
      msg: res.msg,
      rows: res.rows,
      total: res.total
    };
  };
  /** 处理简单搜索 */
  const handleSimpleSearch = (params) => {
    currentSearchParams.value = params;
    reload();
  };

  /** 处理搜索重置 */
  const handleSearchReset = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    reload();
  };

  /** 显示高级搜索弹窗 */
  const showAdvancedModal = () => {
    showAdvanced.value = true;
  };

  /** 关闭高级搜索弹窗 */
  const closeAdvancedModal = () => {
    showAdvanced.value = false;
  };

  /** 处理高级搜索 */
  const handleAdvancedSearch = (conditions) => {
    advancedConditions.value = conditions;
    reload();
  };

  /** 清空高级搜索条件 */
  const clearAdvancedConditions = () => {
    advancedConditions.value = [];
    reload();
  };

  /** 移除单个高级搜索条件 */
  const removeAdvancedCondition = (condition) => {
    const index = advancedConditions.value.findIndex(
      (c) =>
        c.field === condition.field &&
        c.operator === condition.operator &&
        c.value === condition.value
    );
    if (index > -1) {
      advancedConditions.value.splice(index, 1);
      reload();
    }
  };

  /** 打开详情弹窗 */
  const openDetail = (row) => {
    currentDetail.value = row;
    showDetail.value = true;
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 打开批量编辑弹窗 */
  const openBatchEdit = () => {
    if (!selections.value.length) {
      EleMessage.error('请选择要编辑的数据');
      return;
    }
    showBatchEdit.value = true;
  };
  /** 单条删除 */
  const removeSingle = (row) => {
    ElMessageBox.confirm(`确定要删除该条系统事件吗？`, '系统提示', {
      type: 'warning'
    })
      .then(async () => {
        const loading = EleMessage.loading('请稍候...');
        try {
          await deleteInst({
            bkObjId,
            instId: row.bk_inst_id
          });
          loading.close();
          EleMessage.success('删除成功');
          reload();
        } catch (e) {
          loading.close();
          EleMessage.error(e.message || '删除失败');
        }
      })
      .catch(() => {});
  };

  /** 批量删除 */
  const removeBatch = () => {
    if (!selections.value.length) {
      EleMessage.error('请选择要删除的数据');
      return;
    }

    ElMessageBox.confirm(
      `确定要删除选中的${selections.value.length}条系统事件吗？`,
      '系统提示',
      { type: 'warning' }
    )
      .then(async () => {
        const loading = EleMessage.loading('请稍候...');
        try {
          const instIds = selections.value.map((item) => item.bk_inst_id);
          await batchDeleteInst({
            bkObjId,
            instIds: instIds
          });
          loading.close();
          EleMessage.success('删除成功');
          reload();
        } catch (e) {
          loading.close();
          EleMessage.error(e.message || '删除失败');
        }
      })
      .catch(() => {});
  };

  /** 刷新表格 */
  const reload = () => {
    tableRef.value?.reload?.();
    selections.value = [];
  };

  /** 获取截图列表 */
  const getScreenshots = (screenshotStr) => {
    if (!screenshotStr) return [];
    return screenshotStr.split(',').filter((url) => url.trim());
  };

  /** 获取报告文件名 */
  const getReportFileName = (url) => {
    if (!url) return '';
    const filename = url.split('/').pop() || '事件报告';
    return filename.includes('.') ? filename : '事件报告.pdf';
  };

  /** 获取文档图标 */
  const getDocumentIcon = (filename) => {
    const lowerName = filename.toLowerCase();
    if (lowerName.endsWith('.pdf')) {
      return Reading;
    } else if (lowerName.endsWith('.doc') || lowerName.endsWith('.docx')) {
      return DocumentCopy;
    }
    return Document;
  };

  /** 检查是否有附件（图片） */
  const hasAttachments = (attachments) => {
    if (!attachments) return false;
    if (typeof attachments === 'string') {
      return attachments.trim() !== '';
    }
    if (Array.isArray(attachments)) {
      return attachments.length > 0;
    }
    return false;
  };

  /** 获取附件数量（图片） */
  const getAttachmentCount = (attachments) => {
    if (!attachments) return 0;
    if (typeof attachments === 'string') {
      return attachments.split(',').filter((url) => url.trim() !== '').length;
    }
    if (Array.isArray(attachments)) {
      return attachments.length;
    }
    return 0;
  };

  /** 检查是否有文档 */
  const hasDocuments = (documents) => {
    if (!documents) return false;
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        return Array.isArray(parsed) && parsed.length > 0;
      } catch {
        return documents.trim() !== '';
      }
    }
    if (Array.isArray(documents)) {
      return documents.length > 0;
    }
    return false;
  };

  /** 获取文档数量 */
  const getDocumentCount = (documents) => {
    if (!documents) return 0;
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        return Array.isArray(parsed) ? parsed.length : 0;
      } catch {
        return documents.trim() !== '' ? 1 : 0;
      }
    }
    if (Array.isArray(documents)) {
      return documents.length;
    }
    return 0;
  };

  /** 获取图片URL列表 */
  const getImageUrls = (attachments) => {
    if (!attachments) return [];
    if (typeof attachments === 'string') {
      return attachments.split(',').filter((url) => url.trim() !== '');
    }
    if (Array.isArray(attachments)) {
      return attachments;
    }
    return [];
  };

  /** 获取文档列表 */
  const getDocuments = (documents) => {
    if (!documents) return [];
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        if (Array.isArray(parsed)) {
          return parsed.map((item) => ({
            url: item.url,
            fileName: item.fileName,
            ossId: item.ossId
          }));
        }
      } catch {
        return documents.trim() !== ''
          ? [{ url: documents, fileName: '事件报告', ossId: '' }]
          : [];
      }
    }
    if (Array.isArray(documents)) {
      return documents.map((item) => ({
        url: item.url,
        fileName: item.fileName,
        ossId: item.ossId
      }));
    }
    return [];
  };

  /** 打开图片预览 */
  const openImagePreview = (url) => {
    previewImageUrl.value = url;
    imagePreviewVisible.value = true;
  };

  /** 预览文档 */
  const previewDocument = (document) => {
    filePreviewRef.value?.previewFile(document);
  };

  /** 下载文档 */
  const downloadDocument = async (document) => {
    const url = document.url;
    const fileName = document.fileName || '事件报告';

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('下载失败:', error);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  /** 预览图片 */
  const previewImage = (imageUrl) => {
    previewImageUrl.value = imageUrl;
    imagePreviewVisible.value = true;
  };

  /** 打开图片画廊 */
  const openImageGallery = (images) => {
    if (images.length === 1) {
      // 单张图片直接预览
      previewImage(images[0]);
    } else {
      // 多张图片开启画廊模式
      imageGallery.value = images;
      currentImageIndex.value = 0;
      imagePreviewVisible.value = true;
    }
  };

  /** 切换到指定图片 */
  const switchToImage = (index) => {
    currentImageIndex.value = index;
  };

  /** 上一张图片 */
  const previousImage = () => {
    if (currentImageIndex.value > 0) {
      currentImageIndex.value--;
    }
  };

  /** 下一张图片 */
  const nextImage = () => {
    if (currentImageIndex.value < imageGallery.value.length - 1) {
      currentImageIndex.value++;
    }
  };
</script>

<style scoped>
  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .file-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    /* padding: 8px; */
    /* border: 1px solid var(--el-border-color); */
    /* border-radius: 6px; */
    cursor: pointer;
    transition: all 0.3s ease;
    /* background: var(--el-fill-color-lighter); */
    min-width: 48px;
    position: relative;
  }

  .image-count-text {
    font-size: 12px;
    color: var(--el-color-primary);
    font-weight: 500;
    margin-left: 2px;
  }

  .file-item:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-icon {
    font-size: 18px;
    flex-shrink: 0;
  }

  .image-icon {
    color: var(--el-color-success);
  }

  .document-icon {
    color: var(--el-color-primary);
  }

  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .image-gallery-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .image-preview-main {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .image-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .image-thumbnails {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 100%;
    overflow-x: auto;
    padding: 8px;
  }

  .thumbnail-item {
    width: 60px;
    height: 60px;
    border: 2px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  .thumbnail-item:hover {
    border-color: var(--el-color-primary);
    transform: scale(1.05);
  }

  .thumbnail-item.active {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }

  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-navigation {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .image-counter {
    font-size: 14px;
    color: var(--el-text-color-regular);
    font-weight: 500;
    min-width: 60px;
    text-align: center;
  }

  /* 图片预览样式 */
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .image-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
  }

  .image-item:hover {
    transform: translateY(-1px);
  }

  .image-icon {
    font-size: 18px;
    color: var(--el-color-success);
    flex-shrink: 0;
  }

  .image-count-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 2px;
  }

  .image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    max-width: 320px;
  }

  .gallery-item {
    position: relative;
    cursor: pointer;
  }

  .gallery-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.3s ease;
  }

  .gallery-thumbnail:hover {
    border-color: var(--el-color-primary);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  /* 文档预览样式 */
  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .file-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
  }

  .file-item:hover {
    transform: translateY(-1px);
  }

  .file-icon {
    font-size: 18px;
    flex-shrink: 0;
  }

  .document-icon {
    color: var(--el-color-primary);
  }

  .file-count-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 2px;
  }

  .attachment-options {
    max-width: 320px;
  }

  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .attachment-item:last-child {
    border-bottom: none;
  }

  .attachment-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .attachment-icon {
    font-size: 16px;
    color: var(--el-color-primary);
    flex-shrink: 0;
  }

  .attachment-name {
    font-size: 14px;
    color: var(--el-text-color-regular);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .attachment-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }
</style>
