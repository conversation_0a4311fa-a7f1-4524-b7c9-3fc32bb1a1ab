<!-- 维保厂家管理编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑维保厂家' : '新建维保厂家'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="project_name">
              <el-select
                v-model="form.project_name"
                placeholder="请选择项目名称"
                style="width: 100%"
                :loading="projectLoading"
                filterable
                clearable
                @focus="loadProjects"
              >
                <el-option
                  v-for="project in projectOptions"
                  :key="project.value"
                  :label="project.label"
                  :value="project.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保标的" prop="maintained_project">
              <el-input
                v-model="form.maintained_project"
                placeholder="请输入维保标的"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维保厂家名称" prop="maintenance_company">
              <el-input
                v-model="form.maintenance_company"
                placeholder="请输入维保厂家名称"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contact">
              <el-input
                v-model="form.contact"
                placeholder="请输入联系人"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contact_tel">
              <el-input
                v-model="form.contact_tel"
                placeholder="请输入联系方式"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保人员" prop="maintenance_engineer">
              <el-input
                v-model="form.maintenance_engineer"
                placeholder="请输入维保人员"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 维保信息 -->
        <div class="form-section-title">维保信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维保方式" prop="maintenance_mode">
              <el-select
                v-model="form.maintenance_mode"
                placeholder="请选择维保方式"
                style="width: 100%"
                clearable
              >
                <el-option label="远程" value="远程" />
                <el-option label="现场" value="现场" />
                <el-option label="驻场" value="驻场" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保金额（万）" prop="maintenance_fee">
              <el-input
                v-model.number="form.maintenance_fee"
                placeholder="请输入维保金额"
                type="number"
                clearable
              >
                <template #append>万</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维保开始日期" prop="maintenance_start_date">
              <el-date-picker
                v-model="form.maintenance_start_date"
                type="datetime"
                placeholder="选择维保开始日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保结束周期" prop="maintenance_end_date">
              <el-date-picker
                v-model="form.maintenance_end_date"
                type="datetime"
                placeholder="选择维保结束周期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维保是否到期提醒" prop="is_maintenance_due_reminder">
              <el-radio-group v-model="form.is_maintenance_due_reminder">
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保评价得分" prop="maintenance_rating">
              <el-input
                v-model.number="form.maintenance_rating"
                placeholder="请输入维保评价得分"
                type="number"
                clearable
                :min="0"
                :max="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { createInst, updateInst, searchAllInst } from '@/api/cmdb';

  defineOptions({ name: 'MaintenanceCompanyInfoEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'maintenance_company_info';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 项目选项 */
  const projectOptions = ref([]);
  const projectLoading = ref(false);

  /** 表单数据 */
  const form = ref({
    project_name: '',
    maintained_project: '',
    maintenance_company: '',
    contact: '',
    contact_tel: '',
    maintenance_mode: '',
    maintenance_engineer: '',
    maintenance_fee: null,
    maintenance_start_date: null,
    maintenance_end_date: null,
    is_maintenance_due_reminder: '',
    maintenance_rating: null
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {
      project_name: [
        { required: true, message: '请选择项目名称', trigger: 'change' }
      ],
      maintenance_company: [
        { required: true, message: '请输入维保厂家名称', trigger: 'blur' }
      ]
    };
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.project_name}_${data.maintenance_company}`
            }
          });
          EleMessage.success('修改成功');
        } else {
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.project_name}_${data.maintenance_company}`
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
  };

  /** 加载项目数据 */
  const loadProjects = async () => {
    projectLoading.value = true;
    try {
      const formData = new FormData();
      formData.append('bkObjId', 'project_detail');

      const res = await searchAllInst(formData);
      if (res && res.rows && Array.isArray(res.rows)) {
        projectOptions.value = res.rows.map((item) => ({
          label: item.project_name || item.bk_inst_name || '未知项目',
          value: item.project_name || item.bk_inst_name || '未知项目'
        }));
      } else {
        projectOptions.value = [];
      }
    } catch (error) {
      console.error('加载项目数据失败:', error);
      EleMessage.error('加载项目数据失败: ' + (error.message || '未知错误'));
      projectOptions.value = [];
    } finally {
      projectLoading.value = false;
    }
  };

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
      } else {
        // 新增模式，重置表单
        Object.assign(form.value, {
          project_name: '',
          maintained_project: '',
          maintenance_company: '',
          contact: '',
          contact_tel: '',
          maintenance_mode: '',
          maintenance_engineer: '',
          maintenance_fee: null,
          maintenance_start_date: null,
          maintenance_end_date: null,
          is_maintenance_due_reminder: '',
          maintenance_rating: null
        });
      }
    },
    { immediate: true }
  );

  /** 初始化时加载项目数据 */
  onMounted(() => {
    loadProjects();
  });
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }
</style>