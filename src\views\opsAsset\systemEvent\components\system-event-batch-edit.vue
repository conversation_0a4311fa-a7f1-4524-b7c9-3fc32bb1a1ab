<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑系统事件"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="event_source">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.event_source" />
                    <span class="label-text">事件来源</span>
                  </div>
                </template>
                <el-input
                  v-model="form.event_source"
                  placeholder="请输入事件来源"
                  clearable
                  :maxlength="100"
                  :disabled="!fieldEnabled.event_source"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="event_level">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.event_level" />
                    <span class="label-text">事件级别</span>
                  </div>
                </template>
                <el-select
                  v-model="form.event_level"
                  placeholder="请选择事件级别"
                  style="width: 100%"
                  :disabled="!fieldEnabled.event_level"
                >
                  <el-option label="I级" value="I" />
                  <el-option label="II级" value="II" />
                  <el-option label="III级" value="III" />
                  <el-option label="IV级" value="IV" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="impact_scope">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.impact_scope" />
                    <span class="label-text">影响范围</span>
                  </div>
                </template>
                <el-input
                  v-model="form.impact_scope"
                  placeholder="请输入影响范围"
                  clearable
                  :maxlength="200"
                  :disabled="!fieldEnabled.impact_scope"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="affected_business">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.affected_business" />
                    <span class="label-text">影响业务</span>
                  </div>
                </template>
                <el-input
                  v-model="form.affected_business"
                  placeholder="请输入影响业务"
                  clearable
                  :maxlength="200"
                  :disabled="!fieldEnabled.affected_business"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <div class="field-with-checkbox">
          <el-form-item prop="root_cause_fault_system">
            <template #label>
              <div class="form-label-with-checkbox">
                <el-checkbox v-model="fieldEnabled.root_cause_fault_system" />
                <span class="label-text">根因故障系统</span>
              </div>
            </template>
            <el-select
              v-model="form.root_cause_fault_system"
              placeholder="请选择根因故障系统"
              style="width: 100%"
              :disabled="!fieldEnabled.root_cause_fault_system"
              @change="handleSystemChange"
              filterable
              :loading="systemLoading"
            >
              <el-option
                v-for="system in businessSystems"
                :key="system.value"
                :label="system.label"
                :value="system.value"
              />
            </el-select>
          </el-form-item>
        </div>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="dev_handler">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.dev_handler" />
                    <span class="label-text">科创处置人员</span>
                  </div>
                </template>
                <el-input
                  v-model="form.dev_handler"
                  placeholder="请输入科创处置人员"
                  clearable
                  :maxlength="100"
                  :disabled="!fieldEnabled.dev_handler"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="ops_handler">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.ops_handler" />
                    <span class="label-text">运维处置人员</span>
                  </div>
                </template>
                <el-input
                  v-model="form.ops_handler"
                  placeholder="请输入运维处置人员"
                  clearable
                  :maxlength="100"
                  :disabled="!fieldEnabled.ops_handler"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="updateVisible(false)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst, searchBusiness } from '@/api/cmdb';

  defineOptions({ name: 'SystemEventBatchEdit' });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 选中的记录 */
  const selectedRecords = defineModel('selectedRecords', {
    type: Array,
    default: () => []
  });

  /** 模型实例ID */
  const bkObjId = 'system_events_test';

  /** 表单实例 */
  const formRef = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 业务系统列表 */
  const businessSystems = ref([]);

  /** 业务系统搜索loading */
  const systemLoading = ref(false);

  /** 字段启用状态 */
  const fieldEnabled = reactive({
    event_source: false,
    event_level: false,
    impact_scope: false,
    affected_business: false,
    root_cause_fault_system: false,
    dev_handler: false,
    ops_handler: false
  });

  /** 表单数据 */
  const form = reactive({
    event_source: '',
    event_level: '',
    impact_scope: '',
    affected_business: '',
    root_cause_fault_system: '',
    dev_handler: '',
    ops_handler: ''
  });

  /** 表单验证规则 */
  const rules = reactive({});

  /** 更新弹窗状态 */
  const updateVisible = (value) => {
    visible.value = value;
  };

  /** 弹窗关闭回调 */
  const handleClosed = () => {
    resetForm();
  };

  /** 重置表单 */
  const resetForm = () => {
    Object.keys(form).forEach((key) => {
      form[key] = '';
    });
    Object.keys(fieldEnabled).forEach((key) => {
      fieldEnabled[key] = false;
    });
    formRef.value?.clearValidate();
  };

  /** 提交表单 */
  const submit = async () => {
    // 检查是否至少启用一个字段
    const hasEnabledField = Object.values(fieldEnabled).some(
      (enabled) => enabled
    );
    if (!hasEnabledField) {
      EleMessage.warning('请至少启用一个字段进行编辑');
      return;
    }

    // 构造要更新的数据，只包含启用的字段
    const instInfoMap = {};
    Object.keys(fieldEnabled).forEach((key) => {
      if (fieldEnabled[key]) {
        instInfoMap[key] = form[key];
      }
    });

    // 构造批量编辑参数
    const batchUpdateData = {
      bkObjId,
      bkInstId: selectedRecords.value.map((record) => record.bk_inst_id),
      instInfoMap
    };

    loading.value = true;
    try {
      await batchUpdateInst(batchUpdateData);
      EleMessage.success(`成功批量编辑 ${selectedRecords.value.length} 条记录`);
      updateVisible(false);
      emit('done');
    } catch (error) {
      EleMessage.error(error.message || '批量编辑失败');
    } finally {
      loading.value = false;
    }
  };

  /** 根因故障系统变化处理 */
  const handleSystemChange = (value) => {
    const system = businessSystems.value.find((s) => s.value === value);
    if (system) {
      // 可以在这里设置系统级别等相关字段
      console.log('选中的系统:', system);
    }
  };

  /** 加载根因故障系统数据 */
  const loadBusinessSystems = async () => {
    systemLoading.value = true;
    try {
      // 使用searchBusiness接口获取所有业务系统，不传keyword参数获取全部数据
      const res = await searchBusiness({});

      businessSystems.value = res.rows.map((item) => ({
        label: item.bk_inst_name,
        value: item.bk_biz_name, // 使用bk_biz_name作为值
        system_level: item.system_level || '' // 保存system_level用于自动填充
      }));
    } catch (e) {
      console.error('加载业务系统失败:', e);
    }
    systemLoading.value = false;
  };

  onMounted(() => {
    // 初始化业务系统列表
    loadBusinessSystems();
  });
</script>

<style lang="scss" scoped>
  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 20px 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .field-with-checkbox {
    margin-bottom: 20px;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    .form-label-with-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;

      .label-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }
    }
  }
</style>
