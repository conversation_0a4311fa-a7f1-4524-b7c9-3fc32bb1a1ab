<template>
  <slot name="icon" :item="item" :icon="item.icon">
    <ElIcon v-if="item.icon" v-bind="item.iconProps || {}">
      <component :is="item.icon" :style="item.iconStyle" />
    </ElIcon>
  </slot>
  <slot v-if="showTitle" name="title" :item="item" :title="item.title">
    <span v-if="item.title" class="ele-menu-title">{{ item.title }}</span>
  </slot>
  <ElBadge v-if="item.badge != null" v-bind="item.badge" />
  <div
    class="ele-menu-trigger"
    @click="(e) => handleItemClick(item, e)"
    @mouseenter="(e) => handleItemMouseenter(item, e)"
    @mouseleave="(e) => handleItemMouseleave(item, e)"
  >
    <template v-if="showLink">
      <a
        v-if="isExternalLink(item.path)"
        :href="item.path"
        :target="item.pathTarget || '_blank'"
        class="ele-menu-link"
        @click.stop=""
      ></a>
      <RouterLink
        v-else-if="item.path"
        :to="item.path"
        :target="item.pathTarget"
        class="ele-menu-link"
      />
    </template>
  </div>
</template>

<script setup>
  import { ElIcon, ElBadge } from 'element-plus';
  import { isExternalLink } from '../../utils/common';

  defineOptions({ name: 'ItemTitle' });

  defineProps({
    /** 菜单项数据 */
    item: {
      type: Object,
      required: true
    },
    /** 是否需要标题 */
    showTitle: {
      type: Boolean,
      default: true
    },
    /** 是否需要链接 */
    showLink: Boolean
  });

  const emit = defineEmits({
    itemClick: (_item, _e) => true,
    itemMouseenter: (_item, _e) => true,
    itemMouseleave: (_item, _e) => true
  });

  /** 菜单项点击事件 */
  const handleItemClick = (item, e) => {
    emit('itemClick', item, e);
  };

  /** 菜单项鼠标进入事件 */
  const handleItemMouseenter = (item, e) => {
    emit('itemMouseenter', item, e);
  };

  /** 菜单项鼠标离开事件 */
  const handleItemMouseleave = (item, e) => {
    emit('itemMouseleave', item, e);
  };
</script>
