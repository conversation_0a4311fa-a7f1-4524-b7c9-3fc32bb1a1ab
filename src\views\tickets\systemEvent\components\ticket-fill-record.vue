<template>
  <el-drawer
    :model-value="visible"
    title="填写记录"
    :size="1000"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <div class="ticket-info">
        <h4>工单信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单ID">
            {{ ticketData?.ticket_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单类型">
            系统事件
          </el-descriptions-item>
          <el-descriptions-item label="工单等级">
            <el-tag
              v-if="ticketData?.level"
              :type="getLevelType(ticketData.level)"
              size="small"
            >
              {{ ticketData.level }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="工单状态">
            <el-tag
              v-if="ticketData?.ticket_status !== undefined"
              :type="getStatusType(ticketData.ticket_status)"
              size="small"
            >
              {{ getStatusText(ticketData.ticket_status) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="工单内容" span="2">
            {{ ticketData?.comment || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>

        <!-- 第一行：事件标题、记录人 -->
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="事件标题" prop="event_name">
              <el-input
                v-model="form.event_name"
                placeholder="请输入事件标题"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="记录人" prop="recorder">
              <el-input
                v-model="form.recorder"
                placeholder="记录人"
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：事件描述（单独一行） -->
        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="事件描述" prop="event_desc">
              <el-input
                v-model="form.event_desc"
                type="textarea"
                :rows="3"
                placeholder="请输入事件描述"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：发生时间、事件来源 -->
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="发生时间" prop="occurrence_time">
              <el-date-picker
                v-model="form.occurrence_time"
                type="date"
                placeholder="选择发生时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleOccurrenceTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件来源" prop="event_source">
              <el-input
                v-model="form.event_source"
                placeholder="请输入事件来源"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：事件级别、影响范围 -->
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="事件级别" prop="event_level">
              <el-select
                v-model="form.event_level"
                placeholder="请选择事件级别"
                style="width: 100%"
              >
                <el-option label="I级" value="I" />
                <el-option label="II级" value="II" />
                <el-option label="III级" value="III" />
                <el-option label="IV级" value="IV" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="影响范围" prop="impact_scope">
              <el-input
                v-model="form.impact_scope"
                placeholder="请输入影响范围"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第五行：影响业务、事件截图 -->
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="影响业务" prop="affected_business">
              <el-input
                v-model="form.affected_business"
                placeholder="请输入影响业务"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件截图" prop="event_screenshot">
              <div class="screenshot-upload-container disabled-field">
                <el-upload
                  disabled
                  list-type="picture-card"
                  :limit="5"
                  class="disabled-upload"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip disabled-tip">
                      功能优化中，预计周五开放使用
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 故障信息 -->
        <div class="form-section-title">故障信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="故障类别" prop="fault_category">
              <el-select
                v-model="form.fault_category"
                placeholder="请选择故障类别"
                style="width: 100%"
                @change="handleFaultCategoryChange"
              >
                <el-option label="应用服务故障" value="应用服务故障" />
                <el-option label="上线/变更引发" value="上线/变更引发" />
                <el-option label="应用设计缺陷" value="应用设计缺陷" />
                <el-option label="业务需求缺陷" value="业务需求缺陷3" />
                <el-option label="外部系统异常" value="外部系统异常" />
                <el-option label="数据库故障" value="数据库故障" />
                <el-option label="基础网络故障" value="基础网络故障" />
                <el-option label="基础硬件故障" value="基础硬件故障" />
                <el-option label="中间件故障" value="中间件故障" />
                <el-option label="运营商线路异常" value="运营商线路异常" />
                <el-option label="产品/政策配置" value="政策配置" />
                <el-option label="操作/咨询类" value="操作/咨询类" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="根因故障系统" prop="root_cause_fault_system">
              <el-select
                v-model="form.root_cause_fault_system"
                placeholder="请选择根因故障系统"
                style="width: 100%"
                @change="handleSystemChange"
                filterable
                :loading="systemLoading"
              >
                <el-option
                  v-for="system in businessSystems"
                  :key="system.value"
                  :label="system.label"
                  :value="system.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 变更相关字段 -->
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="引发故障的变更单号"
              prop="change_order_no_causing_fault"
            >
              <el-input
                v-model="form.change_order_no_causing_fault"
                placeholder="请输入变更单号"
                clearable
                :maxlength="100"
                :readonly="form.fault_category !== '1'"
                :disabled="form.fault_category !== '1'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="变更申请人" prop="change_requester">
              <el-input
                v-model="form.change_requester"
                placeholder="请输入变更申请人"
                clearable
                :maxlength="50"
                :readonly="form.fault_category !== '1'"
                :disabled="form.fault_category !== '1'"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="开发供应商" prop="development_supplier">
              <el-input
                v-model="form.development_supplier"
                placeholder="请输入开发供应商"
                clearable
                :maxlength="100"
                :readonly="form.fault_category !== '1'"
                :disabled="form.fault_category !== '1'"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 处理信息 -->

        <el-form-item label="事件原因" prop="event_cause">
          <el-input
            v-model="form.event_cause"
            type="textarea"
            :rows="3"
            placeholder="请输入事件原因"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 处理信息 -->
        <div class="form-section-title">处理信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="科创处置人员" prop="dev_handler">
              <el-input
                v-model="form.dev_handler"
                placeholder="请输入科创处置人员"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运维处置人员" prop="ops_handler">
              <el-input
                v-model="form.ops_handler"
                placeholder="请输入运维处置人员"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="事件处理过程" prop="event_handling_process">
          <el-input
            v-model="form.event_handling_process"
            type="textarea"
            :rows="3"
            placeholder="请输入事件处理过程"
            :maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item
          label="事件处理结果和依据"
          prop="event_handling_results_and_basis"
        >
          <div class="results-container disabled-field">
            <el-input
              v-model="form.event_handling_results_and_basis"
              type="textarea"
              :rows="4"
              placeholder="功能优化中，预计周五开放使用"
              :maxlength="1000"
              show-word-limit
              class="results-textarea"
              disabled
            />
            <div class="results-upload-section">
              <div class="results-upload-label">相关截图：</div>
              <el-upload
                disabled
                list-type="picture-card"
                :limit="3"
                class="results-upload disabled-upload"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip disabled-tip">
                    功能优化中，预计周五开放使用
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="解决时间" prop="resolution_time">
              <el-date-picker
                v-model="form.resolution_time"
                type="datetime"
                placeholder="选择解决时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="涉及生产变更"
              prop="solution_and_whether_production_change_is_involved"
            >
              <el-radio-group
                v-model="
                  form.solution_and_whether_production_change_is_involved
                "
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="涉及OA流程"
              prop="solution_and_whether_oa_process_is_involved"
            >
              <el-radio-group
                v-model="form.solution_and_whether_oa_process_is_involved"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="是否进行复盘"
              prop="whether_review_is_conducted"
            >
              <el-radio-group
                v-model="form.whether_review_is_conducted"
                @change="handleReviewChange"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 复盘相关字段 -->
        <el-row :gutter="16" v-if="form.whether_review_is_conducted === '是'">
          <el-col :span="12">
            <el-form-item label="复盘会召开日期" prop="review_meeting_date">
              <el-date-picker
                v-model="form.review_meeting_date"
                type="date"
                placeholder="选择复盘会召开日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="是否形成事件报告"
              prop="whether_event_report_is_formed"
            >
              <el-radio-group
                v-model="form.whether_event_report_is_formed"
                @change="handleEventReportChange"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 事件报告上传 -->
        <el-form-item
          v-if="form.whether_event_report_is_formed === '是'"
          label="事件报告"
          prop="event_report"
        >
          <div class="disabled-field">
            <el-upload
              ref="reportUploadRef"
              v-model:file-list="reportFileList"
              :action="uploadAction"
              :headers="uploadHeaders"
              :before-upload="beforeReportUpload"
              :on-success="handleReportSuccess"
              :on-remove="handleReportRemove"
              accept=".pdf,.doc,.docx,.txt"
              :limit="1"
              disabled
              class="disabled-upload"
            >
              <el-button type="primary" disabled>上传事件报告</el-button>
              <template #tip>
                <div class="el-upload__tip disabled-tip">
                  功能优化中，预计周五开放使用
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 工单操作相关字段 -->
        <div class="form-section-title">工单操作</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="操作类型">
              <el-radio-group
                v-model="showTransferHandler"
                @change="handleOperationTypeChange"
              >
                <el-radio :value="false">完成处理</el-radio>
                <el-radio :value="true">转派他人</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="showTransferHandler">
            <el-form-item label="执行人" prop="nextHandler">
              <el-select
                v-model="form.nextHandlerId"
                placeholder="请选择执行人"
                style="width: 100%"
                filterable
                :loading="userLoading"
                @change="handleNextHandlerChange"
              >
                <el-option
                  v-for="user in userOptions"
                  :key="user.value"
                  :label="user.label"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ showTransferHandler ? '转派' : '提交审核' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue';
  import { Plus } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';
  import { useUserStore } from '@/store/modules/user';
  import { transferTicket } from '@/api/ticket';
  import { pageUsers } from '@/api/system/user';
  import { searchBusiness } from '@/api/cmdb';

  defineOptions({ name: 'TicketFillRecord' });

  const props = defineProps({
    modelValue: Boolean,
    ticketData: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const userStore = useUserStore();

  const visible = computed(() => props.modelValue);

  const formRef = ref(null);
  const uploadRef = ref(null);
  const loading = ref(false);
  const userLoading = ref(false);

  const userOptions = ref([]);
  const businessSystems = ref([]);
  const systemLoading = ref(false);
  const screenshotFileList = ref([]);
  const resultsFileList = ref([]);
  const reportFileList = ref([]);
  const showTransferHandler = ref(false);

  // 上传配置
  const uploadAction = ref('/api/upload');
  const uploadHeaders = computed(() => {
    return {
      Authorization: 'Bearer ' + userStore.token
    };
  });

  const form = ref({
    event_name: '',
    recorder: '',
    event_desc: '',
    occurrence_time: '',
    event_source: '',
    event_level: '',
    impact_scope: '',
    affected_business: '',
    event_screenshot: '',
    fault_category: '0',
    root_cause_fault_system: '',
    system_level: '',
    event_cause: '',
    dev_handler: '',
    ops_handler: '',
    event_handling_process: '',
    event_handling_results_and_basis: '',
    resolution_time: '',
    solution_and_whether_production_change_is_involved: '',
    solution_and_whether_oa_process_is_involved: '',
    change_order_no_causing_fault: '',
    change_requester: '',
    development_supplier: '',
    whether_review_is_conducted: '',
    review_meeting_date: '',
    whether_event_report_is_formed: '',
    event_report: '',
    remarks: '',
    nextHandlerId: null,
    nextHandlerName: ''
  });

  const rules = computed(() => {
    const baseRules = {};

    // 获取当前工单的follow_field来判断哪些字段必填
    const followFields = getFollowFields();

    // 根据follow_field动态添加必填规则
    if (followFields.includes('event_name')) {
      baseRules.event_name = [
        { required: true, message: '请输入事件标题', trigger: 'blur' }
      ];
    }

    if (followFields.includes('recorder')) {
      baseRules.recorder = [
        { required: true, message: '请输入记录人', trigger: 'blur' }
      ];
    }

    if (followFields.includes('occurrence_time')) {
      baseRules.occurrence_time = [
        { required: true, message: '请选择发生时间', trigger: 'change' }
      ];
    }

    if (followFields.includes('event_source')) {
      baseRules.event_source = [
        { required: true, message: '请输入事件来源', trigger: 'blur' }
      ];
    }

    if (followFields.includes('event_level')) {
      baseRules.event_level = [
        { required: true, message: '请选择事件级别', trigger: 'change' }
      ];
    }

    if (followFields.includes('impact_scope')) {
      baseRules.impact_scope = [
        { required: true, message: '请输入影响范围', trigger: 'blur' }
      ];
    }

    if (followFields.includes('affected_business')) {
      baseRules.affected_business = [
        { required: true, message: '请输入影响业务', trigger: 'blur' }
      ];
    }

    // if (followFields.includes('event_screenshot')) {
    //   baseRules.event_screenshot = [
    //     { required: true, message: '请上传事件截图', trigger: 'blur' }
    //   ];
    // }

    if (followFields.includes('event_desc')) {
      baseRules.event_desc = [
        { required: true, message: '请输入事件描述', trigger: 'blur' }
      ];
    }

    if (followFields.includes('fault_category')) {
      baseRules.fault_category = [
        { required: true, message: '请选择故障类别', trigger: 'change' }
      ];
    }

    if (followFields.includes('root_cause_fault_system')) {
      baseRules.root_cause_fault_system = [
        { required: true, message: '请选择根因故障系统', trigger: 'change' }
      ];
    }

    if (followFields.includes('event_cause')) {
      baseRules.event_cause = [
        { required: true, message: '请输入事件原因', trigger: 'blur' }
      ];
    }

    if (followFields.includes('ops_handler')) {
      baseRules.ops_handler = [
        { required: true, message: '请输入运维处置人员', trigger: 'blur' }
      ];
    }

    if (followFields.includes('event_handling_process')) {
      baseRules.event_handling_process = [
        { required: true, message: '请输入事件处理过程', trigger: 'blur' }
      ];
    }

    // if (followFields.includes('event_handling_results_and_basis')) {
    //   baseRules.event_handling_results_and_basis = [
    //     { required: true, message: '请输入事件处理结果和依据', trigger: 'blur' }
    //   ];
    // }

    if (followFields.includes('resolution_time')) {
      baseRules.resolution_time = [
        { required: true, message: '请选择解决时间', trigger: 'change' }
      ];
    }

    if (
      followFields.includes(
        'solution_and_whether_production_change_is_involved'
      )
    ) {
      baseRules.solution_and_whether_production_change_is_involved = [
        { required: true, message: '请选择是否涉及生产变更', trigger: 'change' }
      ];
    }

    if (followFields.includes('whether_review_is_conducted')) {
      baseRules.whether_review_is_conducted = [
        { required: true, message: '请选择是否进行复盘', trigger: 'change' }
      ];
    }

    // 动态添加规则
    if (form.value.fault_category === '1') {
      baseRules.change_order_no_causing_fault = [
        { required: true, message: '请输入引发故障的变更单号', trigger: 'blur' }
      ];
      baseRules.change_requester = [
        { required: true, message: '请输入变更申请人', trigger: 'blur' }
      ];
      baseRules.development_supplier = [
        { required: true, message: '请输入开发供应商', trigger: 'blur' }
      ];
    }

    if (form.value.whether_review_is_conducted === '是') {
      baseRules.review_meeting_date = [
        { required: true, message: '请选择复盘会召开日期', trigger: 'change' }
      ];
    }

    if (form.value.whether_event_report_is_formed === '是') {
      baseRules.event_report = [
        { required: true, message: '请上传事件报告', trigger: 'blur' }
      ];
    }

    if (showTransferHandler.value) {
      baseRules.nextHandlerId = [
        { required: true, message: '请选择执行人', trigger: 'change' }
      ];
    }

    return baseRules;
  });

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  // 获取当前工单的follow_field
  const getFollowFields = () => {
    if (
      props.ticketData?.transferHistory &&
      props.ticketData.transferHistory.length > 0
    ) {
      const latestHistory =
        props.ticketData.transferHistory[
          props.ticketData.transferHistory.length - 1
        ];
      return latestHistory.follow_field || [];
    }
    return [];
  };

  const handleClosed = () => {
    resetFields();
  };

  const resetFields = () => {
    formRef.value?.resetFields();
    form.value = {
      event_name: '',
      recorder: userStore.info.nickName || userStore.info.userName,
      event_desc: '',
      occurrence_time: '',
      event_source: '',
      event_level: '',
      impact_scope: '',
      affected_business: '',
      event_screenshot: '',
      fault_category: '0',
      root_cause_fault_system: '',
      system_level: '',
      event_cause: '',
      dev_handler: '',
      ops_handler: '',
      event_handling_process: '',
      event_handling_results_and_basis: '',
      resolution_time: '',
      solution_and_whether_production_change_is_involved: '',
      solution_and_whether_oa_process_is_involved: '',
      change_order_no_causing_fault: '',
      change_requester: '',
      development_supplier: '',
      whether_review_is_conducted: '',
      review_meeting_date: '',
      whether_event_report_is_formed: '',
      event_report: '',
      remarks: '',
      nextHandlerId: null,
      nextHandlerName: ''
    };
    screenshotFileList.value = [];
    resultsFileList.value = [];
    reportFileList.value = [];
    businessSystems.value = [];
    userOptions.value = [];
  };

  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const systemEventData = {
          ...form.value,
          recorder: userStore.info.nickName || userStore.info.userName,
          bk_inst_name:
            (form.value.event_name || '系统事件') +
            (form.value.occurrence_time ? '_' + form.value.occurrence_time : '')
        };

        const followFields = [
          'event_name',
          'occurrence_time',
          'event_source',
          'event_level',
          'affected_business',
          'event_screenshot',
          'fault_category',
          'root_cause_fault_system',
          'event_cause',
          'ops_handler',
          'event_handling_process',
          'event_handling_results_and_basis',
          'resolution_time',
          'solution_and_whether_production_change_is_involved',
          'whether_review_is_conducted'
        ];

        if (showTransferHandler.value) {
          // 转派他人：选择执行人，继续转派给其他用户，非第一个分派人
          await transferTicket({
            follow_field: followFields,
            executor_user_name: form.value.nextHandlerName,
            executor_user_id: form.value.nextHandlerId,
            comment: `填写记录并转派：${form.value.event_handling_process || '已完成处理'}`,
            ticket_id: props.ticketData?.ticket_id,
            system_item: systemEventData
          });
        } else {
          // 完成处理：逻辑是转派为第一个分派人
          const firstAssignorId = props.ticketData?.create_user_id;
          const firstAssignorName = props.ticketData?.create_user_name;

          if (firstAssignorId && firstAssignorName) {
            await transferTicket({
              follow_field: followFields,
              executor_user_name: firstAssignorName,
              executor_user_id: firstAssignorId,
              comment: `填写记录并提交审核：${form.value.event_handling_process || '已完成处理'}`,
              ticket_id: props.ticketData?.ticket_id,
              system_item: systemEventData
            });
          }
        }

        // TODO: 调用保存系统事件记录的API接口
        // await saveSystemEventRecord({
        //   ticket_id: props.ticketData?.ticket_id,
        //   system_event_data: systemEventData
        // });

        EleMessage.success(
          showTransferHandler.value ? '转派成功' : '提交审核成功'
        );
        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(
          e.message || (showTransferHandler.value ? '转派失败' : '提交审核失败')
        );
      }
      loading.value = false;
    });
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  /** 发生时间变化处理 */
  const handleOccurrenceTimeChange = (value) => {
    // 可以在这里添加发生月份的自动生成逻辑
  };

  /** 故障类别变化处理 */
  const handleFaultCategoryChange = (value) => {
    if (value !== '1') {
      // 清空变更相关字段
      form.value.change_order_no_causing_fault = '';
      form.value.change_requester = '';
      form.value.development_supplier = '';
    }
    // 重新验证表单
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  /** 根因故障系统变化处理 */
  const handleSystemChange = (value) => {
    const system = businessSystems.value.find((s) => s.value === value);
    if (system) {
      form.value.system_level = system.system_level || '';
    }
  };

  /** 加载根因故障系统数据 */
  const loadBusinessSystems = async () => {
    systemLoading.value = true;
    try {
      // 使用searchBusiness接口获取所有业务系统，不传keyword参数获取全部数据
      const res = await searchBusiness({});

      businessSystems.value = res.rows.map((item) => ({
        label: item.bk_inst_name,
        value: item.bk_biz_name, // 使用bk_biz_name作为值
        system_level: item.system_level || '' // 保存system_level用于自动填充
      }));
    } catch (e) {
      console.error('加载业务系统失败:', e);
    }
    systemLoading.value = false;
  };

  /** 复盘选择变化 */
  const handleReviewChange = (value) => {
    if (value !== '是') {
      form.value.review_meeting_date = '';
      form.value.whether_event_report_is_formed = '';
      form.value.event_report = '';
    }
    // 重新验证表单
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  /** 事件报告选择变化 */
  const handleEventReportChange = (value) => {
    if (value !== '是') {
      form.value.event_report = '';
      reportFileList.value = [];
    }
    // 重新验证表单
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  /** 截图上传前校验 */
  const beforeScreenshotUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isImage) {
      EleMessage.error('只能上传图片文件!');
      return false;
    }
    if (!isLt10M) {
      EleMessage.error('上传图片大小不能超过 10MB!');
      return false;
    }
    return true;
  };

  /** 截图上传成功 */
  const handleScreenshotSuccess = (response) => {
    if (response.code === 200) {
      const urls = screenshotFileList.value
        .map((f) => f.url || f.response?.data?.url)
        .filter(Boolean);
      form.value.event_screenshot = urls.join(',');
    }
  };

  /** 截图删除 */
  const handleScreenshotRemove = () => {
    const urls = screenshotFileList.value
      .map((f) => f.url || f.response?.data?.url)
      .filter(Boolean);
    form.value.event_screenshot = urls.join(',');
  };

  /** 报告上传前校验 */
  const beforeReportUpload = (file) => {
    const isValidType = ['.pdf', '.doc', '.docx', '.txt'].some((ext) =>
      file.name.toLowerCase().endsWith(ext)
    );
    const isLt50M = file.size / 1024 / 1024 < 50;

    if (!isValidType) {
      EleMessage.error('只能上传 pdf/doc/docx/txt 格式文件!');
      return false;
    }
    if (!isLt50M) {
      EleMessage.error('上传文件大小不能超过 50MB!');
      return false;
    }
    return true;
  };

  /** 报告上传成功 */
  const handleReportSuccess = (response) => {
    if (response.code === 200) {
      form.value.event_report = response.data?.url || '';
    }
  };

  /** 报告删除 */
  const handleReportRemove = () => {
    form.value.event_report = '';
  };

  // 处理事件截图粘贴上传
  const handleScreenshotPaste = async (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file && screenshotFileList.value.length < 5) {
          // 创建一个临时的文件名
          const fileName = `screenshot_${Date.now()}.png`;
          const newFile = new File([file], fileName, { type: file.type });

          // 手动触发上传
          await uploadFileManually(newFile, 'screenshot');
        }
        break;
      }
    }
  };

  // 处理处理结果截图粘贴上传
  const handleResultsPaste = async (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file && resultsFileList.value.length < 3) {
          // 创建一个临时的文件名
          const fileName = `results_${Date.now()}.png`;
          const newFile = new File([file], fileName, { type: file.type });

          // 手动触发上传
          await uploadFileManually(newFile, 'results');
        }
        break;
      }
    }
  };

  // 手动上传文件的通用方法
  const uploadFileManually = async (file, type) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(uploadAction.value, {
        method: 'POST',
        headers: uploadHeaders.value,
        body: formData
      });

      const result = await response.json();

      if (result.code === 200) {
        const fileObj = {
          name: file.name,
          url: result.data?.url || result.url,
          uid: Date.now()
        };

        if (type === 'screenshot') {
          screenshotFileList.value.push(fileObj);
          updateScreenshotValue();
        } else if (type === 'results') {
          resultsFileList.value.push(fileObj);
          updateResultsValue();
        }

        EleMessage.success('截图上传成功');
      } else {
        EleMessage.error('截图上传失败');
      }
    } catch (error) {
      console.error('Upload error:', error);
      EleMessage.error('截图上传失败');
    }
  };

  // 更新事件截图字段值
  const updateScreenshotValue = () => {
    form.value.event_screenshot = screenshotFileList.value
      .map((file) => file.url)
      .join(',');
  };

  // 更新处理结果截图字段值
  const updateResultsValue = () => {
    // 可以将截图URL添加到处理结果文本中，或者创建专门的字段
    const imageUrls = resultsFileList.value.map((file) => file.url);
    if (imageUrls.length > 0) {
      const imageText = `\n\n相关截图：\n${imageUrls.join('\n')}`;
      if (!form.value.event_handling_results_and_basis.includes('相关截图：')) {
        form.value.event_handling_results_and_basis += imageText;
      }
    }
  };

  // 处理结果截图上传前验证
  const beforeResultsUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    const isLt50M = file.size / 1024 / 1024 < 50;

    if (!isImage) {
      EleMessage.error('只能上传图片文件!');
      return false;
    }
    if (!isLt50M) {
      EleMessage.error('图片大小不能超过 50MB!');
      return false;
    }
    return true;
  };

  // 处理结果截图上传成功
  const handleResultsSuccess = (response) => {
    if (response.code === 200) {
      updateResultsValue();
    }
  };

  // 处理结果截图删除
  const handleResultsRemove = (file) => {
    // 从处理结果文本中移除对应的图片URL
    if (
      file.url &&
      form.value.event_handling_results_and_basis.includes(file.url)
    ) {
      form.value.event_handling_results_and_basis =
        form.value.event_handling_results_and_basis
          .replace(file.url, '')
          .replace(/\n\n相关截图：\n$/, ''); // 如果是最后一张图，清理标题
    }
  };

  const searchUsers = async (query) => {
    userLoading.value = true;
    try {
      const res = await pageUsers({
        userName: query || '',
        pageNum: 1,
        pageSize: 100 // 增加页面大小以获取更多用户
      });
      userOptions.value =
        res.rows?.map((user) => ({
          label: user.nickName || user.userName,
          value: user.userId
        })) || [];
    } catch (e) {
      console.error('搜索用户失败:', e);
    }
    userLoading.value = false;
  };

  /** 加载所有用户数据 */
  const loadUsers = async () => {
    userLoading.value = true;
    try {
      const res = await pageUsers({
        pageNum: 1,
        pageSize: 100 // 获取前100个用户，可根据需要调整
      });
      userOptions.value =
        res.rows?.map((user) => ({
          label: user.nickName || user.userName,
          value: user.userId
        })) || [];
    } catch (e) {
      console.error('加载用户失败:', e);
    }
    userLoading.value = false;
  };

  /** 操作类型变化处理 */
  const handleOperationTypeChange = (value) => {
    if (value) {
      // 选择转派他人时，加载用户数据
      loadUsers();
    } else {
      // 选择完成处理时，清空执行人选择
      form.value.nextHandlerId = null;
      form.value.nextHandlerName = '';
    }
  };

  const handleNextHandlerChange = (value) => {
    const user = userOptions.value.find((u) => u.value === value);
    if (user) {
      form.value.nextHandlerName = user.label;
    }
  };

  const getLevelType = (level) => {
    const levelTypes = {
      p1: 'danger',
      p2: 'warning',
      p3: 'primary',
      p4: 'info'
    };
    return levelTypes[level] || 'info';
  };

  const getStatusType = (status) => {
    const statusTypes = {
      0: 'warning',
      1: 'success',
      2: 'danger'
    };
    return statusTypes[status] || 'info';
  };

  const getStatusText = (status) => {
    const statusTexts = {
      0: '待处理',
      1: '已完成',
      2: '已关闭'
    };
    return statusTexts[status] || '未知';
  };

  watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        // 加载根因故障系统数据
        loadBusinessSystems();
        // 如果有票据数据，填充表单
        if (props.ticketData?.system_item) {
          Object.assign(form.value, props.ticketData.system_item);

          // 处理recorder字段 - 如果原始值存在，则拼接当前登录用户
          const currentUser =
            userStore.info.nickName || userStore.info.userName;
          const originalRecorder = props.ticketData.system_item.recorder || '';
          if (originalRecorder && !originalRecorder.includes(currentUser)) {
            form.value.recorder = originalRecorder + ',' + currentUser;
          } else if (!originalRecorder) {
            form.value.recorder = currentUser;
          }

          // 处理文件列表回显
          if (props.ticketData.system_item.event_screenshot) {
            screenshotFileList.value =
              props.ticketData.system_item.event_screenshot
                .split(',')
                .map((url, index) => ({
                  name: `screenshot_${index + 1}`,
                  url: url
                }));
          }
          if (props.ticketData.system_item.event_report) {
            reportFileList.value = [
              {
                name: 'event_report',
                url: props.ticketData.system_item.event_report
              }
            ];
          }
        } else {
          // 如果没有system_item数据，只设置recorder为当前用户
          form.value.recorder =
            userStore.info.nickName || userStore.info.userName;
        }
      } else {
        resetFields();
      }
    }
  );
</script>

<style scoped>
  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }

  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .ticket-info {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .ticket-info h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .screenshot-upload-container {
    position: relative;
  }

  .results-container {
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;
  }

  .results-textarea {
    margin-bottom: 16px;
  }

  .results-upload-section {
    position: relative;
  }

  .results-upload-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .results-upload .el-upload--picture-card {
    width: 80px;
    height: 80px;
  }

  .results-upload .el-upload-list--picture-card .el-upload-list__item {
    width: 80px;
    height: 80px;
  }

  .disabled-field {
    position: relative;
    opacity: 0.6;
  }

  .disabled-upload {
    pointer-events: none;
  }

  .disabled-tip {
    color: #909399;
    font-size: 12px;
  }
</style>
