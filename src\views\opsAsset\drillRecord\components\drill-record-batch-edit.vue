<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑演练记录"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="system_name">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.system_name" />
                    <span class="label-text">系统名称</span>
                  </div>
                </template>
                <el-select
                  v-model="form.system_name"
                  placeholder="请选择系统名称"
                  style="width: 100%"
                  :disabled="!fieldEnabled.system_name"
                  filterable
                  :loading="systemLoading"
                >
                  <el-option
                    v-for="system in businessSystems"
                    :key="system.value"
                    :label="system.label"
                    :value="system.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="drill_date">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.drill_date" />
                    <span class="label-text">演练日期</span>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.drill_date"
                  type="date"
                  placeholder="选择演练日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled="!fieldEnabled.drill_date"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="affiliated_cycle">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.affiliated_cycle" />
                    <span class="label-text">所属周期</span>
                  </div>
                </template>
                <el-select
                  v-model="form.affiliated_cycle"
                  placeholder="请选择所属周期"
                  style="width: 100%"
                  :disabled="!fieldEnabled.affiliated_cycle"
                >
                  <el-option
                    v-for="cycle in cycleOptions"
                    :key="cycle.value"
                    :label="cycle.label"
                    :value="cycle.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="drill_scenario">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.drill_scenario" />
                    <span class="label-text">演练场景</span>
                  </div>
                </template>
                <el-select
                  v-model="form.drill_scenario"
                  placeholder="请选择演练场景"
                  style="width: 100%"
                  :disabled="!fieldEnabled.drill_scenario"
                >
                  <el-option label="故障恢复演练" value="故障恢复演练" />
                  <el-option label="安全防护演练" value="安全防护演练" />
                  <el-option label="性能压力演练" value="性能压力演练" />
                  <el-option label="灾备切换演练" value="灾备切换演练" />
                  <el-option label="系统升级演练" value="系统升级演练" />
                  <el-option label="其他场景" value="其他场景" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="pic">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.pic" />
                    <span class="label-text">负责人</span>
                  </div>
                </template>
                <el-input
                  v-model="form.pic"
                  placeholder="请输入负责人"
                  clearable
                  :maxlength="50"
                  :disabled="!fieldEnabled.pic"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="result">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.result" />
                    <span class="label-text">结果</span>
                  </div>
                </template>
                <el-select
                  v-model="form.result"
                  placeholder="请选择结果"
                  style="width: 100%"
                  :disabled="!fieldEnabled.result"
                >
                  <el-option label="成功" value="成功" />
                  <el-option label="部分成功" value="部分成功" />
                  <el-option label="失败" value="失败" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <div class="field-with-checkbox">
          <el-form-item prop="next_drill">
            <template #label>
              <div class="form-label-with-checkbox">
                <el-checkbox v-model="fieldEnabled.next_drill" />
                <span class="label-text">下次演练</span>
              </div>
            </template>
            <el-input
              v-model="form.next_drill"
              placeholder="请输入下次演练计划"
              clearable
              :maxlength="200"
              :disabled="!fieldEnabled.next_drill"
            />
          </el-form-item>
        </div>

        <div class="field-with-checkbox">
          <el-form-item prop="remark">
            <template #label>
              <div class="form-label-with-checkbox">
                <el-checkbox v-model="fieldEnabled.remark" />
                <span class="label-text">备注</span>
              </div>
            </template>
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              :maxlength="500"
              show-word-limit
              :disabled="!fieldEnabled.remark"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="updateVisible(false)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, reactive, onMounted, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst, searchBusiness } from '@/api/cmdb';

  defineOptions({ name: 'DrillRecordBatchEdit' });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 选中的记录 */
  const selectedRecords = defineModel('selectedRecords', {
    type: Array,
    default: () => []
  });

  /** 模型实例ID */
  const bkObjId = 'drill_record';

  /** 表单实例 */
  const formRef = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 业务系统列表 */
  const businessSystems = ref([]);

  /** 业务系统搜索loading */
  const systemLoading = ref(false);

  /** 获取当前日期计算周期选项 */
  const getCycleOptions = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;

    let options = [];

    if (month >= 7) {
      options = [
        {
          label: `${year}年下半年（7-12月）`,
          value: `${year}年下半年（7-12月）`
        },
        {
          label: `${year + 1}年上半年（1-6月）`,
          value: `${year + 1}年上半年（1-6月）`
        },
        {
          label: `${year + 1}年下半年（7-12月）`,
          value: `${year + 1}年下半年（7-12月）`
        }
      ];
    } else {
      options = [
        {
          label: `${year}年上半年（1-6月）`,
          value: `${year}年上半年（1-6月）`
        },
        {
          label: `${year}年下半年（7-12月）`,
          value: `${year}年下半年（7-12月）`
        },
        {
          label: `${year + 1}年上半年（1-6月）`,
          value: `${year + 1}年上半年（1-6月）`
        }
      ];
    }

    return options;
  };

  /** 周期选项 */
  const cycleOptions = ref(getCycleOptions());

  /** 字段启用状态 */
  const fieldEnabled = reactive({
    system_name: false,
    drill_date: false,
    affiliated_cycle: false,
    drill_scenario: false,
    pic: false,
    result: false,
    next_drill: false,
    remark: false
  });

  /** 表单数据 */
  const form = reactive({
    system_name: '',
    drill_date: '',
    affiliated_cycle: '',
    drill_scenario: '',
    pic: '',
    result: '',
    next_drill: '',
    remark: ''
  });

  /** 表单验证规则 */
  const rules = reactive({});

  /** 更新弹窗状态 */
  const updateVisible = (value) => {
    visible.value = value;
  };

  /** 弹窗关闭回调 */
  const handleClosed = () => {
    resetForm();
  };

  /** 重置表单 */
  const resetForm = () => {
    Object.keys(form).forEach((key) => {
      form[key] = '';
    });
    Object.keys(fieldEnabled).forEach((key) => {
      fieldEnabled[key] = false;
    });
    formRef.value?.clearValidate();
  };

  /** 提交表单 */
  const submit = async () => {
    // 检查是否至少启用一个字段
    const hasEnabledField = Object.values(fieldEnabled).some(
      (enabled) => enabled
    );
    if (!hasEnabledField) {
      EleMessage.warning('请至少启用一个字段进行编辑');
      return;
    }

    // 构造要更新的数据，只包含启用的字段
    const instInfoMap = {};
    Object.keys(fieldEnabled).forEach((key) => {
      if (fieldEnabled[key]) {
        instInfoMap[key] = form[key];
      }
    });

    // 如果同时更新了系统名称、演练场景和演练日期，则更新bk_inst_name
    if (
      fieldEnabled.system_name &&
      fieldEnabled.drill_scenario &&
      fieldEnabled.drill_date &&
      form.system_name &&
      form.drill_scenario &&
      form.drill_date
    ) {
      instInfoMap.bk_inst_name = `${form.system_name}_${form.drill_scenario}_${form.drill_date}`;
    }

    // 构造批量编辑参数
    const batchUpdateData = {
      bkObjId,
      bkInstId: selectedRecords.value.map((record) => record.bk_inst_id),
      instInfoMap
    };

    loading.value = true;
    try {
      await batchUpdateInst(batchUpdateData);
      EleMessage.success(`成功批量编辑 ${selectedRecords.value.length} 条记录`);
      updateVisible(false);
      emit('done');
    } catch (error) {
      EleMessage.error(error.message || '批量编辑失败');
    } finally {
      loading.value = false;
    }
  };

  /** 加载业务系统数据 */
  const loadBusinessSystems = async () => {
    systemLoading.value = true;
    try {
      const res = await searchBusiness({});
      businessSystems.value = res.rows.map((item) => ({
        label: item.bk_inst_name,
        value: item.bk_biz_name
      }));
    } catch (e) {
      console.error('加载业务系统失败:', e);
    }
    systemLoading.value = false;
  };

  onMounted(() => {
    // 初始化业务系统列表
    loadBusinessSystems();
  });

  /** 监听弹窗打开状态 */
  watch(
    () => visible.value,
    (newValue) => {
      if (newValue) {
        // 弹窗打开时，确保业务系统数据已加载
        if (businessSystems.value.length === 0) {
          loadBusinessSystems();
        }
      }
    }
  );
</script>

<style lang="scss" scoped>
  .field-with-checkbox {
    margin-bottom: 20px;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    .form-label-with-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;

      .label-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }
    }
  }
</style>
