<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑项目建设厂家"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="is_operation_reminder">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.is_operation_reminder" />
                    <span class="label-text">运维是否提醒</span>
                  </div>
                </template>
                <el-radio-group
                  v-model="form.is_operation_reminder"
                  :disabled="!fieldEnabled.is_operation_reminder"
                >
                  <el-radio value="是">是</el-radio>
                  <el-radio value="否">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="has_maintenance_info_in_contract">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox
                      v-model="fieldEnabled.has_maintenance_info_in_contract"
                    />
                    <span class="label-text">合同是否有维保信息</span>
                  </div>
                </template>
                <el-radio-group
                  v-model="form.has_maintenance_info_in_contract"
                  :disabled="!fieldEnabled.has_maintenance_info_in_contract"
                >
                  <el-radio value="是">是</el-radio>
                  <el-radio value="否">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="free_maintenance_type">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.free_maintenance_type" />
                    <span class="label-text">免维方式</span>
                  </div>
                </template>
                <el-select
                  v-model="form.free_maintenance_type"
                  placeholder="请选择免维方式"
                  style="width: 100%"
                  :disabled="!fieldEnabled.free_maintenance_type"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option label="远程" value="远程" />
                  <el-option label="现场" value="现场" />
                  <el-option label="驻场" value="驻场" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="party_b_company">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.party_b_company" />
                    <span class="label-text">乙方公司</span>
                  </div>
                </template>
                <el-input
                  v-model="form.party_b_company"
                  placeholder="请输入乙方公司"
                  :disabled="!fieldEnabled.party_b_company"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="contract_b_owner">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.contract_b_owner" />
                    <span class="label-text">乙方负责人</span>
                  </div>
                </template>
                <el-input
                  v-model="form.contract_b_owner"
                  placeholder="请输入乙方负责人"
                  :disabled="!fieldEnabled.contract_b_owner"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="contract_b_tel">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.contract_b_tel" />
                    <span class="label-text">乙方负责人联系方式</span>
                  </div>
                </template>
                <el-input
                  v-model="form.contract_b_tel"
                  placeholder="请输入乙方负责人联系方式"
                  :disabled="!fieldEnabled.contract_b_tel"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="app_onwer">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.app_onwer" />
                    <span class="label-text">业务负责人</span>
                  </div>
                </template>
                <el-input
                  v-model="form.app_onwer"
                  placeholder="请输入业务负责人"
                  :disabled="!fieldEnabled.app_onwer"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="app_owner_tel">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.app_owner_tel" />
                    <span class="label-text">业务负责人联系方式</span>
                  </div>
                </template>
                <el-input
                  v-model="form.app_owner_tel"
                  placeholder="请输入业务负责人联系方式"
                  :disabled="!fieldEnabled.app_owner_tel"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="contract_maintenance_fee">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox
                      v-model="fieldEnabled.contract_maintenance_fee"
                    />
                    <span class="label-text">合同约定维保费用</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.contract_maintenance_fee"
                  placeholder="请输入合同约定维保费用"
                  :disabled="!fieldEnabled.contract_maintenance_fee"
                  type="number"
                  clearable
                >
                  <template #append>元</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="maintenance_ratio">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintenance_ratio" />
                    <span class="label-text">维保比例</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.maintenance_ratio"
                  placeholder="请输入维保比例"
                  :disabled="!fieldEnabled.maintenance_ratio"
                  type="number"
                  clearable
                >
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div class="selected-items-info">
        <el-alert
          :title="`已选择 ${selectedRecords.length} 条记录进行批量编辑`"
          type="info"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        保存
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst } from '@/api/cmdb';

  defineOptions({ name: 'ProjectConstructionManufacturerBatchEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 选中的记录 */
    selectedRecords: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits([
    'update:modelValue',
    'update:selectedRecords',
    'done'
  ]);

  /** 模型实例ID */
  const bkObjId = 'project_construction_manufacturer';

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 字段启用状态 */
  const fieldEnabled = ref({
    is_operation_reminder: false,
    has_maintenance_info_in_contract: false,
    free_maintenance_type: false,
    party_b_company: false,
    contract_b_owner: false,
    contract_b_tel: false,
    app_onwer: false,
    app_owner_tel: false,
    contract_maintenance_fee: false,
    maintenance_ratio: false
  });

  /** 表单数据 */
  const form = ref({
    is_operation_reminder: '',
    has_maintenance_info_in_contract: '',
    free_maintenance_type: [],
    party_b_company: '',
    contract_b_owner: '',
    contract_b_tel: '',
    app_onwer: '',
    app_owner_tel: '',
    contract_maintenance_fee: '',
    maintenance_ratio: ''
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {};
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = async () => {
    if (!formRef.value) return;

    // 获取启用的字段数据
    const updateData = {};
    Object.keys(fieldEnabled.value).forEach((field) => {
      if (fieldEnabled.value[field]) {
        let value = form.value[field];
        // 处理免维方式数组转字符串
        if (field === 'free_maintenance_type' && Array.isArray(value)) {
          value = value.join(',');
        }
        updateData[field] = value;
      }
    });

    if (Object.keys(updateData).length === 0) {
      EleMessage.error('请至少选择一个字段进行编辑');
      return;
    }

    loading.value = true;

    try {
      const bkInstId = props.selectedRecords.map((record) => record.bk_inst_id);
      await batchUpdateInst({
        bkObjId,
        bkInstId,
        instInfoMap: {
          ...updateData
        }
      });
      EleMessage.success(`成功批量编辑 ${props.selectedRecords.length} 条记录`);
      updateVisible(false);
      emit('done');
    } catch (error) {
      EleMessage.error(error.message || '批量编辑失败');
    } finally {
      loading.value = false;
    }
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    // 重置字段启用状态
    Object.keys(fieldEnabled.value).forEach((field) => {
      fieldEnabled.value[field] = false;
    });
    // 重置表单数据
    Object.assign(form.value, {
      is_operation_reminder: '',
      has_maintenance_info_in_contract: '',
      free_maintenance_type: [],
      party_b_company: '',
      contract_b_owner: '',
      contract_b_tel: '',
      app_onwer: '',
      app_owner_tel: '',
      contract_maintenance_fee: '',
      maintenance_ratio: ''
    });
  };

  /** 监听弹窗打开状态 */
  watch(
    () => visible.value,
    (newValue) => {
      if (newValue) {
        resetFields();
      }
    }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .field-with-checkbox {
    margin-bottom: 16px;
  }

  .form-label-with-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .label-text {
    user-select: none;
  }

  .selected-items-info {
    margin-top: 20px;
  }

  :deep(.el-form-item__label) {
    padding-right: 0 !important;
  }
</style>
