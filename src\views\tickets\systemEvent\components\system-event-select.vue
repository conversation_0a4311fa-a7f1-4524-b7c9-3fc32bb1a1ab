<template>
  <el-dialog
    :model-value="visible"
    title="选择系统事件"
    width="80%"
    :destroy-on-close="true"
    @update:model-value="updateVisible"
  >
    <div class="event-select-content">
      <div class="search-bar">
        <el-form :model="searchForm" :inline="true">
          <el-form-item label="事件名称">
            <el-input
              v-model="searchForm.event_name"
              placeholder="请输入事件名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="事件级别">
            <el-select
              v-model="searchForm.event_level"
              placeholder="请选择事件级别"
              clearable
              style="width: 120px"
            >
              <el-option label="I级" value="I" />
              <el-option label="II级" value="II" />
              <el-option label="III级" value="III" />
              <el-option label="IV级" value="IV" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        ref="tableRef"
        :data="eventList"
        v-loading="loading"
        border
        stripe
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column
          prop="event_name"
          label="事件名称"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="event_level"
          label="事件级别"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.event_level)" size="small">
              {{ row.event_level }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="occurrence_time" label="发生时间" width="120" />
        <el-table-column
          prop="event_source"
          label="事件来源"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="affected_business"
          label="影响业务"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column prop="recorder" label="记录人" width="100" />
      </el-table>

      <div class="pagination">
        <el-pagination
          :current-page="page.current"
          :page-size="page.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="page.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedEvent"
          @click="handleConfirm"
        >
          确认选择
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { ElMessage } from 'element-plus';
  import { searchInst } from '@/api/cmdb';

  defineOptions({ name: 'SystemEventSelect' });

  const props = defineProps({
    modelValue: Boolean
  });

  const emit = defineEmits(['update:modelValue', 'select']);

  const visible = computed(() => props.modelValue);

  const loading = ref(false);
  const eventList = ref([]);
  const selectedEvent = ref(null);

  const searchForm = ref({
    event_name: '',
    event_level: ''
  });

  const page = ref({
    current: 1,
    size: 20,
    total: 0
  });

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const loadEventList = async () => {
    loading.value = true;
    try {
      const params = {
        bkObjId: 'system_events_test',
        condition: {},
        page: {
          start: (page.value.current - 1) * page.value.size,
          limit: page.value.size
        }
      };

      // 添加搜索条件
      if (searchForm.value.event_name) {
        params.condition.event_name = {
          $regex: searchForm.value.event_name
        };
      }
      if (searchForm.value.event_level) {
        params.condition.event_level = searchForm.value.event_level;
      }

      const res = await searchInst(params);
      eventList.value = res.data?.info || [];
      page.value.total = res.data?.count || 0;
    } catch (e) {
      ElMessage.error(e.message || '获取系统事件列表失败');
    }
    loading.value = false;
  };

  const handleSearch = () => {
    page.value.current = 1;
    loadEventList();
  };

  const handleReset = () => {
    searchForm.value = {
      event_name: '',
      event_level: ''
    };
    page.value.current = 1;
    loadEventList();
  };

  const handleCurrentChange = (current) => {
    selectedEvent.value = current;
  };

  const handleSizeChange = (size) => {
    page.value.size = size;
    page.value.current = 1;
    loadEventList();
  };

  const handlePageChange = (current) => {
    page.value.current = current;
    loadEventList();
  };

  const handleConfirm = () => {
    if (selectedEvent.value) {
      emit('select', selectedEvent.value);
    }
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const getLevelType = (level) => {
    const levelTypes = {
      I: 'danger',
      II: 'warning',
      III: 'primary',
      IV: 'info'
    };
    return levelTypes[level] || 'info';
  };

  onMounted(() => {
    if (visible.value) {
      loadEventList();
    }
  });

  // 监听弹窗打开状态
  watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        selectedEvent.value = null;
        loadEventList();
      }
    }
  );
</script>

<style scoped>
  .event-select-content {
    max-height: 60vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .search-bar {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .pagination {
    margin-top: 16px;
    text-align: right;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
</style>
