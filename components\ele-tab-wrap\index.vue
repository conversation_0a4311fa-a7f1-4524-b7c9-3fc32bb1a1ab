<!-- 标签页自定义容器 -->
<template>
  <div
    :class="[
      'ele-tab-wrap',
      'ele-tabs-wrap',
      { 'is-small': size === 'small' },
      { 'is-large': size === 'large' },
      { 'is-simple': type === 'simple' },
      { 'is-indicator': type === 'indicator' },
      { 'is-button': type === 'button' },
      { 'is-tag': type === 'tag' }
    ]"
  >
    <slot></slot>
  </div>
</template>

<script setup>
  import { reactive, provide, watch, onBeforeUnmount } from 'vue';
  import { TAB_WRAP_KEY } from '../ele-tabs/props';
  import { tabWrapProps } from './props';

  defineOptions({ name: 'EleTabWrap' });

  const props = defineProps(tabWrapProps);

  const tabMethods = {};

  const data = reactive({
    size: props.size,
    type: props.type,
    setTabMethods: (methods) => {
      tabMethods.triggerTabItemClick = methods.triggerTabItemClick;
      tabMethods.triggerItemContextMenu = methods.triggerItemContextMenu;
    },
    triggerTabItemClick: (item, tabName, e) => {
      if (tabMethods.triggerTabItemClick) {
        tabMethods.triggerTabItemClick(item, tabName, e);
      }
    },
    triggerItemContextMenu: (item, tabName, e) => {
      if (tabMethods.triggerItemContextMenu) {
        tabMethods.triggerItemContextMenu(item, tabName, e);
      }
    }
  });

  provide(TAB_WRAP_KEY, data);

  watch(
    () => props.size,
    () => {
      data.size = props.size;
    }
  );

  watch(
    () => props.type,
    () => {
      data.type = props.type;
    }
  );

  onBeforeUnmount(() => {
    tabMethods.triggerTabItemClick = void 0;
    tabMethods.triggerItemContextMenu = void 0;
  });
</script>
