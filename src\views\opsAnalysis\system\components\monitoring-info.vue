<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex items-center">
        <el-icon class="text-primary mr-2"><DataAnalysis /></el-icon>
        <span class="text-lg font-semibold">监控信息</span>
      </div>
    </template>
    <div class="p-2">
      <el-row :gutter="24">
        <el-col :lg="12" :md="24">
          <div>
            <h3 class="text-base font-medium mb-4">技术监控</h3>
            <div class="h-64">
              <div ref="techMonitorChart" class="w-full h-full"></div>
            </div>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div>
            <h3 class="text-base font-medium mb-4">业务监控</h3>
            <div class="h-64">
              <div ref="businessMonitorChart" class="w-full h-full"></div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup>
  import { ref, onMounted, nextTick } from 'vue';
  import * as echarts from 'echarts';
  import { DataAnalysis } from '@element-plus/icons-vue';

  const techMonitorChart = ref(null);
  const businessMonitorChart = ref(null);

  const initCharts = () => {
    nextTick(() => {
      // 技术监控图表 - 饼状图
      if (techMonitorChart.value) {
        const chart1 = echarts.init(techMonitorChart.value);
        const option1 = {
          color: [
            '#165DFF',
            '#36CFC9',
            '#722ED1',
            '#FF7D00',
            '#F53F3F',
            '#00B42A',
            '#86909C'
          ],
          tooltip: { trigger: 'item' },
          legend: { orient: 'vertical', left: 10 },
          series: [
            {
              name: '技术资源类型',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 16,
                  fontWeight: 'bold'
                }
              },
              labelLine: { show: false },
              data: [
                { value: 35, name: '物理机' },
                { value: 45, name: '虚拟机' },
                { value: 20, name: '容器' },
                { value: 30, name: 'Web服务器' },
                { value: 25, name: '应用服务器' },
                { value: 40, name: '关系型数据库' },
                { value: 30, name: 'NoSQL数据库' }
              ]
            }
          ]
        };
        chart1.setOption(option1);

        window.addEventListener('resize', () => {
          chart1.resize();
        });
      }

      // 业务监控图表 - 柱状图
      if (businessMonitorChart.value) {
        const chart2 = echarts.init(businessMonitorChart.value);
        const option2 = {
          color: ['#165DFF'],
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: ['2021', '2022', '2023']
          },
          yAxis: {
            type: 'value',
            name: '数量'
          },
          series: [
            {
              name: '业务交易量',
              type: 'bar',
              data: [1200, 1900, 2500]
            }
          ]
        };
        chart2.setOption(option2);

        window.addEventListener('resize', () => {
          chart2.resize();
        });
      }
    });
  };

  onMounted(() => {
    initCharts();
  });
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }
</style>
