<!-- 主机编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑主机' : '新增主机'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="主机名称" prop="host_name">
              <el-input
                v-model="form.host_name"
                placeholder="请输入主机名称"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内网IP" prop="host_innerip">
              <el-input
                v-model="form.host_innerip"
                placeholder="请输入内网IP"
                clearable
                :maxlength="15"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="系统名称" prop="system_name">
              <el-select
                v-model="form.system_name"
                placeholder="请选择系统名称"
                style="width: 100%"
                filterable
                :loading="businessLoading"
                @change="handleSystemNameChange"
              >
                <el-option
                  v-for="item in businessOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="project_name">
              <el-input
                v-model="form.project_name"
                placeholder="根据系统名称自动带出"
                readonly
                style="background-color: #f5f7fa"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="技术负责人" prop="technician_owner">
              <el-input
                v-model="form.technician_owner"
                placeholder="请输入技术负责人"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技术负责人ID" prop="technician_owner_id">
              <el-input
                v-model="form.technician_owner_id"
                placeholder="请输入技术负责人ID"
                clearable
                :maxlength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="主机角色" prop="device_role">
              <el-select
                v-model="form.device_role"
                placeholder="请选择主机角色"
                style="width: 100%"
              >
                <el-option label="应用" value="0" />
                <el-option label="数据库" value="1" />
                <el-option label="中间件" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备环境" prop="environment_code">
              <el-select
                v-model="form.environment_code"
                placeholder="请选择设备环境"
                style="width: 100%"
              >
                <el-option label="生产" value="0" />
                <el-option label="测试" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="使用状态" prop="lifecycle_code">
              <el-select
                v-model="form.lifecycle_code"
                placeholder="请选择使用状态"
                style="width: 100%"
              >
                <el-option label="已下线" value="0" />
                <el-option label="在线" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监控状态" prop="monitor_status">
              <el-select
                v-model="form.monitor_status"
                placeholder="请选择监控状态"
                style="width: 100%"
              >
                <el-option label="正常" value="0" />
                <el-option label="异常" value="1" />
                <el-option label="未知" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="操作系统版本" prop="os_version">
          <el-input
            v-model="form.os_version"
            type="textarea"
            placeholder="请输入操作系统版本"
            :rows="3"
            :maxlength="500"
          />
        </el-form-item>

        <el-form-item label="备注信息" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
            :maxlength="500"
          />
        </el-form-item>

        <!-- 硬件配置 -->
        <div class="form-section-title">硬件配置</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="数据盘大小" prop="disk">
              <el-input
                v-model.number="form.disk"
                placeholder="请输入数据盘大小(GB)"
                clearable
              >
                <template #append>GB</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="磁盘总大小" prop="total_disks">
              <el-input
                v-model.number="form.total_disks"
                placeholder="请输入磁盘总大小(GB)"
                clearable
              >
                <template #append>GB</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="内存容量" prop="memory">
              <el-input
                v-model.number="form.memory"
                placeholder="请输入内存容量(GB)"
                clearable
              >
                <template #append>GB</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="NAS存储" prop="nas">
              <el-input
                v-model.number="form.nas"
                placeholder="请输入NAS存储(GB)"
                clearable
              >
                <template #append>GB</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="CPU逻辑核心数" prop="cpu">
              <el-input
                v-model.number="form.cpu"
                placeholder="请输入CPU逻辑核心数"
                clearable
              >
                <template #append>核</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系统盘" prop="system_disk">
              <el-input
                v-model.number="form.system_disk"
                placeholder="请输入系统盘大小(GB)"
                clearable
              >
                <template #append>GB</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 申请信息 -->
        <div class="form-section-title">申请信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="申请部门" prop="apply_department">
              <el-input
                v-model="form.apply_department"
                placeholder="请输入申请部门"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期" prop="apply_date">
              <el-date-picker
                v-model="form.apply_date"
                type="date"
                placeholder="选择申请日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="申请人" prop="apply_username">
              <el-input
                v-model="form.apply_username"
                placeholder="请输入申请人"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人工号" prop="apply_user_id">
              <el-input
                v-model="form.apply_user_id"
                placeholder="请输入申请人工号"
                clearable
                :maxlength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="录入时间" prop="create_date">
              <el-date-picker
                v-model="form.create_date"
                type="datetime"
                placeholder="选择录入时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="录入人员" prop="create_user">
              <el-input
                v-model="form.create_user"
                placeholder="自动生成为登录用户"
                readonly
                style="background-color: #f5f7fa"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="updateVisible(false)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { useUserStore } from '@/store/modules/user';
  import { createInst, updateInst } from '@/api/cmdb';
  import { searchBusiness } from '@/api/cmdb';

  const emit = defineEmits(['update:model-value', 'done']);

  const props = defineProps({
    modelValue: Boolean,
    hostData: Object
  });

  const userStore = useUserStore();
  const formRef = ref(null);
  const loading = ref(false);
  const businessLoading = ref(false);
  const businessOptions = ref([]);

  const visible = computed(() => props.modelValue);
  const isUpdate = computed(() => !!props.hostData?.bk_inst_id);

  // 表单数据
  const form = ref({
    host_name: '',
    host_innerip: '',
    device_role: '0',
    disk: null,
    total_disks: null,
    memory: null,
    nas: null,
    cpu: null,
    system_disk: null,
    system_name: '',
    project_name: '',
    apply_department: '',
    apply_date: null,
    apply_username: '',
    apply_user_id: null,
    monitor_status: '0',
    os_version: '',
    environment_code: '0',
    lifecycle_code: '1',
    technician_owner: '',
    technician_owner_id: null,
    create_date: '',
    create_user: '',
    remark: ''
  });

  // 表单验证规则
  const rules = {
    host_name: [{ required: true, message: '请输入主机名称', trigger: 'blur' }],
    host_innerip: [
      { required: true, message: '请输入内网IP', trigger: 'blur' },
      {
        pattern:
          /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
        message: '请输入正确的IP地址格式',
        trigger: 'blur'
      }
    ],

    system_name: [
      { required: true, message: '请选择系统名称', trigger: 'change' }
    ],
    technician_owner: [
      { required: true, message: '请输入技术负责人', trigger: 'blur' }
    ]
  };

  // 更新显示状态
  const updateVisible = (value) => {
    emit('update:model-value', value);
  };

  // 加载业务系统列表
  const loadBusinessSystems = async () => {
    try {
      businessLoading.value = true;
      const result = await searchBusiness({});
      if (result && result.rows) {
        businessOptions.value = result.rows.map((item) => ({
          label: item.bk_inst_name,
          value: item.bk_biz_name,
          business_collection: item.business_collection || ''
        }));
      }
    } catch (error) {
      console.error('加载业务系统失败:', error);
      businessOptions.value = [];
    } finally {
      businessLoading.value = false;
    }
  };

  // 系统名称变化时自动带出项目名称
  const handleSystemNameChange = (value) => {
    const selectedBusiness = businessOptions.value.find(
      (item) => item.value === value
    );
    if (selectedBusiness && selectedBusiness.business_collection) {
      form.value.project_name = selectedBusiness.business_collection;
    }
  };

  // 保存
  const save = async () => {
    try {
      await formRef.value?.validate();
      loading.value = true;

      // 构建bk_inst_name
      const bkInstName = `${form.value.system_name}_${form.value.host_name}_${form.value.host_innerip}`;

      const data = {
        bkObjId: 'host_resource',
        instInfoMap: {
          bk_inst_name: bkInstName,
          ...form.value,
          apply_user_id: Number(form.value.apply_user_id),
          technician_owner_id: Number(form.value.technician_owner_id)
        }
      };

      if (isUpdate.value) {
        data.instInfoMap.bk_inst_id = props.hostData.bk_inst_id;
        await updateInst(data);
        EleMessage.success('编辑成功');
      } else {
        await createInst(data);
        EleMessage.success('新增成功');
      }

      updateVisible(false);
      emit('done');
    } catch (error) {
      if (error?.message && !error.message.includes('validation')) {
        EleMessage.error(error.message || '保存失败');
      }
    } finally {
      loading.value = false;
    }
  };

  // 关闭时重置表单
  const handleClosed = () => {
    formRef.value?.resetFields?.();
    businessOptions.value = [];
  };

  // 监听数据变化，初始化表单
  watch(
    () => props.hostData,
    (data) => {
      if (data) {
        // 编辑模式：保留原有的录入时间和录入人员
        Object.assign(form.value, data);
      } else {
        // 新增时重置表单并设置默认值
        Object.keys(form.value).forEach((key) => {
          if (typeof form.value[key] === 'number' || form.value[key] === null) {
            form.value[key] = null;
          } else {
            form.value[key] = '';
          }
        });
        // 设置新增时的默认值
        form.value.create_user =
          userStore.info?.nickName || userStore.info?.userName || '';
        form.value.device_role = '0';
        form.value.monitor_status = '0';
        form.value.environment_code = '0';
        form.value.lifecycle_code = '1';
        form.value.create_date = new Date()
          .toLocaleString('sv-SE')
          .replace('T', ' ');
      }
    },
    { immediate: true }
  );

  // 监听抽屉显示状态，加载业务系统列表
  watch(visible, async (newVisible) => {
    if (newVisible) {
      await loadBusinessSystems();
    }
  });
</script>

<style scoped>
  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }

  .dialog-footer {
    text-align: right;
  }

  .el-input[readonly] {
    background-color: #f5f7fa;
  }
</style>
