# Asset Master V2 UI 生产环境部署文档

## 项目概述

Asset Master V2 UI 是一个基于 Vue 3 + Vite + Element Plus 的前端项目，用于资产管理系统的用户界面。

## 部署前准备

### 环境要求

- Node.js >= 16.x
- npm >= 8.x
- 静态文件服务器（如 Nginx、Apache 等）

### 项目信息

- **项目名称**: ele-admin-plus-ruoyi-ultra
- **版本**: 1.4.0
- **构建工具**: Vite
- **UI框架**: Element Plus

## 环境配置

项目支持多环境配置，配置文件如下：

### 开发环境 (.env.development)
```
VITE_API_URL=http://*********:9502/
```

### 预发布环境 (.env.staging)
```
VITE_API_URL=https://vue.ruoyi.vip/prod-api
```

### 生产环境 (.env.production)
```
VITE_API_URL=http://localhost:9502/
```

## 构建步骤

### 1. 安装依赖

```bash
npm install
```

### 2. 构建生产版本

```bash
# 生产环境构建
npm run build

# 预发布环境构建
npm run build:staging
```

构建完成后，会在项目根目录生成 `dist` 文件夹，包含所有静态资源。

### 3. 构建输出说明

- **目标浏览器**: Chrome 63+
- **代码分割**: 启用，警告阈值 4MB
- **压缩**: 启用 gzip 压缩（阈值 10KB）
- **组件**: 按需引入 Element Plus 组件

## 同服务器部署架构

当前项目采用前后端同服务器部署架构，具体配置如下：

### 服务架构
```
用户请求 → Nginx (80端口) → 静态文件服务 (前端)
                          ↓
                    API代理 → 后端服务 (localhost:9502)
```

### 配置要点

1. **环境变量配置**
   - 生产环境使用 `VITE_API_URL=http://localhost:9502/`
   - 通过 Nginx 代理实现前后端统一访问

2. **Nginx 配置关键点**
   - 前端静态文件直接服务
   - `/api` 路径代理到本地后端服务
   - 单页应用路由支持

3. **部署优势**
   - 简化网络配置
   - 减少跨域问题
   - 便于统一管理和维护

### 替代配置方案

**方案一：相对路径配置**
```bash
# .env.production
VITE_API_URL=/api/
```

**方案二：域名统一配置**
```bash
# .env.production
VITE_API_URL=https://your-domain.com/api/
```

### Nginx 配置示例（同服务器部署）

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 静态文件根目录
    root /var/www/asset-master-v2-ui/dist;
    index index.html;

    # 处理单页应用路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理到本地后端服务
    location /api {
        proxy_pass http://localhost:9502;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
```

### Apache 配置示例

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/asset-master-v2-ui/dist

    # 单页应用路由支持
    <Directory "/var/www/asset-master-v2-ui/dist">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted

        # 重写规则
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>

    # 静态资源缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

## 部署流程

### 手动部署

1. **在本地构建**
   ```bash
   npm run build
   ```

2. **上传文件**
   ```bash
   # 使用 scp 上传到服务器
   scp -r dist/* user@server:/var/www/asset-master-v2-ui/
   ```

3. **配置 Web 服务器**
   - 配置 Nginx 或 Apache
   - 重启服务器

### 自动化部署脚本示例

```bash
#!/bin/bash

# 部署脚本 deploy.sh

set -e

echo "开始部署 Asset Master V2 UI..."

# 1. 安装依赖
npm ci

# 2. 代码检查
npm run lint:eslint

# 3. 构建生产版本
npm run build

# 4. 备份当前版本
sudo cp -r /var/www/asset-master-v2-ui /var/www/asset-master-v2-ui-backup-$(date +%Y%m%d_%H%M%S)

# 5. 部署新版本
sudo cp -r dist/* /var/www/asset-master-v2-ui/

# 6. 重启 Nginx
sudo systemctl reload nginx

echo "部署完成！"
```

## 生产环境配置调整

### 移除水印

生产环境中可能会出现 "ELE ADMIN PLUS" 水印，这是由于组件库的授权检查机制。解决方案：

**推荐方案：环境变量配置**
```bash
# .env.production 添加
VITE_API_URL=/api/
VITE_REMOVE_WATERMARK=true
VITE_LICENSE=""
```

**方案二：修改水印组件**
1. 编辑 `components/ele-watermark/index.vue` 第 137-139 行：
```javascript
const forceRender = computed(() => {
  return false;
});
```

2. 编辑 `components/ele-config-provider/components/receiver-view.jsx` 第 246 行：
```javascript
disabled={true}
```

**方案三：全局禁用水印**
在 `src/main.js` 中添加：
```javascript
// 禁用水印
import { provide } from 'vue';
import { STR_KEY } from '@/components/utils/common';

app.provide(STR_KEY, { forbidden: false });
```

**重新构建项目**
```bash
npm run build
```

### VITE_API_URL
- **说明**: 后端 API 服务地址
- **开发环境**: `http://*********:9502/`
- **生产环境**: `/api/` (相对路径，推荐)
- **注意**:
  - 同服务器部署时使用相对路径 `/api/`
  - 确保后端服务在 9502 端口正常运行
  - 通过 Nginx 代理统一访问前后端服务

### VITE_REMOVE_WATERMARK
- **说明**: 是否移除组件库水印
- **默认值**: `false`
- **生产环境建议**: `true`

## 性能优化

### 构建优化
- 启用 gzip 压缩
- 组件按需引入
- 代码分割和懒加载
- 静态资源预优化

### 服务器优化
- 启用 gzip 压缩
- 设置适当的缓存策略
- 使用 CDN（可选）

## 监控和维护

### 日志文件位置
- Nginx 访问日志: `/var/log/nginx/access.log`
- Nginx 错误日志: `/var/log/nginx/error.log`

### 常见问题排查

1. **页面刷新 404 错误**
   - 检查 Web 服务器是否正确配置了单页应用路由重写

2. **API 请求失败**
   - 检查 `.env.production` 中的 `VITE_API_URL` 配置
   - 确认后端服务正常运行

3. **静态资源加载失败**
   - 检查文件权限设置
   - 确认 Web 服务器配置正确

## 回滚方案

```bash
# 快速回滚到备份版本
sudo rm -rf /var/www/asset-master-v2-ui
sudo cp -r /var/www/asset-master-v2-ui-backup-YYYYMMDD_HHMMSS /var/www/asset-master-v2-ui
sudo systemctl reload nginx
```

## 安全注意事项

1. 确保生产环境不暴露源码和配置信息
2. 定期更新依赖包，修复安全漏洞
3. 配置适当的访问控制和防火墙规则
4. 使用 HTTPS 协议（推荐）

## 联系信息

如有部署相关问题，请联系开发团队或运维团队。