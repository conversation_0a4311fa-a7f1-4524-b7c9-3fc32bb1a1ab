<template>
  <ele-drawer
    v-model="visible"
    :size="1000"
    title="维保人员详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="detail-container" v-if="data">
      <!-- 基本信息 -->
      <div class="detail-section-title">基本信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="姓名" :span="1">
          {{ data?.maintenance_staff_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系方式" :span="1">
          {{ data?.maintenance_staff_tel || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="维护标的（项目名称）" :span="1">
          {{ data?.maintained_project || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="人员级别" :span="1">
          {{ data?.maintenance_staff_level || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="所属厂家" :span="2">
          {{ data?.company_name || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 时间信息 -->
      <div class="detail-section-title">时间信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="维保开始时间" :span="1">
          {{ formatDateTime(data?.maintenance_start_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="维保结束时间" :span="1">
          {{ formatDateTime(data?.maintenance_end_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="入场时间" :span="1">
          {{ formatDateTime(data?.entry_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="离场时间" :span="1">
          {{ formatDateTime(data?.exit_time) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 评价信息 -->
      <div class="detail-section-title">评价信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="一季度评价得分" :span="1">
          <el-tag
            v-if="data?.first_quarter_rating !== null && data?.first_quarter_rating !== undefined"
            :type="getRatingType(data.first_quarter_rating)"
            size="small"
          >
            {{ data.first_quarter_rating }}分
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="二季度评价得分" :span="1">
          <el-tag
            v-if="data?.second_quarter_rating !== null && data?.second_quarter_rating !== undefined"
            :type="getRatingType(data.second_quarter_rating)"
            size="small"
          >
            {{ data.second_quarter_rating }}分
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="三季度评价得分" :span="1">
          <el-tag
            v-if="data?.third_quarter_rating !== null && data?.third_quarter_rating !== undefined"
            :type="getRatingType(data.third_quarter_rating)"
            size="small"
          >
            {{ data.third_quarter_rating }}分
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="年度评价得分" :span="1">
          <el-tag
            v-if="data?.yearly_rating !== null && data?.yearly_rating !== undefined"
            :type="getRatingType(data.yearly_rating)"
            size="small"
          >
            {{ data.yearly_rating }}分
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'MaintenanceStaffInfoDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 格式化日期时间显示 */
  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-';

    // 如果已经是 YYYY-MM-DD HH:mm:ss 格式，直接返回
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateTime)) {
      return dateTime;
    }

    // 如果是 YYYY-MM-DD 格式，添加时间部分
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateTime)) {
      return `${dateTime} 00:00:00`;
    }

    // 其他情况尝试转换为Date对象并格式化
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '-';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch {
      return '-';
    }
  };

  /** 根据评分获取标签类型 */
  const getRatingType = (rating) => {
    const score = Number(rating);
    if (score >= 90) return 'success';
    if (score >= 80) return 'warning';
    if (score >= 60) return 'info';
    return 'danger';
  };

  /** 关闭抽屉 */
  const handleClose = () => {
    visible.value = false;
  };
</script>

<style scoped>
  .detail-container {
    padding: 0 8px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0;
  }

  .detail-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .detail-section-title:first-child {
    margin-top: 0;
  }

  :deep(.el-divider) {
    margin: 24px 0 16px;
  }

  :deep(.el-divider__text) {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  :deep(.el-descriptions) {
    margin-bottom: 0;
  }
</style>