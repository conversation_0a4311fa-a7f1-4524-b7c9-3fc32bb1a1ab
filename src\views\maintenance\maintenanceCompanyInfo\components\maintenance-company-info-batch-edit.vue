<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑维保厂家"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="maintenance_mode">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintenance_mode" />
                    <span class="label-text">维保方式</span>
                  </div>
                </template>
                <el-select
                  v-model="form.maintenance_mode"
                  placeholder="请选择维保方式"
                  style="width: 100%"
                  :disabled="!fieldEnabled.maintenance_mode"
                  clearable
                >
                  <el-option label="远程" value="远程" />
                  <el-option label="现场" value="现场" />
                  <el-option label="驻场" value="驻场" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="is_maintenance_due_reminder">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.is_maintenance_due_reminder" />
                    <span class="label-text">维保是否到期提醒</span>
                  </div>
                </template>
                <el-radio-group
                  v-model="form.is_maintenance_due_reminder"
                  :disabled="!fieldEnabled.is_maintenance_due_reminder"
                >
                  <el-radio value="是">是</el-radio>
                  <el-radio value="否">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="maintenance_company">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintenance_company" />
                    <span class="label-text">维保厂家名称</span>
                  </div>
                </template>
                <el-input
                  v-model="form.maintenance_company"
                  placeholder="请输入维保厂家名称"
                  :disabled="!fieldEnabled.maintenance_company"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="contact">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.contact" />
                    <span class="label-text">联系人</span>
                  </div>
                </template>
                <el-input
                  v-model="form.contact"
                  placeholder="请输入联系人"
                  :disabled="!fieldEnabled.contact"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="contact_tel">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.contact_tel" />
                    <span class="label-text">联系方式</span>
                  </div>
                </template>
                <el-input
                  v-model="form.contact_tel"
                  placeholder="请输入联系方式"
                  :disabled="!fieldEnabled.contact_tel"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="maintenance_engineer">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintenance_engineer" />
                    <span class="label-text">维保人员</span>
                  </div>
                </template>
                <el-input
                  v-model="form.maintenance_engineer"
                  placeholder="请输入维保人员"
                  :disabled="!fieldEnabled.maintenance_engineer"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="maintenance_fee">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintenance_fee" />
                    <span class="label-text">维保金额（万）</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.maintenance_fee"
                  placeholder="请输入维保金额"
                  :disabled="!fieldEnabled.maintenance_fee"
                  type="number"
                  clearable
                >
                  <template #append>万</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="maintenance_rating">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintenance_rating" />
                    <span class="label-text">维保评价得分</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.maintenance_rating"
                  placeholder="请输入维保评价得分"
                  :disabled="!fieldEnabled.maintenance_rating"
                  type="number"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="maintained_project">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintained_project" />
                    <span class="label-text">维保标的（项目名称）</span>
                  </div>
                </template>
                <el-input
                  v-model="form.maintained_project"
                  placeholder="请输入维保标的"
                  :disabled="!fieldEnabled.maintained_project"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div class="selected-items-info">
        <el-alert
          :title="`已选择 ${selectedRecords.length} 条记录进行批量编辑`"
          type="info"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        保存
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst } from '@/api/cmdb';

  defineOptions({ name: 'MaintenanceCompanyInfoBatchEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 选中的记录 */
    selectedRecords: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits([
    'update:modelValue',
    'update:selectedRecords',
    'done'
  ]);

  /** 模型实例ID */
  const bkObjId = 'maintenance_company_info';

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 字段启用状态 */
  const fieldEnabled = ref({
    maintenance_mode: false,
    is_maintenance_due_reminder: false,
    maintenance_company: false,
    contact: false,
    contact_tel: false,
    maintenance_engineer: false,
    maintenance_fee: false,
    maintenance_rating: false,
    maintained_project: false
  });

  /** 表单数据 */
  const form = ref({
    maintenance_mode: '',
    is_maintenance_due_reminder: '',
    maintenance_company: '',
    contact: '',
    contact_tel: '',
    maintenance_engineer: '',
    maintenance_fee: '',
    maintenance_rating: '',
    maintained_project: ''
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {};
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = async () => {
    if (!formRef.value) return;

    // 获取启用的字段数据
    const updateData = {};
    Object.keys(fieldEnabled.value).forEach((field) => {
      if (fieldEnabled.value[field]) {
        updateData[field] = form.value[field];
      }
    });

    if (Object.keys(updateData).length === 0) {
      EleMessage.error('请至少选择一个字段进行编辑');
      return;
    }

    loading.value = true;

    try {
      const bkInstId = props.selectedRecords.map((record) => record.bk_inst_id);
      await batchUpdateInst({
        bkObjId,
        bkInstId,
        instInfoMap: {
          ...updateData
        }
      });
      EleMessage.success(`成功批量编辑 ${props.selectedRecords.length} 条记录`);
      updateVisible(false);
      emit('done');
    } catch (error) {
      EleMessage.error(error.message || '批量编辑失败');
    } finally {
      loading.value = false;
    }
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    // 重置字段启用状态
    Object.keys(fieldEnabled.value).forEach((field) => {
      fieldEnabled.value[field] = false;
    });
    // 重置表单数据
    Object.assign(form.value, {
      maintenance_mode: '',
      is_maintenance_due_reminder: '',
      maintenance_company: '',
      contact: '',
      contact_tel: '',
      maintenance_engineer: '',
      maintenance_fee: '',
      maintenance_rating: '',
      maintained_project: ''
    });
  };

  /** 监听弹窗打开状态 */
  watch(
    () => visible.value,
    (newValue) => {
      if (newValue) {
        resetFields();
      }
    }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .field-with-checkbox {
    margin-bottom: 16px;
  }

  .form-label-with-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .label-text {
    user-select: none;
  }

  .selected-items-info {
    margin-top: 20px;
  }

  :deep(.el-form-item__label) {
    padding-right: 0 !important;
  }
</style>