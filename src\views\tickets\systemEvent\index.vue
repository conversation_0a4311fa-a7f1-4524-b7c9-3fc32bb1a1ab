<template>
  <ele-page>
    <ele-card shadow="never" :body-style="{ paddingBottom: '0' }">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="我的审核" name="review">
          <div class="tab-content">
            <cmdb-pro-table
              ref="reviewTableRef"
              row-key="ticket_id"
              :columns="reviewColumns"
              :datasource="reviewDatasource"
              :show-overflow-tooltip="true"
              v-model:selections="reviewSelections"
              highlight-current-row
              :export-config="{ fileName: '我的审核工单' }"
              cache-key="reviewTicketTable"
            >
              <template #toolbar>
                <el-button type="primary" @click="handleCreateTicket">
                  新建工单
                </el-button>
              </template>
              <template #level="{ row }">
                <el-tag :type="getLevelType(row.level)" size="small">
                  {{ getLevelText(row.level) }}
                </el-tag>
              </template>
              <template #ticket_status="{ row }">
                <el-tag :type="getStatusType(row.ticket_status)" size="small">
                  {{ getStatusText(row.ticket_status) }}
                </el-tag>
              </template>
              <template #ticketFlowUserId="{ row }">
                <div v-if="row.ticketFlowUserId && row.ticketFlowUserId.length">
                  <el-tag
                    v-for="userId in row.ticketFlowUserId.slice(0, 2)"
                    :key="userId"
                    size="small"
                    style="margin-right: 4px; margin-bottom: 2px"
                  >
                    {{ getUserNickname(userId) }}
                  </el-tag>
                  <span
                    v-if="row.ticketFlowUserId.length > 2"
                    class="more-users"
                  >
                    +{{ row.ticketFlowUserId.length - 2 }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
              <template #action="{ row }">
                <el-link
                  type="success"
                  underline="false"
                  @click="handleApprove(row)"
                >
                  通过
                </el-link>
                <el-divider direction="vertical" />
                <el-link
                  type="danger"
                  underline="false"
                  @click="handleReject(row)"
                >
                  拒绝
                </el-link>
                <el-divider direction="vertical" />
                <el-link
                  type="primary"
                  underline="false"
                  @click="handleViewDetail(row)"
                >
                  查看
                </el-link>
              </template>
            </cmdb-pro-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="我的代办" name="todo">
          <div class="tab-content">
            <cmdb-pro-table
              ref="todoTableRef"
              row-key="ticket_id"
              :columns="todoColumns"
              :datasource="todoDatasource"
              :show-overflow-tooltip="true"
              v-model:selections="todoSelections"
              highlight-current-row
              :export-config="{ fileName: '我的代办工单' }"
              cache-key="todoTicketTable"
            >
              <template #level="{ row }">
                <el-tag :type="getLevelType(row.level)" size="small">
                  {{ getLevelText(row.level) }}
                </el-tag>
              </template>
              <template #ticket_status="{ row }">
                <el-tag :type="getStatusType(row.ticket_status)" size="small">
                  {{ getStatusText(row.ticket_status) }}
                </el-tag>
              </template>
              <template #ticketFlowUserId="{ row }">
                <div v-if="row.ticketFlowUserId && row.ticketFlowUserId.length">
                  <el-tag
                    v-for="userId in row.ticketFlowUserId.slice(0, 2)"
                    :key="userId"
                    size="small"
                    style="margin-right: 4px; margin-bottom: 2px"
                  >
                    {{ getUserNickname(userId) }}
                  </el-tag>
                  <span
                    v-if="row.ticketFlowUserId.length > 2"
                    class="more-users"
                  >
                    +{{ row.ticketFlowUserId.length - 2 }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
              <template #action="{ row }">
                <el-link
                  type="primary"
                  underline="false"
                  @click="handleViewDetail(row)"
                >
                  详情
                </el-link>
                <el-divider direction="vertical" />
                <el-link
                  type="success"
                  underline="false"
                  @click="handleFillRecord(row)"
                >
                  填写记录
                </el-link>
                <el-divider direction="vertical" />
                <el-link
                  type="warning"
                  underline="false"
                  @click="handleTransfer(row)"
                >
                  转派
                </el-link>
              </template>
            </cmdb-pro-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="所有涉及工单" name="all">
          <div class="tab-content">
            <cmdb-pro-table
              ref="allTableRef"
              row-key="ticket_id"
              :columns="allColumns"
              :datasource="allDatasource"
              :show-overflow-tooltip="true"
              v-model:selections="allSelections"
              highlight-current-row
              :export-config="{ fileName: '所有涉及工单' }"
              cache-key="allTicketTable"
            >
              <template #level="{ row }">
                <el-tag :type="getLevelType(row.level)" size="small">
                  {{ getLevelText(row.level) }}
                </el-tag>
              </template>
              <template #ticket_status="{ row }">
                <el-tag :type="getStatusType(row.ticket_status)" size="small">
                  {{ getStatusText(row.ticket_status) }}
                </el-tag>
              </template>
              <template #ticketFlowUserId="{ row }">
                <div v-if="row.ticketFlowUserId && row.ticketFlowUserId.length">
                  <el-tag
                    v-for="userId in row.ticketFlowUserId.slice(0, 2)"
                    :key="userId"
                    size="small"
                    style="margin-right: 4px; margin-bottom: 2px"
                  >
                    {{ getUserNickname(userId) }}
                  </el-tag>
                  <span
                    v-if="row.ticketFlowUserId.length > 2"
                    class="more-users"
                  >
                    +{{ row.ticketFlowUserId.length - 2 }}
                  </span>
                </div>
                <span v-else>-</span>
              </template>
              <template #action="{ row }">
                <el-link
                  type="primary"
                  underline="false"
                  @click="handleViewDetail(row)"
                >
                  详情
                </el-link>
              </template>
            </cmdb-pro-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </ele-card>

    <!-- 工单详情抽屉 -->
    <TicketDetail
      v-model="detailVisible"
      :ticket-data="currentTicket"
      @done="handleRefresh"
    />

    <!-- 新建工单抽屉 -->
    <TicketCreate v-model="createVisible" @done="handleRefresh" />

    <!-- 转派工单抽屉 -->
    <TicketTransfer
      v-model="transferVisible"
      :ticket-data="currentTicket"
      @done="handleRefresh"
    />

    <!-- 关闭工单确认对话框 -->
    <TicketClose
      v-model="closeVisible"
      :ticket-data="currentTicket"
      @done="handleRefresh"
    />

    <!-- 拒绝工单确认对话框 -->
    <TicketReject
      v-model="rejectVisible"
      :ticket-data="currentTicket"
      @done="handleRefresh"
    />

    <!-- 填写记录抽屉 -->
    <TicketFillRecord
      v-model="fillRecordVisible"
      :ticket-data="currentTicket"
      @done="handleRefresh"
    />
  </ele-page>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { useUserStore } from '@/store/modules/user';
  import { searchTicketFlow, closeTicket } from '@/api/ticket';
  import { pageUsers } from '@/api/system/user';
  import TicketDetail from './components/ticket-detail.vue';
  import TicketCreate from './components/ticket-create.vue';
  import TicketTransfer from './components/ticket-transfer.vue';
  import TicketClose from './components/ticket-close.vue';
  import TicketReject from './components/ticket-reject.vue';
  import TicketFillRecord from './components/ticket-fill-record.vue';

  defineOptions({ name: 'TicketManage' });

  const userStore = useUserStore();
  const bkObjId = 'system_events_test';
  // 当前选中的标签页
  const activeTab = ref('review');

  // 表格选中数据
  const reviewSelections = ref([]);
  const todoSelections = ref([]);
  const allSelections = ref([]);

  // 表格实例
  const reviewTableRef = ref(null);
  const todoTableRef = ref(null);
  const allTableRef = ref(null);

  // 弹窗状态
  const detailVisible = ref(false);
  const createVisible = ref(false);
  const transferVisible = ref(false);
  const closeVisible = ref(false);
  const rejectVisible = ref(false);
  const fillRecordVisible = ref(false);

  // 当前操作的工单
  const currentTicket = ref(null);

  // 用户数据映射
  const userMap = ref(new Map());

  // 我的审核列配置
  const reviewColumns = computed(() => [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center'
    },
    {
      prop: 'ticket_id',
      label: '工单ID',
      width: 180,
      showOverflowTooltip: true
    },
    {
      label: '工单类型',
      width: 100,
      align: 'center',
      formatter: () => '系统事件'
    },
    {
      prop: 'level',
      label: '工单等级',
      width: 100,
      align: 'center',
      slot: 'level'
    },
    {
      prop: 'comment',
      label: '工单内容',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'ticket_status',
      label: '工单状态',
      width: 100,
      align: 'center',
      slot: 'ticket_status'
    },
    {
      prop: 'ticket_start_time',
      label: '工单开始时间',
      width: 180,
      sortable: 'custom',
      formatter: (row) => formatTime(row.ticket_start_time)
    },
    {
      prop: 'ticketFlowUserId',
      label: '工单涉及人员',
      width: 150,
      align: 'center',
      slot: 'ticketFlowUserId'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  // 我的代办列配置
  const todoColumns = computed(() => [
    {
      prop: 'ticket_id',
      label: '工单ID',
      width: 180,
      showOverflowTooltip: true
    },
    {
      label: '工单类型',
      width: 100,
      align: 'center',
      formatter: () => '系统事件'
    },
    {
      prop: 'level',
      label: '工单等级',
      width: 100,
      align: 'center',
      slot: 'level'
    },
    {
      prop: 'comment',
      label: '工单内容',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'ticket_status',
      label: '工单状态',
      width: 100,
      align: 'center',
      slot: 'ticket_status'
    },
    {
      prop: 'ticket_start_time',
      label: '工单开始时间',
      width: 180,
      formatter: (row) => formatTime(row.ticket_start_time)
    },
    {
      prop: 'ticketFlowUserId',
      label: '工单涉及人员',
      width: 150,
      align: 'center',
      slot: 'ticketFlowUserId'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  // 所有涉及工单列配置
  const allColumns = computed(() => [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center'
    },
    {
      prop: 'ticket_id',
      label: '工单ID',
      width: 180,
      showOverflowTooltip: true
    },
    {
      label: '工单类型',
      width: 100,
      align: 'center',
      formatter: () => '系统事件'
    },
    {
      prop: 'level',
      label: '工单等级',
      width: 100,
      align: 'center',
      slot: 'level'
    },
    {
      prop: 'comment',
      label: '工单内容',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'ticket_status',
      label: '工单状态',
      width: 100,
      align: 'center',
      slot: 'ticket_status'
    },
    {
      prop: 'ticket_start_time',
      label: '工单开始时间',
      width: 180,
      formatter: (row) => formatTime(row.ticket_start_time)
    },
    {
      prop: 'ticketFlowUserId',
      label: '工单涉及人员',
      width: 150,
      align: 'center',
      slot: 'ticketFlowUserId'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 100,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  // 数据源函数
  const reviewDatasource = async ({ pages, where, filters }) => {
    const params = {
      from: (pages.pageNum - 1) * pages.pageSize,
      size: pages.pageSize,
      create_user_id: userStore.info.userId
    };

    const body = {
      ...where,
      ...filters,
      step_status: 0,
      executor_user_id: userStore.info.userId
    };

    const result = await searchTicketFlow(body, params);

    // 前端过滤：只保留待处理状态的工单
    if (result.rows) {
      result.rows = result.rows.filter((row) => row.ticket_status === 0);
      result.total = result.rows.length;
    }

    return result;
  };

  const todoDatasource = ({ pages, where, filters }) => {
    const params = {
      from: (pages.pageNum - 1) * pages.pageSize,
      size: pages.pageSize
    };

    const body = {
      ...where,
      ...filters,
      step_status: 0,
      executor_user_id: userStore.info.userId
    };

    return searchTicketFlow(body, params);
  };

  const allDatasource = ({ pages, where, filters }) => {
    const params = {
      from: (pages.pageNum - 1) * pages.pageSize,
      size: pages.pageSize
    };

    const body = {
      ...where,
      ...filters,
      // step_status: 0,
      executor_user_id: userStore.info.userId
    };

    return searchTicketFlow(body, params);
  };

  // 获取所有用户数据
  const loadAllUsers = async () => {
    try {
      const res = await pageUsers({
        pageNum: 1,
        pageSize: 9999
      });
      const users = res.rows || [];
      userMap.value.clear();
      users.forEach((user) => {
        userMap.value.set(user.userId, user.nickName || user.userName);
      });
    } catch (e) {
      console.error('获取用户列表失败:', e);
    }
  };

  // 根据用户ID获取用户昵称
  const getUserNickname = (userId) => {
    // 特殊处理：用户ID为1时显示admin
    if (userId === 1 || userId === '1') {
      return 'admin';
    }
    return userMap.value.get(userId) || `用户${userId}`;
  };

  // 标签页切换
  const handleTabChange = (tab) => {
    switch (tab) {
      case 'review':
        reviewTableRef.value?.reload?.({ page: 1 });
        break;
      case 'todo':
        todoTableRef.value?.reload?.({ page: 1 });
        break;
      case 'all':
        allTableRef.value?.reload?.({ page: 1 });
        break;
    }
  };

  // 刷新当前标签页数据
  const handleRefresh = () => {
    switch (activeTab.value) {
      case 'review':
        reviewTableRef.value?.reload?.();
        break;
      case 'todo':
        todoTableRef.value?.reload?.();
        break;
      case 'all':
        allTableRef.value?.reload?.();
        break;
    }
  };

  // 新建工单
  const handleCreateTicket = () => {
    createVisible.value = true;
  };

  // 查看详情
  const handleViewDetail = (row) => {
    currentTicket.value = row;
    detailVisible.value = true;
  };

  // 转派工单
  const handleTransfer = (row) => {
    currentTicket.value = row;
    transferVisible.value = true;
  };

  // 审核工单
  const handleApprove = (row) => {
    ElMessageBox.confirm(
      '审核通过后，相关数据录入到系统事件表中，再次确认是否通过？',
      '确认审核',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      try {
        // 构建bk_inst_name：事件标题 + 发生时间
        const eventName = row.system_item?.event_name || '系统事件';
        const occurrenceTime = row.system_item?.occurrence_time || '';
        const bkInstName =
          eventName + (occurrenceTime ? '_' + occurrenceTime : '');

        await closeTicket({
          ticketId: row.ticket_id,
          approval: true,
          bkObjId,
          bk_inst_name: bkInstName
        });
        EleMessage.success('审核通过成功');
        handleRefresh();
      } catch (e) {
        EleMessage.error(e.message || '审核失败');
      }
    });
  };

  // 拒绝工单
  const handleReject = (row) => {
    currentTicket.value = row;
    rejectVisible.value = true;
  };

  // 填写记录
  const handleFillRecord = (row) => {
    currentTicket.value = row;
    fillRecordVisible.value = true;
  };

  // 获取级别标签类型
  const getLevelType = (level) => {
    const levelTypes = {
      p1: 'danger',
      p2: 'warning',
      p3: 'primary',
      p4: 'info'
    };
    return levelTypes[level] || 'info';
  };

  // 获取级别文本
  const getLevelText = (level) => {
    const levelTexts = {
      p1: '紧急',
      p2: '高',
      p3: '中',
      p4: '低'
    };
    return levelTexts[level] || level;
  };

  // 获取状态标签类型
  const getStatusType = (status) => {
    const statusTypes = {
      0: 'warning', // 待处理
      1: 'success', // 已完成
      2: 'danger' // 已关闭
    };
    return statusTypes[status] || 'info';
  };

  // 获取状态文本
  const getStatusText = (status) => {
    const statusTexts = {
      0: '待处理',
      1: '已完成',
      2: '已关闭'
    };
    return statusTexts[status] || '未知';
  };

  // 格式化时间
  const formatTime = (timeValue) => {
    if (!timeValue) return '-';
    const date = new Date(timeValue);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  onMounted(() => {
    loadAllUsers(); // 先加载用户数据
  });
</script>

<style scoped>
  .tab-content {
    padding: 16px 0;
  }

  .more-users {
    font-size: 12px;
    color: #909399;
  }
</style>
