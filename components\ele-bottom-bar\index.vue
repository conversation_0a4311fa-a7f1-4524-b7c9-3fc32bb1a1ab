<!-- 底部工具栏 -->
<template>
  <Teleport :to="teleportTo" :disabled="!teleported || !teleportTo">
    <div :class="['ele-bottom-bar', { 'is-deactivated': !isActivated }]">
      <div class="ele-bottom-bar-body" :style="bodyStyle">
        <slot></slot>
      </div>
      <div class="ele-bottom-bar-extra" :style="extraStyle">
        <slot name="extra"></slot>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref, computed, onActivated, onDeactivated } from 'vue';
  import { useLayoutState } from '../ele-pro-layout/util';
  import { bottomBarProps } from './props';

  defineOptions({ name: 'EleBottomBar' });

  defineProps(bottomBarProps);

  const layoutState = useLayoutState();

  /** 适配组件缓存 */
  const isActivated = ref(true);

  /** 传送节点 */
  const teleportTo = computed(() => {
    return layoutState.getBodyWrapperEl?.();
  });

  onActivated(() => {
    isActivated.value = true;
  });

  onDeactivated(() => {
    isActivated.value = false;
  });
</script>
