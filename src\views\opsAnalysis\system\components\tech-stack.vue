<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex items-center">
        <el-icon class="text-primary mr-2"><Operation /></el-icon>
        <span class="text-lg font-semibold">技术栈信息</span>
      </div>
    </template>
    <div class="p-2">
      <el-row :gutter="24">
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">基础框架</h3>
            <div class="flex items-center">
              <el-icon :class="hasCfpFramework ? 'text-success' : 'text-error'" class="mr-2">
                <Check v-if="hasCfpFramework" />
                <Close v-else />
              </el-icon>
              <span>{{ hasCfpFramework ? '采用CFP框架开发' : '未采用CFP框架' }}</span>
            </div>
            <div v-if="systemFramework !== '未配置'" class="mt-2">
              <span class="text-sm text-gray-600">系统框架: {{ systemFramework }}</span>
            </div>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">中间件</h3>
            <div class="flex flex-wrap gap-2">
              <el-tag
                v-for="item in middleware"
                :key="item"
                type="primary"
                effect="light"
                round
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">数据库</h3>
            <div class="flex flex-wrap gap-2">
              <el-tag
                v-for="item in databases"
                :key="item"
                type="success"
                effect="light"
                round
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">开发语言</h3>
            <div class="flex flex-wrap gap-2">
              <el-tag
                v-for="item in languages"
                :key="item"
                type="warning"
                effect="light"
                round
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup>
  import { computed } from 'vue';
  import { Operation, Check, Close } from '@element-plus/icons-vue';

  // 接收父组件传递的系统数据
  const props = defineProps({
    systemData: {
      type: Object,
      default: () => ({})
    }
  });

  // 计算属性：是否采用CFP框架
  const hasCfpFramework = computed(() => {
    return props.systemData.whether_equipped_with_cfp_framework === '是';
  });

  // 计算属性：系统框架
  const systemFramework = computed(() => {
    return props.systemData.used_system_framework || '未配置';
  });

  // 计算属性：中间件列表
  const middleware = computed(() => {
    const middlewareStr = props.systemData.middleware;
    if (!middlewareStr) return [];
    return middlewareStr.split(/[,，、\s]+/).filter(item => item.trim());
  });

  // 计算属性：数据库列表
  const databases = computed(() => {
    const dbStr = props.systemData.company_configuration_database;
    if (!dbStr) return [];
    return dbStr.split(/[,，、\s]+/).filter(item => item.trim());
  });

  // 计算属性：开发语言列表
  const languages = computed(() => {
    const langStr = props.systemData.development_language;
    if (!langStr) return [];
    return langStr.split(/[,，、\s]+/).filter(item => item.trim());
  });
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }

  .text-success {
    color: #00b42a;
  }

  .text-error {
    color: #f53f3f;
  }

  .text-gray-600 {
    color: #4b5563;
  }

  .gap-2 {
    gap: 0.5rem;
  }
</style>
