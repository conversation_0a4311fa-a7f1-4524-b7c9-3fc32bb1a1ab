<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑监管法规"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="issuing_authority">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.issuing_authority" />
                    <span class="label-text">发布机构</span>
                  </div>
                </template>
                <el-select
                  v-model="form.issuing_authority"
                  placeholder="请选择发布机构"
                  style="width: 100%"
                  :disabled="!fieldEnabled.issuing_authority"
                  filterable
                  allow-create
                  default-first-option
                >
                  <el-option label="中国人民银行" value="中国人民银行" />
                  <el-option label="原银保监会" value="原银保监会" />
                  <el-option
                    label="国家金融监督管理总局"
                    value="国家金融监督管理总局"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="issue_time">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.issue_time" />
                    <span class="label-text">发布时间</span>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.issue_time"
                  type="date"
                  placeholder="选择发布时间"
                  style="width: 100%"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  :disabled="!fieldEnabled.issue_time"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="effective_time">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.effective_time" />
                    <span class="label-text">生效时间</span>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.effective_time"
                  type="date"
                  placeholder="选择生效时间"
                  style="width: 100%"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  :disabled="!fieldEnabled.effective_time"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="expiration_time">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.expiration_time" />
                    <span class="label-text">失效时间</span>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.expiration_time"
                  type="date"
                  placeholder="选择失效时间"
                  style="width: 100%"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  :disabled="!fieldEnabled.expiration_time"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="management_domain">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.management_domain" />
                    <span class="label-text">管理领域</span>
                  </div>
                </template>
                <el-select
                  v-model="form.management_domain"
                  placeholder="请选择管理领域"
                  style="width: 100%"
                  :disabled="!fieldEnabled.management_domain"
                  filterable
                  allow-create
                  default-first-option
                >
                  <el-option label="科技外包" value="科技外包" />
                  <el-option label="运营管理" value="运营管理" />
                  <el-option label="网络及数据安全" value="网络及数据安全" />
                  <el-option label="战略规划" value="战略规划" />
                  <el-option label="业务连续性管理" value="业务连续性管理" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="file_status">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.file_status" />
                    <span class="label-text">文件状态</span>
                  </div>
                </template>
                <el-select
                  v-model="form.file_status"
                  placeholder="请选择文件状态"
                  style="width: 100%"
                  :disabled="!fieldEnabled.file_status"
                >
                  <el-option label="生效中" value="生效中" />
                  <el-option label="已失效" value="已失效" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="updateVisible(false)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst } from '@/api/cmdb';

  defineOptions({ name: 'RegulatoryLawsBatchEdit' });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 选中的记录 */
  const selectedRecords = defineModel('selectedRecords', {
    type: Array,
    default: () => []
  });

  /** 模型实例ID */
  const bkObjId = 'regulatory_laws';

  /** 表单实例 */
  const formRef = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 字段启用状态 */
  const fieldEnabled = reactive({
    issuing_authority: false,
    issue_time: false,
    effective_time: false,
    expiration_time: false,
    management_domain: false,
    file_status: false
  });

  /** 表单数据 */
  const form = reactive({
    issuing_authority: '',
    issue_time: '',
    effective_time: '',
    expiration_time: '',
    management_domain: '',
    file_status: ''
  });

  /** 表单验证规则 */
  const rules = reactive({});

  /** 更新弹窗状态 */
  const updateVisible = (value) => {
    visible.value = value;
  };

  /** 弹窗关闭回调 */
  const handleClosed = () => {
    resetForm();
  };

  /** 重置表单 */
  const resetForm = () => {
    Object.keys(form).forEach((key) => {
      form[key] = '';
    });
    Object.keys(fieldEnabled).forEach((key) => {
      fieldEnabled[key] = false;
    });
    formRef.value?.clearValidate();
  };

  /** 提交表单 */
  const submit = async () => {
    const hasEnabledField = Object.values(fieldEnabled).some(
      (enabled) => enabled
    );
    if (!hasEnabledField) {
      EleMessage.warning('请至少启用一个字段进行编辑');
      return;
    }

    const instInfoMap = {};
    Object.keys(fieldEnabled).forEach((key) => {
      if (fieldEnabled[key]) {
        instInfoMap[key] = form[key];
      }
    });

    const batchUpdateData = {
      bkObjId,
      bkInstId: selectedRecords.value.map((record) => record.bk_inst_id),
      instInfoMap
    };

    loading.value = true;
    try {
      await batchUpdateInst(batchUpdateData);
      EleMessage.success(`成功批量编辑 ${selectedRecords.value.length} 条记录`);
      updateVisible(false);
      emit('done');
    } catch (error) {
      EleMessage.error(error.message || '批量编辑失败');
    } finally {
      loading.value = false;
    }
  };

  /** 监听弹窗打开状态 */
  watch(
    () => visible.value,
    (newValue) => {
      if (newValue) {
        resetForm();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .field-with-checkbox {
    margin-bottom: 20px;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    .form-label-with-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;

      .label-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }
    }
  }
</style>
