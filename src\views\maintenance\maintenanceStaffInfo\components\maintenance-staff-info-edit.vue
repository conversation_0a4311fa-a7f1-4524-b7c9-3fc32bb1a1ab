<!-- 维保人员信息编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑维保人员' : '新建维保人员'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="姓名" prop="maintenance_staff_name">
              <el-input
                v-model="form.maintenance_staff_name"
                placeholder="请输入姓名"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="maintenance_staff_tel">
              <el-input
                v-model="form.maintenance_staff_tel"
                placeholder="请输入联系方式"
                clearable
                :maxlength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维护标的（项目名称）" prop="maintained_project">
              <el-input
                v-model="form.maintained_project"
                placeholder="请输入维护标的（项目名称）"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人员级别" prop="maintenance_staff_level">
              <el-input
                v-model="form.maintenance_staff_level"
                placeholder="请输入人员级别"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="所属厂家" prop="company_name">
              <el-input
                v-model="form.company_name"
                placeholder="请输入所属厂家"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 时间信息 -->
        <div class="form-section-title">时间信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维保开始时间" prop="maintenance_start_date">
              <el-date-picker
                v-model="form.maintenance_start_date"
                type="datetime"
                placeholder="选择维保开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保结束时间" prop="maintenance_end_date">
              <el-date-picker
                v-model="form.maintenance_end_date"
                type="datetime"
                placeholder="选择维保结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="入场时间" prop="entry_time">
              <el-date-picker
                v-model="form.entry_time"
                type="datetime"
                placeholder="选择入场时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="离场时间" prop="exit_time">
              <el-date-picker
                v-model="form.exit_time"
                type="datetime"
                placeholder="选择离场时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 评价信息 -->
        <div class="form-section-title">评价信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="一季度评价得分" prop="first_quarter_rating">
              <el-input
                v-model.number="form.first_quarter_rating"
                placeholder="请输入一季度评价得分"
                type="number"
                min="0"
                max="100"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二季度评价得分" prop="second_quarter_rating">
              <el-input
                v-model.number="form.second_quarter_rating"
                placeholder="请输入二季度评价得分"
                type="number"
                min="0"
                max="100"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="三季度评价得分" prop="third_quarter_rating">
              <el-input
                v-model.number="form.third_quarter_rating"
                placeholder="请输入三季度评价得分"
                type="number"
                min="0"
                max="100"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年度评价得分" prop="yearly_rating">
              <el-input
                v-model.number="form.yearly_rating"
                placeholder="请输入年度评价得分"
                type="number"
                min="0"
                max="100"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { createInst, updateInst } from '@/api/cmdb';

  defineOptions({ name: 'MaintenanceStaffInfoEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'maintenance_staff_info';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单数据 */
  const form = ref({
    maintenance_staff_name: '',
    maintenance_staff_tel: '',
    maintained_project: '',
    maintenance_staff_level: '',
    company_name: '',
    maintenance_start_date: null,
    maintenance_end_date: null,
    entry_time: null,
    first_quarter_rating: null,
    second_quarter_rating: null,
    third_quarter_rating: null,
    yearly_rating: null,
    exit_time: null
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {
      maintenance_staff_name: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
      ],
      maintenance_staff_tel: [
        { required: true, message: '请输入联系方式', trigger: 'blur' }
      ],
      maintained_project: [
        { required: true, message: '请输入维护标的（项目名称）', trigger: 'blur' }
      ]
    };
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.maintenance_staff_name}_${data.company_name || '维保人员'}`
            }
          });
          EleMessage.success('修改成功');
        } else {
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.maintenance_staff_name}_${data.company_name || '维保人员'}`
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
  };

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
      } else {
        // 新增模式，重置表单
        Object.assign(form.value, {
          maintenance_staff_name: '',
          maintenance_staff_tel: '',
          maintained_project: '',
          maintenance_staff_level: '',
          company_name: '',
          maintenance_start_date: null,
          maintenance_end_date: null,
          entry_time: null,
          first_quarter_rating: null,
          second_quarter_rating: null,
          third_quarter_rating: null,
          yearly_rating: null,
          exit_time: null
        });
      }
    },
    { immediate: true }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  :deep(.el-divider) {
    margin: 16px 0;
  }

  :deep(.el-divider__text) {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }
</style>