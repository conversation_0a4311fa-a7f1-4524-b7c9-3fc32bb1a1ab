<template>
  <ele-drawer
    v-model="visible"
    :size="1000"
    title="维保服务记录详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="detail-container" v-if="data">
      <!-- 基本信息 -->
      <div class="detail-section-title">基本信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="维保人员" :span="1">
          {{ data?.maintenance_staff || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="关联系统" :span="1">
          {{ data?.associated_system || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="关联合同" :span="1">
          {{ data?.associated_contract || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="工作类别" :span="1">
          <el-tag
            v-if="data?.work_category"
            :type="getWorkCategoryType(data.work_category)"
            size="small"
          >
            {{ data.work_category }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="工作完成时间" :span="1">
          {{ formatDateTime(data?.work_completion_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="结果验证人员" :span="1">
          {{ data?.result_verification_staff || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 工作详情 -->
      <div class="detail-section-title">工作详情</div>
      <el-descriptions
        :column="1"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="工作依据/问题描述" :span="1">
          <div class="text-content">
            {{ data?.work_basis_problem_description || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="工作内容" :span="1">
          <div class="text-content">
            {{ data?.work_content || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="工作执行结果" :span="1">
          <div class="text-content">
            {{ data?.work_execution_result || '-' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'MaintenanceServiceRecordDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 格式化日期时间显示 */
  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-';

    // 如果已经是 YYYY-MM-DD HH:mm:ss 格式，直接返回
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateTime)) {
      return dateTime;
    }

    // 如果是 YYYY-MM-DD 格式，添加时间部分
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateTime)) {
      return `${dateTime} 00:00:00`;
    }

    // 其他情况尝试转换为Date对象并格式化
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '-';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch {
      return '-';
    }
  };

  /** 根据工作类别获取标签类型 */
  const getWorkCategoryType = (category) => {
    switch (category) {
      case '问题排查':
        return 'warning';
      case '例行维护':
        return 'success';
      case '定期巡检':
        return 'info';
      default:
        return '';
    }
  };

  /** 关闭抽屉 */
  const handleClose = () => {
    visible.value = false;
  };
</script>

<style scoped>
  .detail-container {
    padding: 0 8px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0;
  }

  .detail-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .detail-section-title:first-child {
    margin-top: 0;
  }

  .text-content {
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 300px;
    overflow-y: auto;
    line-height: 1.6;
  }

  :deep(.el-divider) {
    margin: 24px 0 16px;
  }

  :deep(.el-divider__text) {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  :deep(.el-descriptions) {
    margin-bottom: 0;
  }

  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
    vertical-align: top;
  }
</style>