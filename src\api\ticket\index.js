import request from '@/utils/request';
const formData = {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
};
// 搜索工单流程
export async function searchTicketFlow(data, params = {}) {
  const config = Object.keys(params).length > 0 ? { params } : {};
  const res = await request.post('/ticket/searchTicketFlow', data, config);
  if (res.data.code === 200) {
    return {
      code: res.data.code,
      msg: res.data.msg,
      rows: res.data.data.result, // 将 info 数组作为数据
      total: res.data.data.total // 总数
    };
  }
  return Promise.reject(new Error(res.data.msg));
}

// 搜索工单
export async function searchTicket(data, params = {}) {
  const config = Object.keys(params).length > 0 ? { params } : {};
  const res = await request.post('/ticket/searchTicket', data, config);
  if (res.data.code === 200) {
    return {
      code: res.code,
      msg: res.msg,
      rows: res.data.data.result, // 将 info 数组作为数据
      total: res.data.data.total // 总数
    };
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新建工单
export async function allocationTicket(data) {
  const res = await request.post('/ticket/allocationTicket', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 转派工单
export async function transferTicket(data) {
  const res = await request.post('/ticket/transferTicket', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 关闭工单
export async function closeTicket(data) {
  const res = await request.post('/ticket/closeTicket', data, formData);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
