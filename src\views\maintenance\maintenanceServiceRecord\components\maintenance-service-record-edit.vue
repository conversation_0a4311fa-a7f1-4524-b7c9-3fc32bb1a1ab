<!-- 维保服务记录编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑维保服务记录' : '新建维保服务记录'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维保人员" prop="maintenance_staff">
              <el-select
                v-model="form.maintenance_staff"
                placeholder="请选择维保人员"
                style="width: 100%"
                :loading="staffLoading"
                filterable
                clearable
                @focus="loadMaintenanceStaff"
              >
                <el-option
                  v-for="staff in staffOptions"
                  :key="staff.value"
                  :label="staff.label"
                  :value="staff.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联系统" prop="associated_system">
              <el-select
                v-model="form.associated_system"
                placeholder="请选择关联系统"
                style="width: 100%"
                :loading="systemLoading"
                filterable
                clearable
                @focus="loadSystems"
              >
                <el-option
                  v-for="system in systemOptions"
                  :key="system.value"
                  :label="system.label"
                  :value="system.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="关联合同" prop="associated_contract">
              <el-select
                v-model="form.associated_contract"
                placeholder="请选择关联合同"
                style="width: 100%"
                :loading="contractLoading"
                filterable
                clearable
                @focus="loadContracts"
              >
                <el-option
                  v-for="contract in contractOptions"
                  :key="contract.value"
                  :label="contract.label"
                  :value="contract.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作类别" prop="work_category">
              <el-select
                v-model="form.work_category"
                placeholder="请选择工作类别"
                style="width: 100%"
                clearable
              >
                <el-option label="问题排查" value="问题排查" />
                <el-option label="例行维护" value="例行维护" />
                <el-option label="定期巡检" value="定期巡检" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="工作完成时间" prop="work_completion_time">
              <el-date-picker
                v-model="form.work_completion_time"
                type="datetime"
                placeholder="选择工作完成时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结果验证人员" prop="result_verification_staff">
              <el-input
                v-model="form.result_verification_staff"
                placeholder="请输入结果验证人员"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 工作描述 -->
        <div class="form-section-title">工作描述</div>
        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item
              label="工作依据/问题描述"
              prop="work_basis_problem_description"
            >
              <el-input
                v-model="form.work_basis_problem_description"
                type="textarea"
                placeholder="请输入工作依据或问题描述"
                :autosize="{ minRows: 3, maxRows: 6 }"
                :maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="工作内容" prop="work_content">
              <el-input
                v-model="form.work_content"
                type="textarea"
                placeholder="请输入工作内容"
                :autosize="{ minRows: 3, maxRows: 6 }"
                :maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="工作执行结果" prop="work_execution_result">
              <el-input
                v-model="form.work_execution_result"
                type="textarea"
                placeholder="请输入工作执行结果"
                :autosize="{ minRows: 3, maxRows: 6 }"
                :maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { createInst, updateInst, searchAllInst } from '@/api/cmdb';
  import { searchBusiness } from '@/api/cmdb';

  defineOptions({ name: 'MaintenanceServiceRecordEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'maintenance_service_record';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 维保人员选项 */
  const staffOptions = ref([]);
  const staffLoading = ref(false);

  /** 系统选项 */
  const systemOptions = ref([]);
  const systemLoading = ref(false);

  /** 合同选项 */
  const contractOptions = ref([]);
  const contractLoading = ref(false);

  /** 表单数据 */
  const form = ref({
    maintenance_staff: '',
    associated_system: '',
    associated_contract: '',
    work_category: '',
    work_basis_problem_description: '',
    work_content: '',
    work_execution_result: '',
    work_completion_time: null,
    result_verification_staff: ''
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {
      maintenance_staff: [
        { required: true, message: '请选择维保人员', trigger: 'change' }
      ],
      associated_system: [
        { required: true, message: '请选择关联系统', trigger: 'change' }
      ],
      work_category: [
        { required: true, message: '请选择工作类别', trigger: 'change' }
      ]
    };
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.maintenance_staff}_${data.work_category}_${new Date().getTime()}`
            }
          });
          EleMessage.success('修改成功');
        } else {
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.maintenance_staff}_${data.work_category}_${new Date().getTime()}`
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
  };

  /** 加载维保人员数据 */
  const loadMaintenanceStaff = async () => {
    if (staffOptions.value.length > 0) return;

    staffLoading.value = true;
    try {
      const formData = new FormData();
      formData.append('bkObjId', 'maintenance_staff_info');

      const res = await searchAllInst(formData);
      if (res && res.rows && Array.isArray(res.rows)) {
        staffOptions.value = res.rows.map((item) => ({
          label: item.maintenance_staff_name || item.bk_inst_name || '未知人员',
          value: item.maintenance_staff_name || item.bk_inst_name || '未知人员'
        }));
      } else {
        staffOptions.value = [];
      }
    } catch (error) {
      console.error('加载维保人员数据失败:', error);
      EleMessage.error(
        '加载维保人员数据失败: ' + (error.message || '未知错误')
      );
      staffOptions.value = [];
    } finally {
      staffLoading.value = false;
    }
  };

  /** 加载系统数据 */
  const loadSystems = async () => {
    if (systemOptions.value.length > 0) return;

    systemLoading.value = true;
    try {
      const res = await searchBusiness({});
      if (res && res.rows && Array.isArray(res.rows)) {
        systemOptions.value = res.rows.map((item) => ({
          label: item.bk_biz_name || '未知系统',
          value: item.bk_biz_name || item.bk_inst_name || '未知系统'
        }));
      } else {
        systemOptions.value = [];
      }
    } catch (error) {
      console.error('加载系统数据失败:', error);
      EleMessage.error('加载系统数据失败: ' + (error.message || '未知错误'));
      systemOptions.value = [];
    } finally {
      systemLoading.value = false;
    }
  };

  /** 加载合同数据 */
  const loadContracts = async () => {
    if (contractOptions.value.length > 0) return;

    contractLoading.value = true;
    try {
      const formData = new FormData();
      formData.append('bkObjId', 'maintenance_company_info');

      const res = await searchAllInst(formData);
      if (res && res.rows && Array.isArray(res.rows)) {
        contractOptions.value = res.rows.map((item) => ({
          label: item.maintained_project || item.bk_inst_name || '未知合同',
          value: item.maintained_project || item.bk_inst_name || '未知合同'
        }));
      } else {
        contractOptions.value = [];
      }
    } catch (error) {
      console.error('加载合同数据失败:', error);
      EleMessage.error('加载合同数据失败: ' + (error.message || '未知错误'));
      contractOptions.value = [];
    } finally {
      contractLoading.value = false;
    }
  };

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
      } else {
        // 新增模式，重置表单
        Object.assign(form.value, {
          maintenance_staff: '',
          associated_system: '',
          associated_contract: '',
          work_category: '',
          work_basis_problem_description: '',
          work_content: '',
          work_execution_result: '',
          work_completion_time: null,
          result_verification_staff: ''
        });
      }
    },
    { immediate: true }
  );

  /** 初始化时加载选项数据 */
  onMounted(() => {
    loadMaintenanceStaff();
    loadSystems();
    loadContracts();
  });
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  :deep(.el-divider) {
    margin: 16px 0;
  }

  :deep(.el-divider__text) {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }
</style>
