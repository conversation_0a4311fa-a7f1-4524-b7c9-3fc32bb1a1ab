<!-- 项目建设厂家管理编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑项目建设厂家' : '新建项目建设厂家'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="project_name">
              <el-select
                v-model="form.project_name"
                placeholder="请选择项目名称"
                style="width: 100%"
                :loading="projectLoading"
                filterable
                clearable
                @focus="loadProjects"
              >
                <el-option
                  v-for="project in projectOptions"
                  :key="project.value"
                  :label="project.label"
                  :value="project.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同名称" prop="contract_name">
              <el-input
                v-model="form.contract_name"
                placeholder="请输入合同名称"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="项目金额" prop="project_amount_cny">
              <el-input
                v-model="form.project_amount_cny"
                placeholder="请输入项目金额"
                clearable
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="免维期" prop="free_maintenance_duration">
              <el-input
                v-model="form.free_maintenance_duration"
                placeholder="请输入免维期"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="项目开始时间" prop="project_start_date">
              <el-date-picker
                v-model="form.project_start_date"
                type="datetime"
                placeholder="选择项目开始时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目验收时间" prop="project_acceptance_date">
              <el-date-picker
                v-model="form.project_acceptance_date"
                type="datetime"
                placeholder="选择项目验收时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="项目免维开始时间"
              prop="project_free_maintenance_start_date"
            >
              <el-date-picker
                v-model="form.project_free_maintenance_start_date"
                type="datetime"
                placeholder="选择项目免维开始时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="项目免维结束时间"
              prop="project_free_maintenance_end_time"
            >
              <el-date-picker
                v-model="form.project_free_maintenance_end_time"
                type="datetime"
                placeholder="选择项目免维结束时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="运维是否提醒" prop="is_operation_reminder">
              <el-radio-group v-model="form.is_operation_reminder">
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="合同是否有维保信息"
              prop="has_maintenance_info_in_contract"
            >
              <el-radio-group v-model="form.has_maintenance_info_in_contract">
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="免维方式" prop="free_maintenance_type">
              <el-select
                v-model="form.free_maintenance_type"
                placeholder="请选择免维方式"
                style="width: 100%"
                multiple
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option label="远程" value="远程" />
                <el-option label="现场" value="现场" />
                <el-option label="驻场" value="驻场" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 维保费用信息 -->
        <div class="form-section-title">维保费用信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="合同约定维保费用"
              prop="contract_maintenance_fee"
            >
              <el-input
                v-model.number="form.contract_maintenance_fee"
                placeholder="请输入合同约定维保费用"
                type="number"
                clearable
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保比例" prop="maintenance_ratio">
              <el-input
                v-model.number="form.maintenance_ratio"
                placeholder="请输入维保比例"
                type="number"
                clearable
              >
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 乙方信息 -->
        <div class="form-section-title">乙方信息</div>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="乙方公司" prop="party_b_company">
              <el-input
                v-model="form.party_b_company"
                placeholder="请输入乙方公司"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="乙方负责人" prop="contract_b_owner">
              <el-input
                v-model="form.contract_b_owner"
                placeholder="请输入乙方负责人"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人联系方式" prop="contract_b_tel">
              <el-input
                v-model="form.contract_b_tel"
                placeholder="请输入乙方负责人联系方式"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 业务负责人信息 -->
        <div class="form-section-title">业务负责人信息</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="业务负责人" prop="app_onwer">
              <el-input
                v-model="form.app_onwer"
                placeholder="请输入业务负责人"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人联系方式" prop="app_owner_tel">
              <el-input
                v-model="form.app_owner_tel"
                placeholder="请输入业务负责人联系方式"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 科创负责人信息 -->
        <div class="form-section-title">科创负责人信息</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="科创负责人" prop="tech_innovation_owner">
              <el-input
                v-model="form.tech_innovation_owner"
                placeholder="请输入科创负责人"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="负责人联系方式"
              prop="tech_innovation_owner_tel"
            >
              <el-input
                v-model="form.tech_innovation_owner_tel"
                placeholder="请输入科创负责人联系方式"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 信息技术部负责人信息 -->
        <div class="form-section-title">信息技术部负责人信息</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="信息技术部负责人" prop="tech_department_owner">
              <el-input
                v-model="form.tech_department_owner"
                placeholder="请输入信息技术部负责人"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="负责人联系方式"
              prop="tech_department_owner_tel"
            >
              <el-input
                v-model="form.tech_department_owner_tel"
                placeholder="请输入信息技术部负责人联系方式"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { createInst, updateInst, searchAllInst } from '@/api/cmdb';

  defineOptions({ name: 'ProjectConstructionManufacturerEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'project_construction_manufacturer';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 项目选项 */
  const projectOptions = ref([]);
  const projectLoading = ref(false);

  /** 表单数据 */
  const form = ref({
    project_name: '',
    contract_name: '',
    project_amount_cny: null,
    project_start_date: null,
    project_acceptance_date: null,
    project_free_maintenance_start_date: null,
    project_free_maintenance_end_time: null,
    is_operation_reminder: '',
    free_maintenance_duration: null,
    party_b_company: '',
    contract_b_owner: '',
    contract_b_tel: '',
    app_onwer: '',
    app_owner_tel: '',
    tech_innovation_owner: '',
    tech_innovation_owner_tel: '',
    tech_department_owner: '',
    tech_department_owner_tel: '',
    has_maintenance_info_in_contract: '',
    free_maintenance_type: [],
    contract_maintenance_fee: null,
    maintenance_ratio: null
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {
      project_name: [
        { required: true, message: '请选择项目名称', trigger: 'change' }
      ],
      contract_name: [
        { required: true, message: '请输入合同名称', trigger: 'blur' }
      ]
    };
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        // 处理免维方式数组转字符串
        if (Array.isArray(data.free_maintenance_type)) {
          data.free_maintenance_type = data.free_maintenance_type.join(',');
        }

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.project_name}_${data.contract_name}`
            }
          });
          EleMessage.success('修改成功');
        } else {
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.project_name}_${data.contract_name}`
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
  };

  /** 加载项目数据 */
  const loadProjects = async () => {
    projectLoading.value = true;
    try {
      const formData = new FormData();
      formData.append('bkObjId', 'project_detail');

      const res = await searchAllInst(formData);
      if (res && res.rows && Array.isArray(res.rows)) {
        projectOptions.value = res.rows.map((item) => ({
          label: item.project_name || item.bk_inst_name || '未知项目',
          value: item.project_name || item.bk_inst_name || '未知项目'
        }));
      } else {
        projectOptions.value = [];
      }
    } catch (error) {
      console.error('加载项目数据失败:', error);
      EleMessage.error('加载项目数据失败: ' + (error.message || '未知错误'));
      projectOptions.value = [];
    } finally {
      projectLoading.value = false;
    }
  };

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
        // 处理免维方式字符串转数组
        if (
          value.free_maintenance_type &&
          typeof value.free_maintenance_type === 'string'
        ) {
          form.value.free_maintenance_type = value.free_maintenance_type
            .split(',')
            .filter((item) => item.trim());
        }
      } else {
        // 新增模式，重置表单
        Object.assign(form.value, {
          project_name: '',
          contract_name: '',
          project_amount_cny: null,
          project_start_date: null,
          project_acceptance_date: null,
          project_free_maintenance_start_date: null,
          project_free_maintenance_end_time: null,
          is_operation_reminder: '',
          free_maintenance_duration: '',
          party_b_company: '',
          contract_b_owner: '',
          contract_b_tel: '',
          app_onwer: '',
          app_owner_tel: '',
          tech_innovation_owner: '',
          tech_innovation_owner_tel: '',
          tech_department_owner: '',
          tech_department_owner_tel: '',
          has_maintenance_info_in_contract: '',
          free_maintenance_type: [],
          contract_maintenance_fee: null,
          maintenance_ratio: null
        });
      }
    },
    { immediate: true }
  );

  /** 初始化时加载项目数据 */
  onMounted(() => {
    loadProjects();
  });
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  :deep(.el-divider) {
    margin: 16px 0;
  }

  :deep(.el-divider__text) {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }
</style>
