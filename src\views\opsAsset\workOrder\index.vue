<template>
  <ele-page>
    <!-- 搜索组件 -->
    <simple-search
      :fields="searchFields"
      :default-field="defaultField"
      :advanced-conditions="advancedConditions"
      @search="handleSimpleSearch"
      @reset="handleSearchReset"
      @advanced-search="showAdvancedModal"
      @clear-advanced="clearAdvancedConditions"
      @remove-advanced-condition="removeAdvancedCondition"
    />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <cmdb-pro-table
        ref="tableRef"
        row-key="bk_inst_id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '工单管理' }"
        cache-key="workOrderTable"
      >
        <template #action="{ row }">
          <el-link type="info" underline="never" @click="openDetail(row)">
            详情
          </el-link>
          <el-divider direction="vertical" />

          <el-link type="primary" underline="never" @click="openEdit(row)">
            修改
          </el-link>
        </template>
      </cmdb-pro-table>
    </ele-card>

    <!-- 详情弹窗 -->
    <work-order-detail :data="currentDetail" v-model="showDetail" />

    <!-- 高级搜索弹窗 -->
    <advanced-search
      v-model="showAdvanced"
      :search-fields="searchFields"
      :initial-conditions="advancedConditions"
      @search="handleAdvancedSearch"
      @close="closeAdvancedModal"
    />

    <!-- 编辑弹窗 -->
    <work-order-edit :data="current" v-model="showEdit" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { searchInst } from '@/api/cmdb';
  import SimpleSearch from '@/components/Search/simple-search.vue';
  import AdvancedSearch from '@/components/Search/advanced-search.vue';
  import WorkOrderEdit from './components/work-order-edit.vue';
  import WorkOrderDetail from './components/work-order-detail.vue';

  defineOptions({ name: 'WorkOrderManage' });

  /** 模型实例ID */
  const bkObjId = 'tickets';
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格选中数据 */
  const selections = ref([]);
  /** 当前编辑数据 */
  const current = ref(null);
  /** 当前详情数据 */
  const currentDetail = ref(null);
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 是否显示详情弹窗 */
  const showDetail = ref(false);
  /** 是否显示高级搜索弹窗 */
  const showAdvanced = ref(false);

  /** 表格列配置 */
  const columns = computed(() => [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'tickets_name',
      label: '工单名称',
      align: 'center',
      minWidth: 150,
      fixed: 'left'
    },
    {
      prop: 'tickets_applicant',
      label: '创建人',
      align: 'center',
      width: 120
    },
    {
      prop: 'system_name',
      label: '系统名称',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'tickets_endtime',
      label: '终止时间',
      align: 'center',
      width: 180,
      formatter: (row) => formatTime(row.tickets_endtime)
    },
    {
      prop: 'reality_start_date',
      label: '上线时间',
      align: 'center',
      width: 180,
      formatter: (row) => formatTime(row.reality_start_date)
    },
    {
      prop: 'ticket_create_time',
      label: '创建时间',
      align: 'center',
      width: 180,
      formatter: (row) => formatTime(row.ticket_create_time)
    },
    {
      prop: 'tickets_status',
      label: '工单状态',
      align: 'center',
      width: 100
    },
    {
      prop: 'tickets_spendtime',
      label: '耗时时间',
      align: 'center',
      width: 120,
      formatter: (row) => formatSpendTime(row.tickets_spendtime)
    },
    {
      prop: 'flow_instance_id',
      label: '流程ID',
      align: 'center',
      minWidth: 180
    },
    {
      prop: 'department_name',
      label: '部门名称',
      align: 'center',
      minWidth: 150
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 140,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 搜索字段配置 */
  const searchFields = ref([
    { prop: 'tickets_name', label: '工单名称', type: 'text' },
    { prop: 'tickets_applicant', label: '创建人', type: 'text' },
    { prop: 'system_name', label: '系统名称', type: 'text' },
    { prop: 'department_name', label: '部门名称', type: 'text' },
    { prop: 'flow_instance_id', label: '流程ID', type: 'text' },
    { prop: 'tickets_status', label: '工单状态', type: 'text' },
    { prop: 'tickets_endtime', label: '终止时间', type: 'date' },
    { prop: 'reality_start_date', label: '上线时间', type: 'date' },
    { prop: 'ticket_create_time', label: '创建时间', type: 'date' }
  ]);

  /** 默认搜索字段 */
  const defaultField = ref({
    prop: 'tickets_name',
    label: '工单名称'
  });

  /** 高级搜索条件 */
  const advancedConditions = ref([]);

  /** 当前搜索条件 */
  const currentSearchParams = ref({});

  /** 数据源 */
  const datasource = async ({ pages }) => {
    let conditions = [];

    // 处理简单搜索条件
    if (currentSearchParams.value.condition) {
      Object.keys(currentSearchParams.value.condition).forEach((field) => {
        const value = currentSearchParams.value.condition[field];
        if (typeof value === 'object' && value !== null) {
          // 处理操作符对象格式，例如 { $regex: "keyword" }
          Object.keys(value).forEach((operator) => {
            conditions.push({
              field: field,
              operator: operator,
              value: value[operator]
            });
          });
        } else {
          // 处理直接值格式
          conditions.push({
            field: field,
            operator: '$eq',
            value: value
          });
        }
      });
    }

    // 添加高级搜索条件（已经是正确格式）
    if (advancedConditions.value.length > 0) {
      conditions = conditions.concat(
        advancedConditions.value.filter(
          (condition) =>
            condition.field &&
            condition.operator &&
            condition.value !== '' &&
            condition.value !== null &&
            condition.value !== undefined
        )
      );
    }

    const page = {
      start: (pages.pageNum - 1) * pages.pageSize,
      limit: pages.pageSize,
      sort: 'bk_inst_id'
    };

    // 按照新的参考格式组织条件参数
    const conditionParam =
      conditions.length > 0
        ? {
            [bkObjId]: conditions
          }
        : undefined;

    const res = await searchInst({
      bk_obj_id: bkObjId,
      condition: conditionParam,
      page
    });

    return {
      code: res.code,
      msg: res.msg,
      rows: res.rows,
      total: res.total
    };
  };

  /** 处理简单搜索 */
  const handleSimpleSearch = (params) => {
    currentSearchParams.value = params;
    reload();
  };

  /** 处理搜索重置 */
  const handleSearchReset = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    reload();
  };

  /** 显示高级搜索弹窗 */
  const showAdvancedModal = () => {
    showAdvanced.value = true;
  };

  /** 关闭高级搜索弹窗 */
  const closeAdvancedModal = () => {
    showAdvanced.value = false;
  };

  /** 处理高级搜索 */
  const handleAdvancedSearch = (conditions) => {
    advancedConditions.value = conditions;
    reload();
  };

  /** 清空高级搜索条件 */
  const clearAdvancedConditions = () => {
    advancedConditions.value = [];
    reload();
  };

  /** 移除高级搜索条件 */
  const removeAdvancedCondition = (index) => {
    advancedConditions.value.splice(index, 1);
    reload();
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row;
    showEdit.value = true;
  };

  /** 打开详情弹窗 */
  const openDetail = (row) => {
    currentDetail.value = row;
    showDetail.value = true;
  };

  /** 刷新表格 */
  const reload = (params) => {
    tableRef.value?.reload?.(params);
  };

  /** 格式化时间 */
  const formatTime = (timeValue) => {
    if (!timeValue) return '-';
    try {
      const date = new Date(timeValue);
      if (isNaN(date.getTime())) return '-';
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return '-';
    }
  };

  /** 格式化耗时时间 */
  const formatSpendTime = (spendTime) => {
    if (!spendTime || spendTime <= 0) return '-';

    const hours = Math.floor(spendTime / 3600);
    const minutes = Math.floor((spendTime % 3600) / 60);

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟`;
    } else {
      return `${spendTime}秒`;
    }
  };
</script>

<style scoped>
  /* 工单管理样式 */
</style>
