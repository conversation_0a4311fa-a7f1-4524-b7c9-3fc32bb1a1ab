<template>
  <ele-card shadow="never" :body-style="{ padding: '16px' }">
    <el-form
      ref="searchFormRef"
      :model="searchForm"
      label-width="0px"
      class="ele-form-search"
      @keyup.enter="handleSearch"
      @submit.prevent
    >
      <div class="search-inline-container">
        <el-form-item class="field-select-item">
          <el-select
            v-model="selectedField"
            placeholder="选择字段"
            style="width: 180px"
          >
            <el-option
              v-for="field in fields"
              :key="field.prop"
              :label="field.label"
              :value="field.prop"
            />
          </el-select>
        </el-form-item>

        <el-form-item style="margin-left: 5px">
          <el-input
            v-model="searchForm.keyword"
            :placeholder="`请输入${currentField.label}`"
            clearable
            @clear="handleSearch"
            style="width: 240px"
          >
            <!-- <template #prefix>
              <el-icon><Search /></el-icon>
            </template> -->
          </el-input>
        </el-form-item>

        <div class="ele-form-actions">
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="SearchOutlined"
            @click="handleSearch"
          >
            搜索
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="ReloadOutlined"
            @click="handleReset"
          >
            重置
          </el-button>
          <el-button
            type="warning"
            class="ele-btn-icon"
            :icon="SettingOutlined"
            @click="showAdvancedSearch"
          >
            高级搜索
          </el-button>
        </div>
      </div>

      <!-- 筛选标签行 -->
      <div v-if="hasActiveFilters" class="filter-tags-section">
        <div class="filter-tags-container">
          <span class="filter-label">筛选条件：</span>
          <div class="filter-tags">
            <el-tag
              v-for="tag in filterTags"
              :key="tag.key"
              :type="tag.type"
              closable
              @close="removeFilter(tag)"
              class="filter-tag"
            >
              {{ tag.text }}
            </el-tag>
            <el-button
              type="text"
              size="small"
              @click="clearAllFilters"
              class="clear-all-btn"
            >
              <el-icon><Delete /></el-icon>
              清空所有条件
            </el-button>
          </div>
        </div>
      </div>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import {
    SearchOutlined,
    ReloadOutlined,
    SettingOutlined
  } from '@/components/icons';
  import {
    Search,
    RefreshLeft,
    Setting,
    Delete
  } from '@element-plus/icons-vue';

  defineOptions({ name: 'SimpleSearch' });

  const props = defineProps({
    /** 搜索字段配置 */
    fields: {
      type: Array,
      required: true,
      default: () => []
    },
    /** 默认搜索字段 */
    defaultField: {
      type: Object,
      required: true,
      default: () => ({ prop: '', label: '' })
    },
    /** 高级搜索条件 */
    advancedConditions: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits([
    'search',
    'reset',
    'advanced-search',
    'clear-advanced',
    'remove-advanced-condition'
  ]);

  /** 搜索表单引用 */
  const searchFormRef = ref(null);

  /** 选中的搜索字段 */
  const selectedField = ref('');

  /** 简单搜索表单 */
  const searchForm = ref({
    keyword: ''
  });

  /** 当前搜索字段 */
  const currentField = computed(() => {
    if (selectedField.value) {
      const field = props.fields.find((f) => f.prop === selectedField.value);
      return field || { prop: '', label: '关键词' };
    }
    return props.defaultField || { prop: '', label: '关键词' };
  });

  /** 初始化默认字段 */
  watch(
    () => props.defaultField,
    (newField) => {
      if (newField && !selectedField.value) {
        selectedField.value = newField.prop;
      }
    },
    { immediate: true }
  );

  /** 筛选标签 */
  const filterTags = ref([]);

  /** 是否有活跃的筛选条件 */
  const hasActiveFilters = computed(() => {
    return filterTags.value.length > 0;
  });

  /** 获取操作符显示文本 */
  const getOperatorText = (operator) => {
    const operatorMap = {
      $regex: '模糊匹配',
      $eq: '等于',
      $ne: '不等于',
      $gt: '大于',
      $lt: '小于',
      $gte: '大于等于',
      $lte: '小于等于',
      $in: '包含于',
      $nin: '不包含于'
    };
    return operatorMap[operator] || operator;
  };

  /** 更新高级搜索标签 */
  const updateAdvancedTags = (conditions) => {
    // 清除之前的高级搜索标签
    filterTags.value = filterTags.value.filter((tag) => !tag.isAdvanced);

    if (!conditions || conditions.length === 0) {
      return;
    }

    // 添加高级搜索标签
    conditions.forEach((condition, index) => {
      const field = props.fields.find((f) => f.prop === condition.field);
      const fieldLabel = field?.label || condition.field;
      const operatorText = getOperatorText(condition.operator);

      let valueText = condition.value;
      if (field && field.options) {
        const option = field.options.find(
          (opt) => opt.value === condition.value
        );
        valueText = option ? option.label : condition.value;
      }

      const displayText = `${fieldLabel} ${operatorText} ${valueText}`;

      filterTags.value.push({
        key: `advanced_${index}`,
        text: displayText,
        type: 'success',
        isAdvanced: true,
        condition: condition
      });
    });
  };

  /** 监听高级搜索条件变化 */
  watch(
    () => props.advancedConditions,
    (newConditions) => {
      updateAdvancedTags(newConditions);
    },
    { immediate: true, deep: true }
  );

  /** 搜索 */
  const handleSearch = () => {
    const params = buildSearchParams();
    updateSimpleTag();
    emit('search', params);
  };

  /** 构建搜索参数 */
  const buildSearchParams = () => {
    if (!searchForm.value.keyword || !selectedField.value) return {};

    return {
      condition: {
        [selectedField.value]: { $regex: searchForm.value.keyword }
      }
    };
  };

  /** 更新简单搜索标签 */
  const updateSimpleTag = () => {
    // 清除之前的简单搜索标签
    filterTags.value = filterTags.value.filter((tag) => !tag.isSimple);

    if (searchForm.value.keyword && selectedField.value) {
      filterTags.value.unshift({
        key: 'simple_search',
        text: `${currentField.value.label} 模糊匹配 ${searchForm.value.keyword}`,
        type: 'primary',
        isSimple: true
      });
    }
  };

  /** 重置 */
  const handleReset = () => {
    searchFormRef.value?.resetFields();
    searchForm.value = { keyword: '' };
    selectedField.value = props.defaultField?.prop || '';
    filterTags.value = [];
    emit('reset');
  };

  /** 显示高级搜索 */
  const showAdvancedSearch = () => {
    emit('advanced-search');
  };

  /** 移除筛选条件 */
  const removeFilter = (tag) => {
    const index = filterTags.value.findIndex((t) => t.key === tag.key);
    if (index > -1) {
      filterTags.value.splice(index, 1);

      if (tag.isSimple) {
        searchForm.value.keyword = '';
        handleSearch();
      } else if (tag.isAdvanced && tag.condition) {
        emit('remove-advanced-condition', tag.condition);
      }
    }
  };

  /** 清空所有筛选条件 */
  const clearAllFilters = () => {
    searchForm.value.keyword = '';
    filterTags.value = [];
    emit('clear-advanced');
  };

  /** 暴露方法给父组件 */
  defineExpose({
    reset: handleReset,
    getSearchParams: buildSearchParams
  });
</script>

<style lang="scss" scoped>
  .ele-form-search {
    .el-form-item {
      margin-bottom: 0;
      margin-right: 0;
    }
  }

  .search-inline-container {
    display: flex;
    align-items: center;
    gap: 0;
    flex-wrap: wrap;
    margin-bottom: 0;

    .field-select-item {
      margin-right: -1px; // 边框重叠
      z-index: 2;

      :deep(.el-select) {
        .el-input {
          .el-input__wrapper {
            border-top-right-radius: 0 !important;
            border-bottom-right-radius: 0 !important;
          }
        }
      }
    }

    .search-input-item {
      z-index: 1;

      :deep(.el-input) {
        .el-input__wrapper {
          border-top-left-radius: 10 !important;
          border-bottom-left-radius: 10 !important;
        }
      }
    }
  }

  .ele-form-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 12px;
    flex-wrap: wrap;

    .el-button {
      display: flex;
      align-items: center;
      gap: 4px;
      margin: 0;
      height: 32px; // 与输入框保持同高

      .el-icon {
        font-size: 14px;
      }
    }
  }

  .filter-tags-section {
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .filter-tags-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-label {
    color: var(--el-text-color-regular);
    font-size: 14px;
    white-space: nowrap;
    margin-right: 4px;
    font-weight: 500;
  }

  .filter-tags {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-tag {
    margin: 0;
    font-size: 12px;
  }

  .clear-all-btn {
    color: var(--el-color-danger);
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0;
    padding: 4px 8px;
    height: auto;

    &:hover {
      color: var(--el-color-danger-light-3);
      background-color: var(--el-color-danger-light-9);
    }

    .el-icon {
      font-size: 12px;
    }
  }

  // 响应式适配
  @media (max-width: 1200px) {
    .search-inline-container {
      .field-select-item .el-select {
        width: 150px !important;
      }

      .search-input-item .el-input {
        width: 200px !important;
      }
    }

    .ele-form-actions {
      margin-left: 10px;
    }
  }

  @media (max-width: 992px) {
    .search-inline-container {
      gap: 8px;

      .field-select-item {
        margin-right: 0;

        :deep(.el-select) {
          width: 140px !important;

          .el-input {
            .el-input__wrapper {
              border-radius: var(--el-input-border-radius) !important;
            }
          }
        }
      }

      .search-input-item {
        :deep(.el-input) {
          width: 180px !important;

          .el-input__wrapper {
            border-radius: var(--el-input-border-radius) !important;
          }
        }
      }
    }

    .ele-form-actions {
      margin-left: 8px;

      .el-button {
        font-size: 12px;
        padding: 6px 12px;
        height: 30px;
      }
    }
  }

  @media (max-width: 768px) {
    :deep(.ele-card) {
      .el-card__body {
        padding: 12px !important;
      }
    }

    .search-inline-container {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      margin-bottom: 8px;

      .field-select-item,
      .search-input-item {
        margin-right: 0;

        :deep(.el-select),
        :deep(.el-input) {
          width: 100% !important;

          .el-input__wrapper {
            border-radius: var(--el-input-border-radius) !important;
          }
        }
      }
    }

    .ele-form-actions {
      margin-left: 0;
      justify-content: space-between;
      gap: 8px;

      .el-button {
        flex: 1;
        min-width: 70px;
        font-size: 12px;
        padding: 8px 10px;
        height: 36px;
      }
    }

    .filter-tags-section {
      margin-top: 12px;
      padding-top: 8px;
    }

    .filter-tags-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .filter-tags {
      width: 100%;
      justify-content: flex-start;
    }
  }

  @media (max-width: 480px) {
    :deep(.ele-card) {
      .el-card__body {
        padding: 8px !important;
      }
    }

    .search-inline-container {
      gap: 8px;
    }

    .ele-form-actions {
      .el-button {
        font-size: 11px;
        padding: 6px 8px;
        height: 32px;
      }
    }

    .filter-label {
      font-size: 13px;
    }

    .filter-tag {
      font-size: 11px;
    }
  }
</style>

<!-- 全局样式覆盖，确保圆角效果 -->
<style lang="scss">
  .search-inline-container {
    .field-select-item {
      .el-select .el-input .el-input__wrapper {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
      }

      .el-select .el-input .el-input__inner {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
      }
    }

    .search-input-item {
      .el-input .el-input__wrapper {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }

      .el-input .el-input__inner {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }
    }
  }

  // 在小屏幕时恢复正常圆角
  @media (max-width: 992px) {
    .search-inline-container {
      .field-select-item {
        .el-select .el-input .el-input__wrapper,
        .el-select .el-input .el-input__inner {
          border-radius: var(--el-input-border-radius) !important;
        }
      }

      .search-input-item {
        .el-input .el-input__wrapper,
        .el-input .el-input__inner {
          border-radius: var(--el-input-border-radius) !important;
        }
      }
    }
  }
</style>
