{"name": "asset-master-v2", "version": "1.4.0", "type": "module", "private": true, "scripts": {"dev": "vite --host", "serve": "vite build && vite preview --host", "build": "vite build", "serve:staging": "vite build --mode staging && vite preview --host", "build:staging": "vite build --mode staging", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "1.0.1", "@ant-design/colors": "7.2.1", "@bytemd/plugin-gfm": "1.22.0", "@bytemd/plugin-highlight": "1.22.0", "@element-plus/icons-vue": "2.3.1", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vueuse/core": "13.3.0", "axios": "1.9.0", "bytemd": "1.22.0", "countup.js": "2.8.2", "cropperjs": "1.6.2", "dayjs": "1.11.13", "echarts": "5.6.0", "element-plus": "2.9.10", "exceljs": "4.4.0", "github-markdown-css": "5.8.1", "highlight.js": "11.11.1", "jsbarcode": "3.11.6", "jszip": "3.10.1", "lodash-es": "4.17.21", "monaco-editor": "0.47.0", "nprogress": "0.2.0", "pinia": "3.0.2", "sortablejs": "1.15.6", "tinymce": "5.10.9", "vue": "3.5.15", "vue-echarts": "7.0.3", "vue-router": "4.5.1", "vuedraggable": "4.1.0", "xgplayer": "3.0.22"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.2.0", "@vue/compiler-sfc": "3.5.15", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.32.0", "postcss": "8.5.3", "prettier": "3.5.3", "rimraf": "6.0.1", "sass": "1.89.0", "unplugin-vue-components": "28.7.0", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vue-eslint-parser": "9.4.3"}}