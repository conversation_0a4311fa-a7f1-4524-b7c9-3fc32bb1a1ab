<template>
  <ele-page>
    <!-- 搜索组件 -->
    <simple-search
      :fields="searchFields"
      :default-field="defaultField"
      :advanced-conditions="advancedConditions"
      @search="handleSimpleSearch"
      @reset="handleSearchReset"
      @advanced-search="showAdvancedModal"
      @clear-advanced="clearAdvancedConditions"
      @remove-advanced-condition="removeAdvancedCondition"
    />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <cmdb-pro-table
        ref="tableRef"
        row-key="bk_inst_id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '主机资源管理' }"
        cache-key="hostResourceTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="handleCreate()"
          >
            新建
          </el-button>
          <el-button
            type="success"
            class="ele-btn-icon"
            :icon="UploadOutlined"
            @click="handleImport()"
          >
            导入
          </el-button>
          <el-button
            type="warning"
            class="ele-btn-icon"
            :icon="EditOutlined"
            :disabled="!selections.length"
            @click="handleBatchEdit()"
          >
            批量编辑
          </el-button>
          <el-button
            type="danger"
            class="ele-btn-icon hidden-sm-and-down"
            :icon="DeleteOutlined"
            :disabled="!selections.length"
            @click="handleBatchDelete()"
          >
            批量删除
          </el-button>
        </template>

        <template #device_role="{ row }">
          <el-tag :type="getDeviceRoleType(row.device_role)" size="small">
            {{ getDeviceRoleText(row.device_role) }}
          </el-tag>
        </template>

        <template #monitor_status="{ row }">
          <el-tag :type="getMonitorStatusType(row.monitor_status)" size="small">
            {{ getMonitorStatusText(row.monitor_status) }}
          </el-tag>
        </template>

        <template #environment_code="{ row }">
          <el-tag :type="getEnvironmentType(row.environment_code)" size="small">
            {{ getEnvironmentText(row.environment_code) }}
          </el-tag>
        </template>

        <template #lifecycle_code="{ row }">
          <el-tag :type="getLifecycleType(row.lifecycle_code)" size="small">
            {{ getLifecycleText(row.lifecycle_code) }}
          </el-tag>
        </template>

        <template #action="{ row }">
          <el-link type="info" underline="false" @click="handleViewDetail(row)">
            详情
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" underline="false" @click="handleEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" underline="false" @click="handleDelete(row)">
            删除
          </el-link>
        </template>
      </cmdb-pro-table>
    </ele-card>

    <!-- 高级搜索弹窗 -->
    <advanced-search
      v-model="showAdvanced"
      :search-fields="searchFields"
      :initial-conditions="advancedConditions"
      @search="handleAdvancedSearch"
      @close="closeAdvancedModal"
    />

    <!-- 主机详情抽屉 -->
    <HostDetail v-model="detailVisible" :data="currentHostDetail" />

    <!-- 主机编辑抽屉 -->
    <HostEdit
      v-model="editVisible"
      :host-data="currentHost"
      @done="handleRefresh"
    />

    <!-- 主机批量编辑抽屉 -->
    <HostBatchEdit
      v-model="batchEditVisible"
      :selected-records="selections"
      @done="handleRefresh"
    />

    <!-- 主机导入弹窗 -->
    <HostImport v-model="importVisible" @done="handleRefresh" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import {
    PlusOutlined,
    DeleteOutlined,
    UploadOutlined,
    EditOutlined
  } from '@/components/icons';
  import { searchInst, deleteInst, batchDeleteInst } from '@/api/cmdb';
  import SimpleSearch from '@/components/Search/simple-search.vue';
  import AdvancedSearch from '@/components/Search/advanced-search.vue';
  import HostDetail from './components/host-detail.vue';
  import HostEdit from './components/host-edit.vue';
  import HostBatchEdit from './components/host-batch-edit.vue';
  import HostImport from './components/host-import.vue';

  defineOptions({ name: 'HostManage' });

  const bkObjId = 'host_resource';

  // 表格选中数据
  const selections = ref([]);
  const tableRef = ref(null);

  // 弹窗状态
  const editVisible = ref(false);
  const currentHost = ref(null);
  const showAdvanced = ref(false);
  const batchEditVisible = ref(false);
  const detailVisible = ref(false);
  const currentHostDetail = ref(null);
  const importVisible = ref(false);

  // 搜索相关
  const currentSearchParams = ref({});
  const advancedConditions = ref([]);

  // 搜索字段配置
  const searchFields = ref([
    { prop: 'host_name', label: '主机名称', type: 'text' },
    { prop: 'host_innerip', label: '内网IP', type: 'text' },
    {
      prop: 'device_role',
      label: '主机角色',
      type: 'select',
      options: [
        { label: '应用', value: 0 },
        { label: '数据库', value: 1 },
        { label: '中间件', value: 2 }
      ]
    },
    { prop: 'system_name', label: '系统名称', type: 'text' },
    { prop: 'project_name', label: '项目名称', type: 'text' },
    { prop: 'apply_department', label: '申请部门', type: 'text' },
    { prop: 'apply_date', label: '申请日期', type: 'daterange' },
    { prop: 'apply_username', label: '申请人', type: 'text' },
    { prop: 'apply_user_id', label: '申请人工号', type: 'text' },
    {
      prop: 'monitor_status',
      label: '监控状态',
      type: 'select',
      options: [
        { label: '正常', value: 0 },
        { label: '异常', value: 1 },
        { label: '未知', value: 2 }
      ]
    },
    {
      prop: 'environment_code',
      label: '设备环境',
      type: 'select',
      options: [
        { label: '生产', value: 0 },
        { label: '测试', value: 1 }
      ]
    },
    {
      prop: 'lifecycle_code',
      label: '使用状态',
      type: 'select',
      options: [
        { label: '已下线', value: 0 },
        { label: '在线', value: 1 }
      ]
    },
    { prop: 'technician_owner', label: '技术负责人', type: 'text' },
    { prop: 'technician_owner_id', label: '技术负责人ID', type: 'text' },
    { prop: 'create_date', label: '录入时间', type: 'daterange' },
    { prop: 'create_user', label: '录入人员', type: 'text' },
    { prop: 'disk', label: '数据盘大小', type: 'number' },
    { prop: 'total_disks', label: '磁盘总大小', type: 'number' },
    { prop: 'memory', label: '内存容量', type: 'number' },
    { prop: 'nas', label: 'NAS存储', type: 'number' },
    { prop: 'cpu', label: 'CPU逻辑核心数', type: 'number' },
    { prop: 'system_disk', label: '系统盘', type: 'number' },
    { prop: 'os_version', label: '操作系统版本', type: 'text' },
    { prop: 'remark', label: '备注信息', type: 'text' }
  ]);

  // 默认搜索字段
  const defaultField = ref({
    prop: 'host_innerip',
    label: '内网IP'
  });

  // 表格列配置
  const columns = computed(() => [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'host_name',
      label: '主机名称',
      width: 150,
      showOverflowTooltip: true,
      fixed: 'left'
    },
    {
      prop: 'host_innerip',
      label: '内网IP',
      width: 120,
      showOverflowTooltip: true,
      fixed: 'left'
    },

    {
      prop: 'system_name',
      label: '系统名称',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'project_name',
      label: '项目名称',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'cpu',
      label: 'CPU逻辑核心数',
      width: 130,
      formatter: (row) => (row.cpu ? `${row.cpu} 核` : '-')
    },
    {
      prop: 'memory',
      label: '内存容量',
      width: 120,
      formatter: (row) => (row.memory ? `${row.memory} GB` : '-')
    },
    {
      prop: 'total_disks',
      label: '磁盘总大小',
      width: 120,
      formatter: (row) => (row.total_disks ? `${row.total_disks} GB` : '-')
    },
    {
      prop: 'disk',
      label: '数据盘大小',
      width: 120,
      formatter: (row) => (row.disk ? `${row.disk} GB` : '-')
    },
    {
      prop: 'system_disk',
      label: '系统盘',
      width: 120,
      formatter: (row) => (row.system_disk ? `${row.system_disk} GB` : '-')
    },

    {
      prop: 'nas',
      label: 'NAS存储',
      width: 120,
      formatter: (row) => (row.nas ? `${row.nas} GB` : '-')
    },

    {
      prop: 'device_role',
      label: '主机角色',
      width: 100,
      align: 'center',
      slot: 'device_role'
    },
    {
      prop: 'environment_code',
      label: '设备环境',
      width: 100,
      align: 'center',
      slot: 'environment_code'
    },

    {
      prop: 'os_version',
      label: '操作系统版本',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'apply_department',
      label: '申请部门',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'apply_date',
      label: '申请日期',
      width: 160,
      formatter: (row) => formatDate(row.apply_date)
    },
    {
      prop: 'apply_username',
      label: '申请人',
      width: 100,
      showOverflowTooltip: true
    },
    {
      prop: 'apply_user_id',
      label: '申请人工号',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'technician_owner',
      label: '技术负责人',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'technician_owner_id',
      label: '技术负责人ID',
      width: 130,
      showOverflowTooltip: true
    },
    {
      prop: 'lifecycle_code',
      label: '使用状态',
      width: 100,
      align: 'center',
      slot: 'lifecycle_code'
    },
    {
      prop: 'monitor_status',
      label: '监控状态',
      width: 100,
      align: 'center',
      slot: 'monitor_status'
    },
    {
      prop: 'create_date',
      label: '录入时间',
      width: 160,
      formatter: (row) => formatDate(row.create_date)
    },
    {
      prop: 'create_user',
      label: '录入人员',
      width: 100,
      showOverflowTooltip: true
    },
    {
      prop: 'remark',
      label: '备注信息',
      width: 150,
      showOverflowTooltip: true
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  // 数据源函数
  const datasource = async ({ pages }) => {
    let conditions = [];

    // 处理简单搜索条件
    if (currentSearchParams.value.condition) {
      Object.keys(currentSearchParams.value.condition).forEach((field) => {
        const value = currentSearchParams.value.condition[field];
        if (typeof value === 'object' && value !== null) {
          // 处理操作符对象格式，例如 { $regex: "keyword" }
          Object.keys(value).forEach((operator) => {
            conditions.push({
              field: field,
              operator: operator,
              value: value[operator]
            });
          });
        } else {
          // 处理直接值格式
          conditions.push({
            field: field,
            operator: '$eq',
            value: value
          });
        }
      });
    }

    // 添加高级搜索条件（已经是正确格式）
    if (advancedConditions.value.length > 0) {
      conditions = conditions.concat(
        advancedConditions.value.filter(
          (condition) =>
            condition.field &&
            condition.operator &&
            condition.value !== '' &&
            condition.value !== null &&
            condition.value !== undefined
        )
      );
    }

    const page = {
      start: (pages.pageNum - 1) * pages.pageSize,
      limit: pages.pageSize,
      sort: 'bk_inst_id'
    };

    // 按照参考格式组织条件参数
    const conditionParam =
      conditions.length > 0
        ? {
            [bkObjId]: conditions
          }
        : undefined;

    const res = await searchInst({
      bk_obj_id: bkObjId,
      condition: conditionParam,
      page
    });

    return {
      code: res.code,
      msg: res.msg,
      rows: res.rows,
      total: res.total
    };
  };

  // 处理简单搜索
  const handleSimpleSearch = (params) => {
    currentSearchParams.value = params;
    handleRefresh();
  };

  // 处理搜索重置
  const handleSearchReset = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    handleRefresh();
  };

  // 显示高级搜索弹窗
  const showAdvancedModal = () => {
    showAdvanced.value = true;
  };

  // 关闭高级搜索弹窗
  const closeAdvancedModal = () => {
    showAdvanced.value = false;
  };

  // 处理高级搜索
  const handleAdvancedSearch = (conditions) => {
    advancedConditions.value = conditions;
    handleRefresh();
  };

  /** 清空高级搜索条件 */
  const clearAdvancedConditions = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    selections.value = [];
    handleRefresh();
  };

  /** 移除单个高级搜索条件 */
  const removeAdvancedCondition = (condition) => {
    const index = advancedConditions.value.findIndex(
      (c) =>
        c.field === condition.field &&
        c.operator === condition.operator &&
        c.value === condition.value
    );
    if (index > -1) {
      advancedConditions.value.splice(index, 1);
      selections.value = [];
      handleRefresh();
    }
  };

  // 新增主机
  const handleCreate = () => {
    currentHost.value = null;
    editVisible.value = true;
  };

  // 查看详情
  const handleViewDetail = (row) => {
    currentHostDetail.value = row;
    detailVisible.value = true;
  };

  // 编辑主机
  const handleEdit = (row) => {
    currentHost.value = row;
    editVisible.value = true;
  };

  // 批量编辑主机
  const handleBatchEdit = () => {
    if (!selections.value.length) {
      EleMessage.warning('请选择要编辑的主机');
      return;
    }
    batchEditVisible.value = true;
  };

  // 导入主机
  const handleImport = () => {
    importVisible.value = true;
  };

  // 删除主机
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确定要删除主机"${row.host_name}"吗？`, '确认删除', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        await deleteInst({
          bkObjId,
          instId: row.bk_inst_id
        });
        EleMessage.success('删除成功');
        handleRefresh();
      } catch (e) {
        EleMessage.error(e.message || '删除失败');
      }
    });
  };

  // 批量删除
  const handleBatchDelete = () => {
    const selectedNames = selections.value
      .map((item) => item.host_name)
      .join('、');
    ElMessageBox.confirm(
      `确定要删除选中的 ${selections.value.length} 个主机吗？\n主机名称：${selectedNames}`,
      '确认批量删除',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      try {
        const instIds = selections.value.map((item) => item.bk_inst_id);
        await batchDeleteInst({ bkObjId, instIds });
        EleMessage.success('批量删除成功');
        handleRefresh();
      } catch (e) {
        EleMessage.error(e.message || '批量删除失败');
      }
    });
  };

  // 刷新数据
  const handleRefresh = () => {
    tableRef.value?.reload?.();
  };

  // 获取主机角色标签类型
  const getDeviceRoleType = (role) => {
    const roleTypes = {
      0: 'primary',
      1: 'success',
      2: 'warning'
    };
    return roleTypes[role] || 'info';
  };

  // 获取主机角色文本
  const getDeviceRoleText = (role) => {
    const roleTexts = {
      0: '应用',
      1: '数据库',
      2: '中间件'
    };
    return roleTexts[role] || '未知';
  };

  // 获取监控状态标签类型
  const getMonitorStatusType = (status) => {
    const statusTypes = {
      0: 'success',
      1: 'danger',
      2: 'warning'
    };
    return statusTypes[status] || 'info';
  };

  // 获取监控状态文本
  const getMonitorStatusText = (status) => {
    const statusTexts = {
      0: '正常',
      1: '异常',
      2: '未知'
    };
    return statusTexts[status] || '未知';
  };

  // 获取环境标签类型
  const getEnvironmentType = (env) => {
    const envTypes = {
      0: 'danger',
      1: 'primary'
    };
    return envTypes[env] || 'info';
  };

  // 获取环境文本
  const getEnvironmentText = (env) => {
    const envTexts = {
      0: '生产',
      1: '测试'
    };
    return envTexts[env] || '未知';
  };

  // 获取生命周期标签类型
  const getLifecycleType = (lifecycle) => {
    const lifecycleTypes = {
      0: 'info',
      1: 'success'
    };
    return lifecycleTypes[lifecycle] || 'info';
  };

  // 获取生命周期文本
  const getLifecycleText = (lifecycle) => {
    const lifecycleTexts = {
      0: '已下线',
      1: '在线'
    };
    return lifecycleTexts[lifecycle] || '未知';
  };

  // 格式化日期
  const formatDate = (dateValue) => {
    if (!dateValue) return '-';
    const date = new Date(dateValue);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };
</script>

<style scoped>
  .tab-content {
    padding: 16px 0;
  }
</style>
