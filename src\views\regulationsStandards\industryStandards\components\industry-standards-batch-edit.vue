<template>
  <ele-modal
    :width="800"
    :model-value="visible"
    title="批量编辑行业标准"
    :body-style="{ paddingBottom: '8px' }"
    @update:model-value="updateVisible"
  >
    <div v-if="selectedRecords.length > 0" class="batch-edit-container">
      <el-alert
        :title="`已选择 ${selectedRecords.length} 条记录进行批量编辑`"
        type="info"
        :closable="false"
        style="margin-bottom: 16px"
      />

      <el-form
        ref="formRef"
        :model="form"
        label-width="100px"
        style="padding-right: 16px"
      >
        <el-row :gutter="16">
          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="标准类别">
              <el-checkbox
                v-model="enableFields.standard_category"
                @change="handleFieldToggle('standard_category')"
              >
                批量修改
              </el-checkbox>
              <el-select
                v-model="form.standard_category"
                placeholder="请选择标准类别"
                :disabled="!enableFields.standard_category"
                clearable
                filterable
                allow-create
                style="width: 100%; margin-top: 8px"
              >
                <el-option label="金融标准" value="金融标准" />
                <el-option label="ISO标准" value="ISO标准" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="标准类型">
              <el-checkbox
                v-model="enableFields.standard_type"
                @change="handleFieldToggle('standard_type')"
              >
                批量修改
              </el-checkbox>
              <el-select
                v-model="form.standard_type"
                placeholder="请选择标准类型"
                :disabled="!enableFields.standard_type"
                clearable
                filterable
                allow-create
                style="width: 100%; margin-top: 8px"
              >
                <el-option label="强制性国家标准" value="强制性国家标准" />
                <el-option label="推荐性国家标准" value="推荐性国家标准" />
                <el-option label="推荐性行业标准" value="推荐性行业标准" />
                <el-option label="国际标准" value="国际标准" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="发布机构">
              <el-checkbox
                v-model="enableFields.issuing_authority"
                @change="handleFieldToggle('issuing_authority')"
              >
                批量修改
              </el-checkbox>
              <el-select
                v-model="form.issuing_authority"
                placeholder="请选择发布机构"
                :disabled="!enableFields.issuing_authority"
                clearable
                filterable
                allow-create
                style="width: 100%; margin-top: 8px"
              >
                <el-option label="中国人民银行" value="中国人民银行" />
                <el-option
                  label="国家金融监督管理总局"
                  value="国家金融监督管理总局"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="管理领域">
              <el-checkbox
                v-model="enableFields.management_domain"
                @change="handleFieldToggle('management_domain')"
              >
                批量修改
              </el-checkbox>
              <el-select
                v-model="form.management_domain"
                placeholder="请选择管理领域"
                :disabled="!enableFields.management_domain"
                clearable
                filterable
                allow-create
                style="width: 100%; margin-top: 8px"
              >
                <el-option label="信息和数据安全" value="信息和数据安全" />
                <el-option label="系统设计和研发" value="系统设计和研发" />
                <el-option label="基础环境及网络" value="基础环境及网络" />
                <el-option label="金融科技管理" value="金融科技管理" />
                <el-option label="信息科技发展规划" value="信息科技发展规划" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="标准状态">
              <el-checkbox
                v-model="enableFields.standard_status"
                @change="handleFieldToggle('standard_status')"
              >
                批量修改
              </el-checkbox>
              <el-select
                v-model="form.standard_status"
                placeholder="请选择标准状态"
                :disabled="!enableFields.standard_status"
                clearable
                style="width: 100%; margin-top: 8px"
              >
                <el-option label="生效中" value="生效中" />
                <el-option label="已失效" value="已失效" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="发布时间">
              <el-checkbox
                v-model="enableFields.issue_time"
                @change="handleFieldToggle('issue_time')"
              >
                批量修改
              </el-checkbox>
              <el-date-picker
                v-model="form.issue_time"
                type="date"
                placeholder="请选择发布时间"
                :disabled="!enableFields.issue_time"
                style="width: 100%; margin-top: 8px"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="生效时间">
              <el-checkbox
                v-model="enableFields.effective_time"
                @change="handleFieldToggle('effective_time')"
              >
                批量修改
              </el-checkbox>
              <el-date-picker
                v-model="form.effective_time"
                type="date"
                placeholder="请选择生效时间"
                :disabled="!enableFields.effective_time"
                style="width: 100%; margin-top: 8px"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :lg="12" :md="12" :sm="24" :xs="24">
            <el-form-item label="失效时间">
              <el-checkbox
                v-model="enableFields.expiration_time"
                @change="handleFieldToggle('expiration_time')"
              >
                批量修改
              </el-checkbox>
              <el-date-picker
                v-model="form.expiration_time"
                type="date"
                placeholder="请选择失效时间"
                :disabled="!enableFields.expiration_time"
                style="width: 100%; margin-top: 8px"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-divider content-position="left">预览修改记录</el-divider>
      <el-table
        :data="selectedRecords.slice(0, 5)"
        border
        style="width: 100%"
        max-height="300"
      >
        <el-table-column prop="standard_name" label="标准名称" width="200" />
        <el-table-column
          prop="standard_category"
          label="标准类别"
          width="120"
        />
        <el-table-column prop="standard_type" label="标准类型" width="150" />
        <el-table-column prop="standard_status" label="标准状态" width="100" />
      </el-table>
      <div v-if="selectedRecords.length > 5" class="more-records">
        <span>还有 {{ selectedRecords.length - 5 }} 条记录...</span>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :loading="loading"
        :disabled="!hasEnabledFields"
        @click="handleSave"
      >
        批量保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { updateInst } from '@/api/cmdb';

  defineOptions({ name: 'IndustryStandardsBatchEdit' });

  const props = defineProps({
    modelValue: Boolean,
    selectedRecords: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits([
    'update:modelValue',
    'update:selectedRecords',
    'done'
  ]);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 修改visible */
  const updateVisible = (value) => {
    visible.value = value;
  };

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 字段启用状态 */
  const enableFields = ref({
    standard_category: false,
    standard_type: false,
    issuing_authority: false,
    management_domain: false,
    standard_status: false,
    issue_time: false,
    effective_time: false,
    expiration_time: false
  });

  /** 表单数据 */
  const form = ref({
    standard_category: '',
    standard_type: '',
    issuing_authority: '',
    management_domain: '',
    standard_status: '',
    issue_time: '',
    effective_time: '',
    expiration_time: ''
  });

  /** 是否有启用的字段 */
  const hasEnabledFields = computed(() => {
    return Object.values(enableFields.value).some((enabled) => enabled);
  });

  /** 字段切换处理 */
  const handleFieldToggle = (field) => {
    if (!enableFields.value[field]) {
      form.value[field] = '';
    }
  };

  /** 监听弹窗打开 */
  watch(
    visible,
    (value) => {
      if (value) {
        nextTick(() => {
          resetForm();
        });
      }
    },
    { immediate: true }
  );

  /** 重置表单 */
  const resetForm = () => {
    // 重置字段启用状态
    Object.keys(enableFields.value).forEach((key) => {
      enableFields.value[key] = false;
    });

    // 重置表单数据
    Object.keys(form.value).forEach((key) => {
      form.value[key] = '';
    });
  };

  /** 保存 */
  const handleSave = async () => {
    if (!hasEnabledFields.value) {
      EleMessage.error('请至少选择一个字段进行修改');
      return;
    }

    if (props.selectedRecords.length === 0) {
      EleMessage.error('没有选中的记录');
      return;
    }

    loading.value = true;

    try {
      // 构建更新数据，只包含启用的字段
      const updateData = {};
      Object.keys(enableFields.value).forEach((field) => {
        if (enableFields.value[field]) {
          updateData[field] = form.value[field];
        }
      });

      let successCount = 0;
      let errorCount = 0;

      // 批量更新
      for (const record of props.selectedRecords) {
        try {
          await updateInst({
            bkObjId: 'industry_standards',
            instId: record.bk_inst_id,
            data: updateData
          });
          successCount++;
        } catch (error) {
          console.error(`更新记录 ${record.bk_inst_id} 失败:`, error);
          errorCount++;
        }
      }

      if (errorCount === 0) {
        EleMessage.success(`批量编辑完成！成功更新 ${successCount} 条记录`);
      } else {
        EleMessage.warning(
          `批量编辑完成！成功 ${successCount} 条，失败 ${errorCount} 条`
        );
      }

      updateVisible(false);
      emit('update:selectedRecords', []);
      emit('done');
    } catch (error) {
      console.error('批量编辑失败:', error);
      EleMessage.error('批量编辑失败，请重试');
    } finally {
      loading.value = false;
    }
  };

  /** 关闭 */
  const handleCancel = () => {
    updateVisible(false);
  };
</script>

<style scoped>
  .batch-edit-container {
    padding: 8px 0;
  }

  .more-records {
    text-align: center;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-top: 8px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-checkbox) {
    margin-bottom: 8px;
  }
</style>
