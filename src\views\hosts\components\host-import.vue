<template>
  <ele-modal
    :width="460"
    title="批量导入主机"
    :body-style="{ paddingTop: '8px' }"
    v-model="visible"
  >
    <div v-loading="importLoading" class="host-import-upload">
      <el-upload
        drag
        :action="uploadAction"
        :data="uploadData"
        :headers="uploadHeaders"
        accept=".xlsx,.xls"
        :show-upload-list="false"
        :before-upload="beforeUpload"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
      >
        <ele-text
          type="primary"
          :icon="CloudUploadOutlined"
          :icon-props="{ size: 52 }"
          style="margin-bottom: 10px"
        />
        <ele-text type="placeholder">将文件拖到此处, 或点击上传</ele-text>
      </el-upload>
    </div>
    <div style="display: flex; align-items: center">
      <ele-text size="sm" type="secondary" style="line-height: 17px; flex: 1">
        <span style="padding-right: 8px">只能上传 xls、xlsx 文件，</span>
        <el-link
          type="primary"
          underline="never"
          style="font-size: inherit; line-height: inherit; vertical-align: 0"
          @click="downloadTemplate"
        >
          下载模板
        </el-link>
      </ele-text>
    </div>
  </ele-modal>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { CloudUploadOutlined } from '@/components/icons';

  defineOptions({ name: 'HostImport' });

  const emit = defineEmits(['done']);

  // 弹窗是否打开
  const visible = defineModel({ type: Boolean });

  // 模型实例ID
  const bkObjId = 'host_resource';

  // 导入请求状态
  const importLoading = ref(false);

  // 上传配置
  const uploadAction = '/api/cmdb/importExcel';
  const uploadData = { bkObjId };
  const uploadHeaders = {
    Authorization: `Bearer ${localStorage.getItem('token') || ''}`
  };

  // 下载模板
  const downloadTemplate = async () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    try {
      const formData = new FormData();
      formData.append('bkObjId', bkObjId);

      const response = await fetch(
        'http://*********:9502/cmdb/importTemplate',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token') || ''}`
          },
          body: formData
        }
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = '主机资源导入模板.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        loading.close();
      } else {
        throw new Error('下载失败');
      }
    } catch (error) {
      loading.close();
      EleMessage.error({
        message: error.message || '模板下载失败',
        plain: true
      });
    }
  };

  // 上传前检查
  const beforeUpload = (file) => {
    const isExcel =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      EleMessage.error({ message: '只能选择 excel 文件', plain: true });
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      EleMessage.error({ message: '大小不能超过 10MB', plain: true });
      return false;
    }

    importLoading.value = true;
    return true;
  };

  // 导入成功回调
  const handleImportSuccess = (response) => {
    importLoading.value = false;
    if (response.success || response.code === 200) {
      ElMessageBox({
        type: 'success',
        title: '导入结果',
        message: `成功导入 ${response.data?.successCount || '未知'} 条主机数据`,
        customStyle: { maxWidth: '442px' },
        draggable: true
      })
        .then(() => {})
        .catch(() => {});
      visible.value = false;
      emit('done');
    } else {
      ElMessageBox({
        type: 'error',
        title: '导入失败',
        message: response.message || '导入失败，请检查文件格式和内容',
        customStyle: { maxWidth: '442px' },
        draggable: true
      })
        .then(() => {})
        .catch(() => {});
    }
  };

  // 导入失败回调
  const handleImportError = (error) => {
    importLoading.value = false;
    console.error('导入错误:', error);
    ElMessageBox({
      type: 'error',
      title: '导入失败',
      message: '网络错误或服务器异常，请稍后重试',
      customStyle: { maxWidth: '442px' },
      draggable: true
    })
      .then(() => {})
      .catch(() => {});
  };
</script>

<style scoped>
  .host-import-upload {
    margin-bottom: 16px;
  }

  :deep(.el-upload) {
    width: 100%;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    padding: 32px 16px;
    text-align: center;
    transition: border-color 0.3s;
  }

  :deep(.el-upload-dragger:hover) {
    border-color: #409eff;
    background: #f0f8ff;
  }

  :deep(.el-upload-dragger.is-dragover) {
    border-color: #409eff;
    background: #f0f8ff;
  }
</style>
