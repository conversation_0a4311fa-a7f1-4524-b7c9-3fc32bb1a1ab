<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <el-icon class="text-primary mr-2"><Setting /></el-icon>
          <span class="text-lg font-semibold">系统变更记录</span>
        </div>
        <el-link type="primary" :underline="false">查看全部</el-link>
      </div>
    </template>
    <div class="p-2">
      <el-table :data="changeData" style="width: 100%">
        <el-table-column prop="content" label="变更内容" min-width="200" />
        <el-table-column prop="time" label="时间" width="120" />
        <el-table-column prop="operator" label="操作人" width="100" />
      </el-table>
    </div>
  </el-card>
</template>

<script setup>
  import { Setting } from '@element-plus/icons-vue';

  const changeData = [
    {
      content: 'V2.3.0版本发布，新增报表导出功能',
      time: '2023-10-10',
      operator: '王开发'
    },
    {
      content: '修复用户权限管理bug',
      time: '2023-10-05',
      operator: '李工程师'
    },
    {
      content: '数据库性能优化',
      time: '2023-09-28',
      operator: '张DBA'
    }
  ];
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }
</style>
