<template>
  <el-drawer
    :model-value="visible"
    title="转派工单"
    :size="600"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <div class="ticket-info">
        <h4>工单信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单ID">
            {{ ticketData?.ticket_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单类型">
            {{ ticketData?.ticket_type || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单等级">
            <el-tag
              v-if="ticketData?.level"
              :type="getLevelType(ticketData.level)"
              size="small"
            >
              {{ getLevelText(ticketData.level) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ ticketData?.create_user_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单内容" span="2">
            {{ ticketData?.comment || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
        @submit.prevent=""
      >
        <el-form-item label="执行人" prop="executor_user_id">
          <el-select
            v-model="form.executor_user_id"
            placeholder="请选择执行人"
            style="width: 100%"
            filterable
            @change="handleExecutorChange"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        转派工单
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { transferTicket } from '@/api/ticket';
  import { pageUsers } from '@/api/system/user';

  defineOptions({ name: 'TicketTransfer' });

  const props = defineProps({
    modelValue: Boolean,
    ticketData: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const visible = computed(() => props.modelValue);

  const formRef = ref(null);
  const loading = ref(false);
  const userLoading = ref(false);

  const userOptions = ref([]);

  const form = ref({
    executor_user_name: '',
    executor_user_id: null,
    ticket_id: ''
  });

  const rules = computed(() => ({
    executor_user_id: [
      { required: true, message: '请选择执行人', trigger: 'change' }
    ]
  }));

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleClosed = () => {
    resetFields();
  };

  const resetFields = () => {
    formRef.value?.resetFields();
    form.value = {
      executor_user_name: '',
      executor_user_id: null,
      ticket_id: ''
    };
    userOptions.value = [];
  };

  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        // 获取当前工单的system_item数据
        let systemItemData = null;
        if (
          props.ticketData?.transferHistory &&
          props.ticketData.transferHistory.length > 0
        ) {
          const latestHistory =
            props.ticketData.transferHistory[
              props.ticketData.transferHistory.length - 1
            ];
          systemItemData = latestHistory.system_item || null;
        }

        const requestData = {
          follow_field: [
            'event_name',
            'occurrence_time',
            'event_source',
            'event_level',
            'affected_business',
            'event_screenshot',
            'fault_category',
            'root_cause_fault_system',
            'event_cause',
            'ops_handler',
            'event_handling_process',
            'event_handling_results_and_basis',
            'resolution_time',
            'solution_and_whether_production_change_is_involved',
            'whether_review_is_conducted'
          ],
          executor_user_name: form.value.executor_user_name,
          executor_user_id: form.value.executor_user_id,
          comment: props.ticketData?.comment || '',
          ticket_id: props.ticketData?.ticket_id || form.value.ticket_id,
          system_item: systemItemData
        };

        await transferTicket(requestData);

        EleMessage.success('工单转派成功');
        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '转派工单失败');
      }
      loading.value = false;
    });
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const handleExecutorChange = (value) => {
    const user = userOptions.value.find((u) => u.value === value);
    if (user) {
      form.value.executor_user_name = user.label;
    }
  };

  const searchUsers = async () => {
    userLoading.value = true;
    try {
      const res = await pageUsers({
        pageNum: 1,
        pageSize: 9999 // 获取所有用户
      });
      userOptions.value =
        res.rows?.map((user) => ({
          label: user.nickName || user.userName,
          value: user.userId
        })) || [];
    } catch (e) {
      console.error('搜索用户失败:', e);
    }
    userLoading.value = false;
  };

  const getLevelType = (level) => {
    const levelTypes = {
      p1: 'danger',
      p2: 'warning',
      p3: 'primary',
      p4: 'info'
    };
    return levelTypes[level] || 'info';
  };

  const getLevelText = (level) => {
    const levelTexts = {
      p1: '紧急',
      p2: '高',
      p3: '中',
      p4: '低'
    };
    return levelTexts[level] || level;
  };

  watch(
    () => props.ticketData,
    (value) => {
      if (value) {
        form.value.ticket_id = value.ticket_id;
      }
    },
    { immediate: true }
  );

  watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        // 初始化用户列表
        searchUsers();
      }
    }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .ticket-info {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .ticket-info h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .system-item-section {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
    background-color: #fafafa;
  }

  .section-desc {
    margin: 0 0 12px 0;
    color: #606266;
    font-size: 14px;
  }

  .current-event {
    margin-top: 12px;
  }
</style>
