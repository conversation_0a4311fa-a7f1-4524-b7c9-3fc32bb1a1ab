<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex items-center">
        <el-icon class="text-primary mr-2"><BellFilled /></el-icon>
        <span class="text-lg font-semibold">运维工单统计</span>
      </div>
    </template>
    <div class="p-4">
      <div class="h-64 mb-6">
        <div ref="workOrderChart" class="w-full h-full"></div>
      </div>
      <div class="space-y-3">
        <div
          v-for="item in workOrderStats"
          :key="item.status"
          class="flex justify-between items-center"
        >
          <div class="flex items-center">
            <span class="w-3 h-3 rounded-full mr-2" :class="item.color"></span>
            <span class="text-sm">{{ item.status }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-lg font-semibold mr-1">{{ item.count }}</span>
            <span class="text-xs text-gray-500">工单</span>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
  import { ref, onMounted, nextTick } from 'vue';
  import * as echarts from 'echarts';
  import { BellFilled } from '@element-plus/icons-vue';

  const workOrderChart = ref(null);

  const workOrderStats = [
    { status: '已上线', count: 128, color: 'bg-green-500' },
    { status: '执行上线', count: 45, color: 'bg-yellow-500' },
    { status: '终止', count: 12, color: 'bg-red-500' }
  ];

  const initChart = () => {
    nextTick(() => {
      if (workOrderChart.value) {
        const chart = echarts.init(workOrderChart.value);
        const option = {
          color: ['#00B42A', '#FF7D00', '#F53F3F'],
          tooltip: {
            trigger: 'item'
          },
          series: [
            {
              name: '工单状态',
              type: 'pie',
              radius: '80%',
              center: ['50%', '50%'],
              data: [
                { value: 128, name: '已上线' },
                { value: 45, name: '执行上线' },
                { value: 12, name: '终止' }
              ],
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}: {c} ({d}%)'
              }
            }
          ]
        };
        chart.setOption(option);

        window.addEventListener('resize', () => {
          chart.resize();
        });
      }
    });
  };

  onMounted(() => {
    initChart();
  });
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }

  .space-y-3 > * + * {
    margin-top: 0.75rem;
  }
</style>
