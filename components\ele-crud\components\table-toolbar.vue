<!-- 表头操作按钮 -->
<template>
  <ElButton
    v-if="addBtnProps !== false"
    type="primary"
    :icon="PlusOutlined"
    class="ele-btn-icon"
    v-bind="(addBtnProps === true ? void 0 : addBtnProps) || {}"
    @click="handleAddBtnClick"
  >
    {{ lang.add }}
  </ElButton>
  <ElButton
    v-if="delBtnProps !== false"
    type="danger"
    :icon="DeleteOutlined"
    class="ele-btn-icon"
    v-bind="(delBtnProps === true ? void 0 : delBtnProps) || {}"
    @click="handleDelBtnClick"
  >
    {{ lang.deleteBatch }}
  </ElButton>
  <slot></slot>
</template>

<script setup>
  import { ElButton } from 'element-plus';
  import { DeleteOutlined, PlusOutlined } from '../../icons/index';

  defineOptions({ name: 'TableToolbar' });

  defineProps({
    /** 添加按钮属性 */
    addBtnProps: [Boolean, Object],
    /** 批量删除按钮属性 */
    delBtnProps: [Boolean, Object],
    /** 国际化 */
    lang: {
      type: Object,
      required: true
    }
  });

  const emit = defineEmits({
    /** 操作按钮点击事件 */
    btnClick: (_action, _e) => true
  });

  /** 按钮点击事件 */
  const handleBtnClick = (action, e) => {
    emit('btnClick', action, e);
  };

  /** 添加按钮点击事件 */
  const handleAddBtnClick = (e) => {
    handleBtnClick('add', e);
  };

  /** 批量删除按钮点击事件 */
  const handleDelBtnClick = (e) => {
    handleBtnClick('delSelections', e);
  };
</script>
