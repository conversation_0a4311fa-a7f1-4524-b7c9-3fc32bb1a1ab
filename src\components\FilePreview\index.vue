<template>
  <el-dialog
    v-model="visible"
    :title="previewTitle"
    width="80%"
    :before-close="closePreview"
    append-to-body
  >
    <div class="preview-container">
      <div v-if="isLoading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <div v-else-if="previewError" class="error-container">
        <el-icon><WarningFilled /></el-icon>
        <span>{{ previewError }}</span>
      </div>
      <div v-else class="office-preview">
        <!-- PDF 预览 -->
        <vue-office-pdf
          v-if="previewType === 'pdf'"
          :src="previewUrl"
          style="height: 70vh"
          @rendered="onPreviewRendered"
          @renderFailed="onPreviewFailed"
          @error="onPreviewFailed"
        />
        <!-- Word 文档预览 -->
        <vue-office-docx
          v-else-if="previewType === 'docx'"
          :src="previewUrl"
          style="height: 70vh"
          @rendered="onPreviewRendered"
          @renderFailed="onPreviewFailed"
          @error="onPreviewFailed"
        />
        <!-- Excel 预览 -->
        <vue-office-excel
          v-else-if="previewType === 'excel'"
          :src="previewUrl"
          style="height: 70vh"
          @rendered="onPreviewRendered"
          @renderFailed="onPreviewFailed"
          @error="onPreviewFailed"
        />
        <!-- 不支持的文件类型 -->
        <div v-else class="unsupported-container">
          <el-icon><Document /></el-icon>
          <span>暂不支持预览此文件类型</span>
          <el-button type="primary" @click="downloadCurrentFile">
            下载文件
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { Document, Loading, WarningFilled } from '@element-plus/icons-vue';
  import VueOfficePdf from '@vue-office/pdf';
  import VueOfficeDocx from '@vue-office/docx';
  import VueOfficeExcel from '@vue-office/excel';
  import '@vue-office/docx/lib/index.css';
  import '@vue-office/excel/lib/index.css';

  defineOptions({ name: 'FilePreview' });

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 预览相关数据 */
  const previewUrl = ref('');
  const previewType = ref('');
  const previewTitle = ref('');
  const currentPreviewFile = ref(null);
  const isLoading = ref(false);
  const previewError = ref('');

  /** 获取文件扩展名 */
  const getFileExtension = (fileName) => {
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1) return '';
    return fileName.substring(lastDotIndex + 1).toLowerCase();
  };

  /** 获取文件名 */
  const getFileName = (url) => {
    if (!url) return '';
    const filename = url.split('/').pop() || '附件文件';
    return filename.includes('.') ? filename : '附件文件.pdf';
  };

  /** 预览文件 */
  const previewFile = async (attachment) => {
    const fileName = attachment.fileName || getFileName(attachment.url);
    const fileExtension = getFileExtension(fileName);

    console.log('预览文件:', { fileName, fileExtension, url: attachment.url });

    currentPreviewFile.value = attachment;
    previewTitle.value = `预览 - ${fileName}`;
    previewError.value = '';
    isLoading.value = true;

    // 根据文件扩展名确定预览类型
    if (fileExtension === 'pdf') {
      previewType.value = 'pdf';
    } else if (['doc', 'docx'].includes(fileExtension)) {
      previewType.value = 'docx';
    } else if (['xls', 'xlsx'].includes(fileExtension)) {
      previewType.value = 'excel';
    } else {
      previewType.value = 'unsupported';
      isLoading.value = false;
      visible.value = true;
      return;
    }

    try {
      // 直接使用原始URL
      previewUrl.value = attachment.url;
      visible.value = true;

      // 延迟隐藏loading状态，确保组件有时间渲染
      setTimeout(() => {
        isLoading.value = false;
      }, 500);
    } catch (error) {
      console.error('预览文件失败:', error);
      isLoading.value = false;
      previewError.value = '文件预览失败，请稍后重试';
      visible.value = true;
    }
  };

  /** 预览渲染成功回调 */
  const onPreviewRendered = () => {
    console.log('预览渲染成功');
    isLoading.value = false;
  };

  /** 预览渲染失败回调 */
  const onPreviewFailed = (error) => {
    console.error('预览渲染失败:', error);
    isLoading.value = false;
    previewError.value = '文件预览失败，可能是文件格式不支持或文件已损坏';
  };

  /** 关闭预览弹窗 */
  const closePreview = () => {
    visible.value = false;
    previewUrl.value = '';
    previewType.value = '';
    previewTitle.value = '';
    currentPreviewFile.value = null;
    isLoading.value = false;
    previewError.value = '';
  };

  /** 下载当前预览的文件 */
  const downloadCurrentFile = async () => {
    if (!currentPreviewFile.value) return;

    const fileName =
      currentPreviewFile.value.fileName ||
      getFileName(currentPreviewFile.value.url);

    try {
      const response = await fetch(currentPreviewFile.value.url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('下载失败:', error);
      const link = document.createElement('a');
      link.href = currentPreviewFile.value.url;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  /** 监听弹窗关闭 */
  watch(visible, (newValue) => {
    if (!newValue) {
      closePreview();
    }
  });

  /** 暴露预览方法给父组件 */
  defineExpose({
    previewFile
  });
</script>

<style scoped>
  /* 预览弹窗样式 */
  .preview-container {
    height: 75vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .loading-container,
  .error-container,
  .unsupported-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: var(--el-text-color-regular);
  }

  .loading-container .is-loading {
    font-size: 24px;
    color: var(--el-color-primary);
  }

  .error-container .el-icon {
    font-size: 24px;
    color: var(--el-color-warning);
  }

  .unsupported-container .el-icon {
    font-size: 48px;
    color: var(--el-text-color-placeholder);
  }

  .office-preview {
    width: 100%;
    height: 100%;
  }
</style>
