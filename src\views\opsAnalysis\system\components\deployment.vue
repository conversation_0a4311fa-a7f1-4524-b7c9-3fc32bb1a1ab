<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex items-center">
        <el-icon class="text-primary mr-2"><Monitor /></el-icon>
        <span class="text-lg font-semibold">部署方式</span>
      </div>
    </template>
    <div class="p-2">
      <el-row :gutter="24">
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">部署模式</h3>
            <el-tag type="primary" size="large">{{ deploymentMethod }}</el-tag>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">高可用配置</h3>
            <div class="space-y-2">
              <div class="flex items-center">
                <span class="text-sm mr-2">应用双活:</span>
                <el-tag :type="appActiveActive ? 'success' : 'info'" size="small" plain>
                  <el-icon class="mr-1">
                    <Check v-if="appActiveActive" />
                    <Close v-else />
                  </el-icon>
                  {{ appActiveActive ? '是' : '否' }}
                </el-tag>
              </div>
              <div class="flex items-center">
                <span class="text-sm mr-2">数据库双活:</span>
                <el-tag :type="dbActiveActive ? 'success' : 'info'" size="small" plain>
                  <el-icon class="mr-1">
                    <Check v-if="dbActiveActive" />
                    <Close v-else />
                  </el-icon>
                  {{ dbActiveActive ? '是' : '否' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">部署机房</h3>
            <div class="space-y-2">
              <div class="flex items-center">
                <span class="text-sm mr-2">应用部署:</span>
                <span class="text-sm">{{ appDeploymentRoom }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm mr-2">数据库部署:</span>
                <span class="text-sm">{{ dbDeploymentRoom }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-500 mb-3">备份策略</h3>
            <div class="space-y-2">
              <div class="flex items-center">
                <span class="text-sm mr-2">应用备份:</span>
                <span class="text-sm">{{ appBackupMethod }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm mr-2">应用备份周期:</span>
                <span class="text-sm">{{ appBackupCycle }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm mr-2">数据库备份:</span>
                <span class="text-sm">{{ dbBackupMethod }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm mr-2">数据库备份周期:</span>
                <span class="text-sm">{{ dbBackupCycle }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup>
  import { computed } from 'vue';
  import { Monitor, Check, Close } from '@element-plus/icons-vue';

  // 接收父组件传递的系统数据
  const props = defineProps({
    systemData: {
      type: Object,
      default: () => ({})
    }
  });

  // 计算属性：部署方式
  const deploymentMethod = computed(() => {
    return props.systemData.deployment_method || '未配置';
  });

  // 计算属性：应用双活
  const appActiveActive = computed(() => {
    return props.systemData.application_active_active === '是';
  });

  // 计算属性：数据库双活
  const dbActiveActive = computed(() => {
    return props.systemData.database_active_active === '是';
  });

  // 计算属性：应用部署机房
  const appDeploymentRoom = computed(() => {
    return props.systemData.application_deployment_computer_room || '未配置';
  });

  // 计算属性：数据库部署机房
  const dbDeploymentRoom = computed(() => {
    return props.systemData.database_deployment_computer_room || '未配置';
  });

  // 计算属性：应用备份方式
  const appBackupMethod = computed(() => {
    return props.systemData.application_backup_method || '未配置';
  });

  // 计算属性：应用备份周期
  const appBackupCycle = computed(() => {
    return props.systemData.application_backup_cycle || '未配置';
  });

  // 计算属性：数据库备份方式
  const dbBackupMethod = computed(() => {
    return props.systemData.database_backup_method || '未配置';
  });

  // 计算属性：数据库备份周期
  const dbBackupCycle = computed(() => {
    return props.systemData.database_backup_cycle || '未配置';
  });
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }

  .space-y-2 > :not(:last-child) {
    margin-bottom: 0.5rem;
  }
</style>
