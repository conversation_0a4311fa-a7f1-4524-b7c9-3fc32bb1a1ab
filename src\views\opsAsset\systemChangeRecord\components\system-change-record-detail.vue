<template>
  <ele-drawer
    v-model="visible"
    :size="800"
    title="系统变更记录详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="system-change-detail" v-if="data">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="流程标题" :span="2">
            {{ data.process_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="变更月份">
            {{ data.change_month || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="流程编号">
            {{ data.process_code || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ data.apply_real_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人编号">
            {{ data.apply_user_code || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="技术复核人">
            {{ data.technology_review_user || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请日期">
            {{ formatTime(data.apply_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="应用系统级别">
            {{ data.system_level || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="系统变更类型">
            {{ data.online_type || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="本次变更系统">
            {{ data.change_system || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="是否多系统变更">
            {{ data.multiple_system_change || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="变更所属来源系统">
            {{ data.source_system || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            {{ data.urgency || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="流程状态">
            {{ data.process_status || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="变更性质">
            {{ data.online_style || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="需求编号">
            {{ data.requirement_code || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 实施信息 -->
      <div class="detail-section">
        <h3 class="section-title">实施信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="变更实施开始时间">
            {{ formatTime(data.online_start) }}
          </el-descriptions-item>
          <el-descriptions-item label="变更实施结束时间">
            {{ formatTime(data.online_end) }}
          </el-descriptions-item>
          <el-descriptions-item label="归档日期">
            {{ formatTime(data.complete_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="变更实施操作人">
            {{ data.change_operate_user || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="实施结果记录" :span="2">
            <div class="content-text">
              {{ data.online_result || '-' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="业务检核确认" :span="2">
            <div class="content-text">
              {{ data.business_verify || '-' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="最近更新时间">
            {{ formatTime(data.update_time) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </ele-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'SystemChangeRecordDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  /** 格式化时间 */
  const formatTime = (timeValue) => {
    if (!timeValue) return '-';
    try {
      const date = new Date(timeValue);
      if (isNaN(date.getTime())) return '-';
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return '-';
    }
  };
</script>

<style scoped>
  .system-change-detail {
    padding: 0;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .detail-section:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .content-text {
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.5;
  }

  :deep(.el-descriptions__label) {
    font-weight: 600;
    color: #606266;
    width: 150px;
    white-space: nowrap !important;
    word-break: keep-all !important;
  }

  :deep(.el-descriptions__content) {
    color: #303133;
  }
</style>
