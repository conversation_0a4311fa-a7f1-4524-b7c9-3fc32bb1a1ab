<template>
  <ele-page>
    <!-- 搜索组件 -->
    <simple-search
      :fields="searchFields"
      :default-field="defaultField"
      :advanced-conditions="advancedConditions"
      @search="handleSimpleSearch"
      @reset="handleSearchReset"
      @advanced-search="showAdvancedModal"
      @clear-advanced="clearAdvancedConditions"
      @remove-advanced-condition="removeAdvancedCondition"
    />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <cmdb-pro-table
        ref="tableRef"
        row-key="bk_inst_id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '行业标准' }"
        cache-key="industryStandardsTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="success"
            class="ele-btn-icon"
            :icon="UploadOutlined"
            @click="openImport()"
          >
            导入
          </el-button>
          <el-button
            type="warning"
            class="ele-btn-icon"
            :icon="EditOutlined"
            :disabled="!selections.length"
            @click="openBatchEdit()"
          >
            批量编辑
          </el-button>
          <el-button
            type="danger"
            class="ele-btn-icon hidden-sm-and-down"
            :icon="DeleteOutlined"
            :disabled="!selections.length"
            @click="removeBatch()"
          >
            批量删除
          </el-button>
        </template>
        <template #attachment="{ row }">
          <div
            v-if="getAttachments(row.attachment).length > 0"
            class="file-list"
          >
            <el-popover placement="bottom" width="auto" trigger="click">
              <template #reference>
                <div class="file-item document-item">
                  <el-icon class="file-icon document-icon">
                    <component
                      :is="
                        getDocumentIcon(
                          getAttachments(row.attachment)[0].fileName ||
                            getAttachments(row.attachment)[0].url
                        )
                      "
                    />
                  </el-icon>
                  <span
                    v-if="getAttachments(row.attachment).length > 1"
                    class="file-count-text"
                  >
                    【{{ getAttachments(row.attachment).length }}】
                  </span>
                </div>
              </template>
              <div class="attachment-options">
                <div
                  v-for="(attachment, index) in getAttachments(row.attachment)"
                  :key="index"
                  class="attachment-item"
                >
                  <div class="attachment-info">
                    <el-icon class="attachment-icon">
                      <component
                        :is="
                          getDocumentIcon(attachment.fileName || attachment.url)
                        "
                      />
                    </el-icon>
                    <span class="attachment-name">{{
                      attachment.fileName || getFileName(attachment.url)
                    }}</span>
                  </div>
                  <div class="attachment-actions">
                    <el-button
                      type="primary"
                      size="small"
                      link
                      @click="previewFile(attachment)"
                    >
                      预览
                    </el-button>
                    <el-button
                      type="default"
                      size="small"
                      link
                      @click="
                        downloadFile(
                          attachment.url,
                          attachment.fileName || getFileName(attachment.url)
                        )
                      "
                    >
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </el-popover>
          </div>
          <span v-else>-</span>
        </template>
        <template #action="{ row }">
          <el-link type="info" underline="never" @click="openDetail(row)">
            详情
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            v-permission="'system:config:edit'"
            type="primary"
            underline="never"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['system:config:edit', 'system:config:remove']"
            direction="vertical"
          />
          <el-link
            v-permission="'system:config:remove'"
            type="danger"
            underline="never"
            @click="removeSingle(row)"
          >
            删除
          </el-link>
        </template>
      </cmdb-pro-table>
    </ele-card>

    <!-- 详情弹窗 -->
    <industry-standards-detail :data="currentDetail" v-model="showDetail" />

    <!-- 高级搜索弹窗 -->
    <advanced-search
      v-model="showAdvanced"
      :search-fields="searchFields"
      :initial-conditions="advancedConditions"
      @search="handleAdvancedSearch"
      @close="closeAdvancedModal"
    />

    <!-- 编辑弹窗 -->
    <industry-standards-edit
      :data="current"
      v-model="showEdit"
      @done="reload"
    />

    <!-- 导入弹窗 -->
    <industry-standards-import v-model="showImport" @done="reload" />

    <!-- 批量编辑弹窗 -->
    <industry-standards-batch-edit
      v-model="showBatchEdit"
      v-model:selectedRecords="selections"
      @done="reload"
    />

    <!-- 文件预览组件 -->
    <FilePreview ref="filePreviewRef" v-model="showPreview" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';

  import {
    PlusOutlined,
    DeleteOutlined,
    UploadOutlined,
    EditOutlined
  } from '@/components/icons';
  import { Document, DocumentCopy, Reading } from '@element-plus/icons-vue';
  import { searchInst, deleteInst, batchDeleteInst } from '@/api/cmdb';
  import SimpleSearch from '@/components/Search/simple-search.vue';
  import AdvancedSearch from '@/components/Search/advanced-search.vue';
  import IndustryStandardsEdit from './components/industry-standards-edit.vue';
  import IndustryStandardsDetail from './components/industry-standards-detail.vue';
  import IndustryStandardsImport from './components/industry-standards-import.vue';
  import IndustryStandardsBatchEdit from './components/industry-standards-batch-edit.vue';
  import FilePreview from '@/components/FilePreview/index.vue';

  /** 模型实例ID */
  const bkObjId = 'industry_standards';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'standard_name',
        columnKey: 'standard_name',
        label: '标准名称',
        align: 'center',
        width: 200,
        fixed: 'left'
      },
      {
        prop: 'standard_category',
        label: '标准类别',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'standard_type',
        label: '标准类型',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'issuing_authority',
        label: '发布机构',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'issue_time',
        label: '发布时间',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'effective_time',
        label: '生效时间',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'expiration_time',
        label: '失效时间',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'management_domain',
        label: '管理领域',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'standard_status',
        label: '标准状态',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'uploader',
        label: '上传人员',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'attachment',
        columnKey: 'attachment',
        label: '附件',
        align: 'center',
        minWidth: 120,
        slot: 'attachment'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 160,
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true,
        fixed: 'right'
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 搜索字段配置 */
  const searchFields = ref([
    { prop: 'standard_name', label: '标准名称', type: 'text' },
    {
      prop: 'standard_category',
      label: '标准类别',
      type: 'select',
      options: [
        { label: '金融标准', value: '金融标准' },
        { label: 'ISO标准', value: 'ISO标准' }
      ]
    },
    {
      prop: 'standard_type',
      label: '标准类型',
      type: 'select',
      options: [
        { label: '强制性国家标准', value: '强制性国家标准' },
        { label: '推荐性国家标准', value: '推荐性国家标准' },
        { label: '推荐性行业标准', value: '推荐性行业标准' },
        { label: '国际标准', value: '国际标准' }
      ]
    },
    {
      prop: 'issuing_authority',
      label: '发布机构',
      type: 'select',
      options: [
        { label: '中国人民银行', value: '中国人民银行' },
        { label: '国家金融监督管理总局', value: '国家金融监督管理总局' }
      ]
    },
    { prop: 'issue_time', label: '发布时间', type: 'daterange' },
    { prop: 'effective_time', label: '生效时间', type: 'daterange' },
    { prop: 'expiration_time', label: '失效时间', type: 'daterange' },
    {
      prop: 'management_domain',
      label: '管理领域',
      type: 'select',
      options: [
        { label: '信息和数据安全', value: '信息和数据安全' },
        { label: '系统设计和研发', value: '系统设计和研发' },
        { label: '基础环境及网络', value: '基础环境及网络' },
        { label: '金融科技管理', value: '金融科技管理' },
        { label: '信息科技发展规划', value: '信息科技发展规划' }
      ]
    },
    {
      prop: 'standard_status',
      label: '标准状态',
      type: 'select',
      options: [
        { label: '生效中', value: '生效中' },
        { label: '已失效', value: '已失效' }
      ]
    },
    { prop: 'uploader', label: '上传人员', type: 'text' }
  ]);

  /** 默认搜索字段 */
  const defaultField = ref({
    prop: 'standard_name',
    label: '标准名称'
  });

  /** 高级搜索条件 */
  const advancedConditions = ref([]);

  /** 当前搜索条件 */
  const currentSearchParams = ref({});

  /** 是否显示高级搜索弹窗 */
  const showAdvanced = ref(false);

  /** 是否显示详情弹窗 */
  const showDetail = ref(false);

  /** 当前详情数据 */
  const currentDetail = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示导入弹窗 */
  const showImport = ref(false);

  /** 是否显示批量编辑弹窗 */
  const showBatchEdit = ref(false);

  /** 文件预览相关 */
  const filePreviewRef = ref(null);
  const showPreview = ref(false);

  /** 表格数据源 */
  const datasource = async ({ pages }) => {
    let conditions = [];

    if (currentSearchParams.value.condition) {
      Object.keys(currentSearchParams.value.condition).forEach((field) => {
        const value = currentSearchParams.value.condition[field];
        if (typeof value === 'object' && value !== null) {
          Object.keys(value).forEach((operator) => {
            conditions.push({
              field: field,
              operator: operator,
              value: value[operator]
            });
          });
        } else {
          conditions.push({
            field: field,
            operator: '$eq',
            value: value
          });
        }
      });
    }

    if (advancedConditions.value.length > 0) {
      conditions = conditions.concat(
        advancedConditions.value.filter(
          (condition) =>
            condition.field &&
            condition.operator &&
            condition.value !== '' &&
            condition.value !== null &&
            condition.value !== undefined
        )
      );
    }

    const page = {
      start: (pages.pageNum - 1) * pages.pageSize,
      limit: pages.pageSize,
      sort: 'bk_inst_id'
    };

    const conditionParam =
      conditions.length > 0
        ? {
            [bkObjId]: conditions
          }
        : undefined;

    const res = await searchInst({
      bk_obj_id: bkObjId,
      condition: conditionParam,
      page
    });

    return {
      code: res.code,
      msg: res.msg,
      rows: res.rows,
      total: res.total
    };
  };

  /** 处理简单搜索 */
  const handleSimpleSearch = (params) => {
    currentSearchParams.value = params;
    reload();
  };

  /** 处理搜索重置 */
  const handleSearchReset = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    reload();
  };

  /** 显示高级搜索弹窗 */
  const showAdvancedModal = () => {
    showAdvanced.value = true;
  };

  /** 关闭高级搜索弹窗 */
  const closeAdvancedModal = () => {
    showAdvanced.value = false;
  };

  /** 处理高级搜索 */
  const handleAdvancedSearch = (conditions) => {
    advancedConditions.value = conditions;
    reload();
  };

  /** 清空高级搜索条件 */
  const clearAdvancedConditions = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    selections.value = [];
    reload();
  };

  /** 移除单个高级搜索条件 */
  const removeAdvancedCondition = (condition) => {
    const index = advancedConditions.value.findIndex(
      (c) =>
        c.field === condition.field &&
        c.operator === condition.operator &&
        c.value === condition.value
    );
    if (index > -1) {
      advancedConditions.value.splice(index, 1);
      selections.value = [];
      reload();
    }
  };

  /** 打开详情弹窗 */
  const openDetail = (row) => {
    currentDetail.value = row;
    showDetail.value = true;
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 打开批量编辑弹窗 */
  const openBatchEdit = () => {
    if (!selections.value.length) {
      EleMessage.error('请选择要编辑的数据');
      return;
    }
    showBatchEdit.value = true;
  };

  /** 单条删除 */
  const removeSingle = (row) => {
    ElMessageBox.confirm(`确定要删除该条行业标准吗？`, '系统提示', {
      type: 'warning'
    })
      .then(async () => {
        const loading = EleMessage.loading('请稍候...');
        try {
          await deleteInst({
            bkObjId,
            instId: row.bk_inst_id
          });
          loading.close();
          EleMessage.success('删除成功');
          reload();
        } catch (e) {
          loading.close();
          EleMessage.error(e.message || '删除失败');
        }
      })
      .catch(() => {});
  };

  /** 批量删除 */
  const removeBatch = () => {
    if (!selections.value.length) {
      EleMessage.error('请选择要删除的数据');
      return;
    }

    ElMessageBox.confirm(
      `确定要删除选中的${selections.value.length}条行业标准吗？`,
      '系统提示',
      { type: 'warning' }
    )
      .then(async () => {
        const loading = EleMessage.loading('请稍候...');
        try {
          const instIds = selections.value.map((item) => item.bk_inst_id);
          await batchDeleteInst({
            bkObjId,
            instIds: instIds
          });
          loading.close();
          EleMessage.success('删除成功');
          reload();
        } catch (e) {
          loading.close();
          EleMessage.error(e.message || '删除失败');
        }
      })
      .catch(() => {});
  };

  /** 刷新表格 */
  const reload = () => {
    tableRef.value?.reload?.();
    selections.value = [];
  };

  /** 获取附件列表 */
  const getAttachments = (attachmentData) => {
    if (!attachmentData) return [];

    if (typeof attachmentData === 'string') {
      try {
        // 尝试解析JSON字符串
        const parsed = JSON.parse(attachmentData);
        if (Array.isArray(parsed)) {
          return parsed.map((item) => ({
            url: item.url,
            fileName: item.fileName,
            ossId: item.ossId
          }));
        }
      } catch {
        // 如果解析失败，按逗号分隔的URL字符串处理（兼容旧格式）
        return attachmentData
          .split(',')
          .filter((url) => url.trim())
          .map((url) => ({
            url: url,
            fileName: getFileName(url),
            ossId: ''
          }));
      }
    } else if (Array.isArray(attachmentData)) {
      // 数组格式（兼容）
      return attachmentData.map((item) => ({
        url: item.url,
        fileName: item.fileName,
        ossId: item.ossId
      }));
    }

    return [];
  };

  /** 获取文件名 */
  const getFileName = (url) => {
    if (!url) return '';
    const filename = url.split('/').pop() || '附件文件';
    return filename.includes('.') ? filename : '附件文件.pdf';
  };

  /** 获取文档图标 */
  const getDocumentIcon = (filename) => {
    const lowerName = filename.toLowerCase();
    if (lowerName.endsWith('.pdf')) {
      return Reading;
    } else if (lowerName.endsWith('.doc') || lowerName.endsWith('.docx')) {
      return DocumentCopy;
    }
    return Document;
  };

  /** 下载文件 */
  const downloadFile = async (url, fileName) => {
    try {
      // 尝试使用 fetch 获取文件
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();

      // 创建 blob URL 并下载
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName || '附件文件';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 释放 blob URL
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('下载失败:', error);
      // 如果 fetch 失败，回退到直接下载方式
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || '附件文件';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  /** 预览文件 */
  const previewFile = (attachment) => {
    filePreviewRef.value?.previewFile(attachment);
  };
</script>

<style scoped>
  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .file-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
  }

  .file-item:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-icon {
    font-size: 18px;
    flex-shrink: 0;
  }

  .document-icon {
    color: var(--el-color-primary);
  }

  .file-count-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 2px;
  }

  .attachment-options {
    max-width: 320px;
  }

  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .attachment-item:last-child {
    border-bottom: none;
  }

  .attachment-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .attachment-icon {
    font-size: 16px;
    color: var(--el-color-primary);
    flex-shrink: 0;
  }

  .attachment-name {
    font-size: 14px;
    color: var(--el-text-color-regular);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .attachment-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }
</style>
