<template>
  <el-drawer v-model="visible" title="应用系统详情" direction="rtl" size="50%">
    <div v-if="businessData" class="detail-content">
      <!-- 基础信息 -->
      <div class="detail-section">
        <h3 class="section-title">基础信息</h3>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="业务名称">
            {{ businessData.bk_biz_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属项目">
            {{ businessData.business_collection || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="系统级别">
            <el-tag
              :type="getLevelType(businessData.system_level)"
              size="small"
            >
              {{ businessData.system_level || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="系统版本">
            {{ businessData.system_version || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="系统介绍" :span="2">
            {{ businessData.introduction || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="实现功能" :span="2">
            {{ businessData.functions || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="开发负责人">
            {{ businessData.system_developer || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="运维负责人">
            {{ businessData.system_maintainer || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="开发厂商" :span="2">
            {{ businessData.development_vendor || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="是否交维">
            <el-tag
              :type="
                businessData.whether_handed_over_for_maintenance === '是'
                  ? 'success'
                  : 'danger'
              "
              size="small"
            >
              {{ businessData.whether_handed_over_for_maintenance || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="交维日期">
            {{ formatDate(businessData.handover_date_for_maintenance) }}
          </el-descriptions-item>
          <el-descriptions-item label="是否演练过">
            <el-tag
              :type="
                businessData.whether_drilled === '是' ? 'success' : 'danger'
              "
              size="small"
            >
              {{ businessData.whether_drilled || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="演练日期">
            {{ formatDate(businessData.drill_date) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 技术栈 -->
      <div class="detail-section">
        <h3 class="section-title">技术栈</h3>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="是否有CFP框架">
            <el-tag
              :type="
                businessData.whether_equipped_with_cfp_framework === '是'
                  ? 'success'
                  : 'info'
              "
              size="small"
            >
              {{ businessData.whether_equipped_with_cfp_framework || '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="使用系统框架">
            {{ businessData.used_system_framework || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="中间件">
            {{ businessData.middleware || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="公司配置数据库">
            {{ businessData.company_configuration_database || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="开发语言">
            {{ businessData.development_language || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 应用部署情况 -->
      <div class="detail-section">
        <h3 class="section-title">应用部署情况</h3>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="应用双活">
            <el-tag
              :type="
                businessData.application_active_active === '是'
                  ? 'success'
                  : 'info'
              "
              size="small"
            >
              {{ businessData.application_active_active || '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="应用备份工具">
            {{ businessData.application_backup_tool || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用备份周期">
            {{ businessData.application_backup_cycle || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="部署方式">
            {{ businessData.deployment_method || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用部署机房">
            {{ businessData.application_deployment_computer_room || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用备份方式">
            {{ businessData.application_backup_method || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用备份文件所在机房">
            {{ businessData.computer_room_for_application_backup_files || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用备份文件地址">
            {{ businessData.address_of_application_backup_files || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 数据库部署情况 -->
      <div class="detail-section">
        <h3 class="section-title">数据库部署情况</h3>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="数据库双活">
            <el-tag
              :type="
                businessData.database_active_active === '是'
                  ? 'success'
                  : 'info'
              "
              size="small"
            >
              {{ businessData.database_active_active || '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据库版本">
            {{ businessData.database_version || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库备份工具">
            {{ businessData.database_backup_tool || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库备份周期">
            {{ businessData.database_backup_cycle || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库部署机房">
            {{ businessData.database_deployment_computer_room || '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库备份方式">
            {{ businessData.database_backup_method || '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库备份文件所在机房">
            {{ businessData.computer_room_for_database_backup_files || '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库备份文件地址">
            {{ businessData.address_of_database_backup_files || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 图片文档 -->
      <div class="detail-section">
        <h3 class="section-title">图片文档</h3>

        <el-descriptions :column="1" border>
          <el-descriptions-item label="业务逻辑图">
            <div v-if="businessLogicImages.length > 0" class="file-list">
              <div
                v-for="(image, index) in businessLogicImages"
                :key="index"
                class="file-item image-item"
              >
                <el-icon class="file-icon">
                  <Picture />
                </el-icon>
                <span class="file-name">业务逻辑图</span>
                <div class="file-actions">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleImagePreview(image)"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="default"
                    size="small"
                    link
                    @click="downloadFile(image, '业务逻辑图.jpg')"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>

          <el-descriptions-item label="技术流程图">
            <div v-if="technicalFlowImages.length > 0" class="file-list">
              <div
                v-for="(image, index) in technicalFlowImages"
                :key="index"
                class="file-item image-item"
              >
                <el-icon class="file-icon">
                  <Picture />
                </el-icon>
                <span class="file-name">技术流程图</span>
                <div class="file-actions">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleImagePreview(image)"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="default"
                    size="small"
                    link
                    @click="downloadFile(image, '技术流程图.jpg')"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>

          <el-descriptions-item label="相关文档">
            <div v-if="documents.length > 0" class="file-list">
              <div
                v-for="(doc, index) in documents"
                :key="index"
                class="file-item document-item"
              >
                <el-icon class="file-icon document-icon">
                  <component
                    :is="getDocumentIcon(doc.fileName || getFileName(doc.url))"
                  />
                </el-icon>
                <span class="file-name">{{
                  doc.fileName || getFileName(doc.url)
                }}</span>
                <div class="file-actions">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleDocumentPreview(doc)"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="default"
                    size="small"
                    link
                    @click="
                      downloadFile(
                        doc.url,
                        doc.fileName || getFileName(doc.url)
                      )
                    "
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="80%"
      append-to-body
    >
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>

    <!-- 文件预览组件 -->
    <FilePreview ref="filePreviewRef" v-model="showPreview" />
  </el-drawer>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import {
    ZoomIn,
    Document,
    Download,
    DocumentCopy,
    Reading,
    Picture
  } from '@element-plus/icons-vue';
  import FilePreview from '@/components/FilePreview/index.vue';

  const props = defineProps({
    modelValue: Boolean,
    businessData: Object
  });

  const emit = defineEmits(['update:modelValue']);

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  // 图片预览相关
  const imagePreviewVisible = ref(false);
  const previewImageUrl = ref('');

  // 文件预览相关
  const filePreviewRef = ref(null);
  const showPreview = ref(false);

  // 处理业务逻辑图 - 只显示第一张图片
  const businessLogicImages = computed(() => {
    if (!props.businessData?.business_logic_diagram) return [];

    let images = [];
    if (typeof props.businessData.business_logic_diagram === 'string') {
      images = props.businessData.business_logic_diagram
        .split(',')
        .filter((url) => url.trim());
    } else if (Array.isArray(props.businessData.business_logic_diagram)) {
      images = props.businessData.business_logic_diagram;
    }

    // 只返回第一张图片
    return images.length > 0 ? [images[0]] : [];
  });

  // 处理技术流程图 - 只显示第一张图片
  const technicalFlowImages = computed(() => {
    if (!props.businessData?.technical_flow_diagram) return [];

    let images = [];
    if (typeof props.businessData.technical_flow_diagram === 'string') {
      images = props.businessData.technical_flow_diagram
        .split(',')
        .filter((url) => url.trim());
    } else if (Array.isArray(props.businessData.technical_flow_diagram)) {
      images = props.businessData.technical_flow_diagram;
    }

    // 只返回第一张图片
    return images.length > 0 ? [images[0]] : [];
  });

  // 处理文档
  const documents = computed(() => {
    if (!props.businessData?.documents) return [];

    let docs = [];
    if (typeof props.businessData.documents === 'string') {
      try {
        docs = JSON.parse(props.businessData.documents);
      } catch {
        docs = [
          { url: props.businessData.documents, fileName: '文档', ossId: '' }
        ];
      }
    } else if (Array.isArray(props.businessData.documents)) {
      docs = props.businessData.documents;
    }

    return docs.filter((doc) => doc.url);
  });

  // 图片预览
  const handleImagePreview = (imageUrl) => {
    previewImageUrl.value = imageUrl;
    imagePreviewVisible.value = true;
  };

  // 文档预览
  const handleDocumentPreview = (doc) => {
    const attachment = {
      url: doc.url,
      fileName: doc.fileName || getFileName(doc.url)
    };
    filePreviewRef.value?.previewFile(attachment);
  };

  // 获取文件名
  const getFileName = (url) => {
    if (!url) return '';
    const filename = url.split('/').pop() || '附件文件';
    return filename.includes('.') ? filename : '附件文件.pdf';
  };

  // 获取文档图标
  const getDocumentIcon = (filename) => {
    const lowerName = filename.toLowerCase();
    if (lowerName.endsWith('.pdf')) {
      return Reading;
    } else if (lowerName.endsWith('.doc') || lowerName.endsWith('.docx')) {
      return DocumentCopy;
    }
    return Document;
  };

  // 下载文件
  const downloadFile = async (url, fileName) => {
    try {
      // 尝试使用 fetch 获取文件
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();

      // 创建 blob URL 并下载
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName || '附件文件';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 释放 blob URL
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('下载失败:', error);
      // 如果 fetch 失败，回退到直接下载方式
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || '附件文件';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // 获取级别标签类型
  const getLevelType = (level) => {
    const levelTypes = {
      A: 'danger',
      B: 'warning',
      C: 'primary'
    };
    return levelTypes[level] || 'info';
  };

  // 格式化日期
  const formatDate = (dateValue) => {
    if (!dateValue) return '-';
    const date = new Date(dateValue);
    return date.toLocaleDateString('zh-CN');
  };
</script>

<style scoped>
  .detail-content {
    padding: 0;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .detail-section:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .no-data {
    color: #909399;
    font-style: italic;
  }

  /* 文件列表样式 - 参考 regulatory-laws-detail */
  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    transition: all 0.3s ease;
    background: var(--el-fill-color-lighter);
    min-width: 80px;
    position: relative;
  }

  .file-item:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-icon {
    font-size: 18px;
    flex-shrink: 0;
    color: var(--el-color-primary);
  }

  .file-name {
    font-size: 12px;
    color: var(--el-text-color-regular);
    word-break: break-all;
    line-height: 1.2;
    flex: 1;
    min-width: 0;
  }

  .file-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }

  /* 保留原有的图片预览样式 */
  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: 80vh;
  }

  .preview-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 4px;
  }
</style>
