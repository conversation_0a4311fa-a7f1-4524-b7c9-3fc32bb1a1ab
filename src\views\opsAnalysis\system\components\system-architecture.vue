<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex items-center">
        <el-icon class="text-primary mr-2"><Grid /></el-icon>
        <span class="text-lg font-semibold">技术流程图</span>
      </div>
    </template>
    <div class="p-2">
      <div v-if="architectureUrl" class="image-container flex justify-center bg-gray-50 rounded-lg overflow-hidden">
        <el-image
          :src="architectureUrl"
          fit="contain"
          class="w-full h-full"
          :preview-src-list="[architectureUrl]"
          :initial-index="0"
          hide-on-click-modal
          :preview-teleported="true"
        >
          <template #error>
            <div
              class="flex items-center justify-center w-full h-full bg-gray-100 rounded-lg"
            >
              <div class="text-center">
                <el-icon class="text-4xl text-gray-400 mb-2"
                  ><Picture
                /></el-icon>
                <p class="text-gray-500">架构图加载失败</p>
              </div>
            </div>
          </template>
        </el-image>
      </div>
      <div v-else class="flex items-center justify-center image-container bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div class="text-center">
          <el-icon class="text-4xl text-gray-400 mb-3"><Picture /></el-icon>
          <p class="text-gray-500 text-sm">未提供，请及时更新图片</p>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
  import { computed } from 'vue';
  import { Grid, Picture } from '@element-plus/icons-vue';

  // 接收父组件传递的系统数据
  const props = defineProps({
    systemData: {
      type: Object,
      default: () => ({})
    }
  });

  // 计算属性：技术流程图URL
  const architectureUrl = computed(() => {
    return props.systemData.technical_flow_diagram || '';
  });
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }

  .image-container {
    width: 100%;
    min-height: 200px;
    max-height: 400px;
    aspect-ratio: 4 / 3;
  }

  /* 响应式断点 */
  @media (max-width: 768px) {
    .image-container {
      min-height: 150px;
      max-height: 300px;
      aspect-ratio: 4 / 3;
    }
  }

  @media (max-width: 480px) {
    .image-container {
      min-height: 120px;
      max-height: 250px;
      aspect-ratio: 1 / 1;
    }
  }

  .mb-3 {
    margin-bottom: 0.75rem;
  }

  .h-48 {
    height: 12rem;
  }

  .border-2 {
    border-width: 2px;
  }

  .border-dashed {
    border-style: dashed;
  }

  .border-gray-300 {
    border-color: #d1d5db;
  }

  .text-gray-500 {
    color: #6b7280;
  }
</style>
