<!-- 主机详情抽屉 -->
<template>
  <ele-drawer
    v-model="visible"
    :size="800"
    title="主机详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="host-detail" v-if="data">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="主机名称">
            {{ data.host_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="内网IP">
            {{ data.host_innerip || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="主机角色">
            <el-tag
              v-if="data.device_role !== null && data.device_role !== undefined"
              :type="getDeviceRoleType(data.device_role)"
              size="small"
            >
              {{ getDeviceRoleText(data.device_role) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="设备环境">
            <el-tag
              v-if="
                data.environment_code !== null &&
                data.environment_code !== undefined
              "
              :type="getEnvironmentType(data.environment_code)"
              size="small"
            >
              {{ getEnvironmentText(data.environment_code) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="使用状态">
            <el-tag
              v-if="
                data.lifecycle_code !== null &&
                data.lifecycle_code !== undefined
              "
              :type="getLifecycleType(data.lifecycle_code)"
              size="small"
            >
              {{ getLifecycleText(data.lifecycle_code) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="监控状态">
            <el-tag
              v-if="
                data.monitor_status !== null &&
                data.monitor_status !== undefined
              "
              :type="getMonitorStatusType(data.monitor_status)"
              size="small"
            >
              {{ getMonitorStatusText(data.monitor_status) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="系统名称">
            {{ data.system_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="项目名称">
            {{ data.project_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="技术负责人">
            {{ data.technician_owner || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="技术负责人ID">
            {{ data.technician_owner_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作系统版本" :span="2">
            <div class="text-content">
              {{ data.os_version || '-' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="备注信息" :span="2">
            <div class="text-content">
              {{ data.remark || '-' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="录入时间">
            {{ formatDate(data.create_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="录入人员">
            {{ data.create_user || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 硬件配置 -->
      <div class="detail-section">
        <h3 class="section-title">硬件配置</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="数据盘大小">
            {{ formatStorage(data.disk) }}
          </el-descriptions-item>
          <el-descriptions-item label="磁盘总大小">
            {{ formatStorage(data.total_disks) }}
          </el-descriptions-item>
          <el-descriptions-item label="内存容量">
            {{ formatStorage(data.memory) }}
          </el-descriptions-item>
          <el-descriptions-item label="NAS存储">
            {{ formatStorage(data.nas) }}
          </el-descriptions-item>
          <el-descriptions-item label="CPU逻辑核心数">
            {{ formatCpu(data.cpu) }}
          </el-descriptions-item>
          <el-descriptions-item label="系统盘">
            {{ formatStorage(data.system_disk) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 申请信息 -->
      <div class="detail-section">
        <h3 class="section-title">申请信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请部门">
            {{ data.apply_department || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请日期">
            {{ formatDate(data.apply_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ data.apply_username || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人工号">
            {{ data.apply_user_id || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </ele-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:model-value']);

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:model-value', value)
  });

  // 获取主机角色标签类型
  const getDeviceRoleType = (role) => {
    const roleTypes = {
      0: 'primary',
      1: 'success',
      2: 'warning'
    };
    return roleTypes[role] || 'info';
  };

  // 获取主机角色文本
  const getDeviceRoleText = (role) => {
    const roleTexts = {
      0: '应用',
      1: '数据库',
      2: '中间件'
    };
    return roleTexts[role] || '未知';
  };

  // 获取监控状态标签类型
  const getMonitorStatusType = (status) => {
    const statusTypes = {
      0: 'success',
      1: 'danger',
      2: 'warning'
    };
    return statusTypes[status] || 'info';
  };

  // 获取监控状态文本
  const getMonitorStatusText = (status) => {
    const statusTexts = {
      0: '正常',
      1: '异常',
      2: '未知'
    };
    return statusTexts[status] || '未知';
  };

  // 获取环境标签类型
  const getEnvironmentType = (env) => {
    const envTypes = {
      0: 'danger',
      1: 'primary'
    };
    return envTypes[env] || 'info';
  };

  // 获取环境文本
  const getEnvironmentText = (env) => {
    const envTexts = {
      0: '生产',
      1: '测试'
    };
    return envTexts[env] || '未知';
  };

  // 获取生命周期标签类型
  const getLifecycleType = (lifecycle) => {
    const lifecycleTypes = {
      0: 'info',
      1: 'success'
    };
    return lifecycleTypes[lifecycle] || 'info';
  };

  // 获取生命周期文本
  const getLifecycleText = (lifecycle) => {
    const lifecycleTexts = {
      0: '已下线',
      1: '在线'
    };
    return lifecycleTexts[lifecycle] || '未知';
  };

  // 格式化日期
  const formatDate = (dateValue) => {
    if (!dateValue) return '-';
    const date = new Date(dateValue);
    return date.toLocaleDateString('zh-CN');
  };

  // 格式化存储大小
  const formatStorage = (value) => {
    if (value === null || value === undefined || value === '') return '-';
    return `${value} GB`;
  };

  // 格式化CPU
  const formatCpu = (value) => {
    if (value === null || value === undefined || value === '') return '-';
    return `${value} 核`;
  };
</script>

<style scoped>
  .host-detail {
    padding: 0;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .detail-section:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .text-content {
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
  }

  :deep(.el-descriptions__label) {
    font-weight: 600;
    color: #606266;
    width: 150px;
  }

  :deep(.el-descriptions__content) {
    color: #303133;
  }
</style>
