<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑项目"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="ppb">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.ppb" />
                    <span class="label-text">项目预算</span>
                  </div>
                </template>
                <el-input-number
                  v-model="form.ppb"
                  placeholder="请输入项目预算"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  :disabled="!fieldEnabled.ppb"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="project_maintainer">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.project_maintainer" />
                    <span class="label-text">项目负责人</span>
                  </div>
                </template>
                <el-input
                  v-model="form.project_maintainer"
                  placeholder="请输入项目负责人"
                  clearable
                  :maxlength="100"
                  :disabled="!fieldEnabled.project_maintainer"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="project_budget_memory">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.project_budget_memory" />
                    <span class="label-text">项目预算内存</span>
                  </div>
                </template>
                <el-input-number
                  v-model="form.project_budget_memory"
                  placeholder="请输入项目预算内存"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  :disabled="!fieldEnabled.project_budget_memory"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="project_budget_disk">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.project_budget_disk" />
                    <span class="label-text">项目预算存储</span>
                  </div>
                </template>
                <el-input-number
                  v-model="form.project_budget_disk"
                  placeholder="请输入项目预算存储"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  :disabled="!fieldEnabled.project_budget_disk"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="project_budget_cpu">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.project_budget_cpu" />
                    <span class="label-text">项目预算CPU</span>
                  </div>
                </template>
                <el-input-number
                  v-model="form.project_budget_cpu"
                  placeholder="请输入项目预算CPU"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  :disabled="!fieldEnabled.project_budget_cpu"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="project_budget_nas">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.project_budget_nas" />
                    <span class="label-text">项目预算NAS</span>
                  </div>
                </template>
                <el-input-number
                  v-model="form.project_budget_nas"
                  placeholder="请输入项目预算NAS"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  :disabled="!fieldEnabled.project_budget_nas"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="projcet_mark">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.projcet_mark" />
                    <span class="label-text">备注</span>
                  </div>
                </template>
                <el-input
                  v-model="form.projcet_mark"
                  type="textarea"
                  placeholder="请输入备注"
                  :rows="3"
                  :maxlength="500"
                  show-word-limit
                  :disabled="!fieldEnabled.projcet_mark"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="updateVisible(false)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst } from '@/api/cmdb';

  defineOptions({ name: 'ProjectBatchEdit' });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 选中的记录 */
  const selectedRecords = defineModel('selectedRecords', {
    type: Array,
    default: () => []
  });

  /** 模型实例ID */
  const bkObjId = 'project_detail';

  /** 表单实例 */
  const formRef = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 字段启用状态 */
  const fieldEnabled = reactive({
    ppb: false,
    project_maintainer: false,
    project_budget_memory: false,
    project_budget_disk: false,
    project_budget_cpu: false,
    project_budget_nas: false,
    projcet_mark: false
  });

  /** 表单数据 */
  const form = reactive({
    ppb: null,
    project_maintainer: '',
    project_budget_memory: null,
    project_budget_disk: null,
    project_budget_cpu: null,
    project_budget_nas: null,
    projcet_mark: ''
  });

  /** 表单验证规则 */
  const rules = reactive({});

  /** 更新弹窗状态 */
  const updateVisible = (value) => {
    visible.value = value;
  };

  /** 弹窗关闭回调 */
  const handleClosed = () => {
    resetForm();
  };

  /** 重置表单 */
  const resetForm = () => {
    form.ppb = null;
    form.project_maintainer = '';
    form.project_budget_memory = null;
    form.project_budget_disk = null;
    form.project_budget_cpu = null;
    form.project_budget_nas = null;
    form.projcet_mark = '';

    Object.keys(fieldEnabled).forEach((key) => {
      fieldEnabled[key] = false;
    });
    formRef.value?.clearValidate();
  };

  /** 提交表单 */
  const submit = async () => {
    const hasEnabledField = Object.values(fieldEnabled).some(
      (enabled) => enabled
    );
    if (!hasEnabledField) {
      EleMessage.warning('请至少启用一个字段进行编辑');
      return;
    }

    const instInfoMap = {};
    Object.keys(fieldEnabled).forEach((key) => {
      if (fieldEnabled[key]) {
        instInfoMap[key] = form[key];
      }
    });

    const batchUpdateData = {
      bkObjId,
      bkInstId: selectedRecords.value.map((record) => record.bk_inst_id),
      instInfoMap
    };

    loading.value = true;
    try {
      await batchUpdateInst(batchUpdateData);
      EleMessage.success(`成功批量编辑 ${selectedRecords.value.length} 条记录`);
      updateVisible(false);
      emit('done');
    } catch (error) {
      EleMessage.error(error.message || '批量编辑失败');
    } finally {
      loading.value = false;
    }
  };

  /** 监听弹窗打开状态 */
  watch(
    () => visible.value,
    (newValue) => {
      if (newValue) {
        resetForm();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .field-with-checkbox {
    margin-bottom: 20px;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    .form-label-with-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;

      .label-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }
    }
  }
</style>
