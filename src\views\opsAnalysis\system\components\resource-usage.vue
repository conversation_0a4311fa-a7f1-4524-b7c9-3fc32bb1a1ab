<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex items-center">
        <el-icon class="text-primary mr-2"><Setting /></el-icon>
        <span class="text-lg font-semibold">资源使用情况</span>
      </div>
    </template>
    <div class="p-2">
      <div class="flex border-b border-gray-100 mb-4">
        <el-button
          :type="activeTab === 'prod' ? 'primary' : ''"
          text
          @click="activeTab = 'prod'"
          class="px-4 py-2"
        >
          生产环境
        </el-button>
        <el-button
          :type="activeTab === 'test' ? 'primary' : ''"
          text
          @click="activeTab = 'test'"
          class="px-4 py-2"
        >
          测试环境
        </el-button>
      </div>
      <el-row :gutter="16">
        <el-col
          :lg="6"
          :md="12"
          :xs="24"
          v-for="(resource, index) in resources"
          :key="index"
        >
          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <div class="flex justify-between items-start mb-3">
              <div>
                <h3 class="text-sm text-gray-500">{{ resource.name }}</h3>
                <p class="text-2xl font-semibold mt-1">{{ resource.total }}</p>
              </div>
              <div
                class="w-10 h-10 rounded-full flex items-center justify-center"
                :class="resource.iconBg"
              >
                <el-icon :class="resource.iconColor">
                  <component :is="resource.icon" />
                </el-icon>
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="h-2 rounded-full transition-all duration-300"
                :class="resource.progressColor"
                :style="{ width: resource.percentage + '%' }"
              ></div>
            </div>
            <div class="text-xs text-gray-500 mt-1">
              使用率: {{ resource.percentage }}%
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup>
  import { ref } from 'vue';
  import {
    Setting,
    Check,
    BellFilled,
    Promotion
  } from '@element-plus/icons-vue';

  const activeTab = ref('prod');

  const resources = [
    {
      name: 'CPU使用',
      total: '32核',
      percentage: 65,
      icon: Setting,
      iconBg: 'bg-blue-100',
      iconColor: 'text-primary',
      progressColor: 'bg-primary'
    },
    {
      name: '内存使用',
      total: '128GB',
      percentage: 42,
      icon: Check,
      iconBg: 'bg-green-100',
      iconColor: 'text-success',
      progressColor: 'bg-success'
    },
    {
      name: '磁盘使用',
      total: '2TB',
      percentage: 78,
      icon: BellFilled,
      iconBg: 'bg-yellow-100',
      iconColor: 'text-warning',
      progressColor: 'bg-warning'
    },
    {
      name: 'NAS使用',
      total: '5TB',
      percentage: 35,
      icon: Promotion,
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600',
      progressColor: 'bg-purple-600'
    }
  ];
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }

  .text-success {
    color: #00b42a;
  }

  .text-warning {
    color: #ff7d00;
  }

  .bg-primary {
    background-color: #165dff;
  }

  .bg-success {
    background-color: #00b42a;
  }

  .bg-warning {
    background-color: #ff7d00;
  }

  .bg-purple-600 {
    background-color: #722ed1;
  }

  .text-purple-600 {
    color: #722ed1;
  }
</style>
