<!-- 业务管理编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑应用系统' : '新建应用系统'"
    :size="1000"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基础信息 -->
        <div class="form-section-title">基础信息</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="业务名称" prop="bk_biz_name">
              <el-input
                v-model="form.bk_biz_name"
                placeholder="请输入业务名称"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属项目" prop="business_collection">
              <el-select
                v-model="form.business_collection"
                placeholder="请选择所属项目"
                style="width: 100%"
                :loading="projectLoading"
                filterable
                clearable
                @focus="loadProjects"
              >
                <el-option
                  v-for="project in projectOptions"
                  :key="project.value"
                  :label="project.label"
                  :value="project.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="系统级别" prop="system_level">
              <el-select
                v-model="form.system_level"
                placeholder="请选择系统级别"
                style="width: 100%"
              >
                <el-option label="A" value="A" />
                <el-option label="B" value="B" />
                <el-option label="C" value="C" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系统版本" prop="system_version">
              <el-input
                v-model="form.system_version"
                placeholder="请输入系统版本"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="系统介绍" prop="introduction">
              <el-input
                v-model="form.introduction"
                type="textarea"
                :rows="3"
                placeholder="请输入系统介绍"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="实现功能" prop="function">
              <el-input
                v-model="form.function"
                type="textarea"
                :rows="3"
                placeholder="请输入实现功能"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="开发负责人" prop="system_developer">
              <el-input
                v-model="form.system_developer"
                placeholder="请输入开发负责人"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运维负责人" prop="system_maintainer">
              <el-input
                v-model="form.system_maintainer"
                placeholder="请输入运维负责人"
                clearable
                :maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="开发厂商" prop="development_vendor">
              <el-input
                v-model="form.development_vendor"
                placeholder="请输入开发厂商"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="是否交维"
              prop="whether_handed_over_for_maintenance"
            >
              <el-radio-group
                v-model="form.whether_handed_over_for_maintenance"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="交维日期" prop="handover_date_for_maintenance">
              <el-date-picker
                v-model="form.handover_date_for_maintenance"
                type="date"
                placeholder="请选择交维日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否演练过" prop="whether_drilled">
              <el-radio-group v-model="form.whether_drilled">
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="演练日期" prop="drill_date">
              <el-date-picker
                v-model="form.drill_date"
                type="date"
                placeholder="请选择演练日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="是否有CFP框架"
              prop="whether_equipped_with_cfp_framework"
            >
              <el-radio-group
                v-model="form.whether_equipped_with_cfp_framework"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 技术栈 -->
        <div class="form-section-title">技术栈</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="使用系统框架" prop="used_system_framework">
              <el-input
                v-model="form.used_system_framework"
                placeholder="请输入使用的系统框架"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中间件" prop="middleware">
              <el-select
                v-model="form.middleware"
                multiple
                placeholder="请选择中间件"
                style="width: 100%"
                filterable
                allow-create
              >
                <el-option label="MQ" value="mq" />
                <el-option label="Nacos" value="nacos" />
                <el-option label="Redis" value="redis" />
                <el-option label="海豚" value="海豚" />
                <el-option label="Nginx" value="nginx" />
                <el-option label="FastDFS" value="fastdfs" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="公司配置数据库"
              prop="company_configuration_database"
            >
              <el-select
                v-model="form.company_configuration_database"
                multiple
                placeholder="请选择数据库"
                style="width: 100%"
                filterable
                allow-create
              >
                <el-option label="MySQL8" value="mysql8" />
                <el-option label="Oracle11g" value="oracle11g" />
                <el-option label="Oracle19c" value="oracle19c" />
                <el-option label="TDSQL" value="tdsql" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开发语言" prop="development_language">
              <el-select
                v-model="form.development_language"
                multiple
                placeholder="请选择开发语言"
                style="width: 100%"
                filterable
                allow-create
              >
                <el-option label="Java" value="java" />
                <el-option label="C" value="c" />
                <el-option label="Python" value="python" />
                <el-option label="Go" value="go" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 应用部署情况 -->
        <div class="form-section-title">应用部署情况</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="部署方式" prop="deployment_method">
              <el-select
                v-model="form.deployment_method"
                multiple
                placeholder="请选择部署方式"
                style="width: 100%"
              >
                <el-option label="PAAS" value="paas" />
                <el-option label="虚拟机集群" value="虚拟机集群" />
                <el-option label="虚拟机单机" value="虚拟机单机" />
                <el-option label="Docker单机" value="docker单机" />
                <el-option label="Docker集群" value="docker集群" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用双活" prop="application_active_active">
              <el-radio-group v-model="form.application_active_active">
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="应用部署机房"
              prop="application_deployment_computer_room"
            >
              <el-select
                v-model="form.application_deployment_computer_room"
                multiple
                placeholder="请选择应用部署机房"
                style="width: 100%"
              >
                <el-option label="主机房" value="主机房" />
                <el-option label="备机房" value="备机房" />
                <el-option label="贵阳机房" value="贵阳机房" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用备份方式" prop="application_backup_method">
              <el-select
                v-model="form.application_backup_method"
                multiple
                placeholder="请选择应用备份方式"
                style="width: 100%"
                filterable
                allow-create
              >
                <el-option label="热备" value="热备" />
                <el-option label="冷备" value="冷备" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="应用备份工具" prop="application_backup_tool">
              <el-input
                v-model="form.application_backup_tool"
                placeholder="请输入应用备份工具"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用备份周期" prop="application_backup_cycle">
              <el-input
                v-model="form.application_backup_cycle"
                placeholder="请输入应用备份周期"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="备份文件所在机房"
              prop="computer_room_for_application_backup_files"
            >
              <el-select
                v-model="form.computer_room_for_application_backup_files"
                multiple
                placeholder="请选择应用备份文件所在机房"
                style="width: 100%"
              >
                <el-option label="主机房" value="主机房" />
                <el-option label="备机房" value="备机房" />
                <el-option label="贵阳机房" value="贵阳机房" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="备份文件地址"
              prop="address_of_application_backup_files"
            >
              <el-input
                v-model="form.address_of_application_backup_files"
                placeholder="请输入应用备份文件地址"
                clearable
                :maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 数据库部署情况 -->
        <div class="form-section-title">数据库部署情况</div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="数据库双活" prop="database_active_active">
              <el-radio-group v-model="form.database_active_active">
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="数据库部署机房"
              prop="database_deployment_computer_room"
            >
              <el-select
                v-model="form.database_deployment_computer_room"
                multiple
                placeholder="请选择数据库部署机房"
                style="width: 100%"
              >
                <el-option label="主机房" value="主机房" />
                <el-option label="备机房" value="备机房" />
                <el-option label="贵阳机房" value="贵阳机房" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="数据库版本" prop="database_version">
              <el-input
                v-model="form.database_version"
                placeholder="请输入数据库版本"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库备份方式" prop="database_backup_method">
              <el-select
                v-model="form.database_backup_method"
                multiple
                placeholder="请选择数据库备份方式"
                style="width: 100%"
                filterable
                allow-create
              >
                <el-option label="DG" value="DG" />
                <el-option label="双活" value="双活" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="数据库备份工具" prop="database_backup_tool">
              <el-input
                v-model="form.database_backup_tool"
                placeholder="请输入数据库备份工具"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库备份周期" prop="database_backup_cycle">
              <el-input
                v-model="form.database_backup_cycle"
                placeholder="请输入数据库备份周期"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="备份文件所在机房"
              prop="computer_room_for_database_backup_files"
            >
              <el-select
                v-model="form.computer_room_for_database_backup_files"
                multiple
                placeholder="请选择数据库备份文件所在机房"
                style="width: 100%"
              >
                <el-option label="主机房" value="主机房" />
                <el-option label="备机房" value="备机房" />
                <el-option label="贵阳机房" value="贵阳机房" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="备份文件地址"
              prop="address_of_database_backup_files"
            >
              <el-input
                v-model="form.address_of_database_backup_files"
                placeholder="请输入数据库备份文件地址"
                clearable
                :maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="业务逻辑图" prop="business_logic_diagram">
              <div
                class="image-upload-container"
                @paste="(event) => handlePaste('business_logic', event)"
                @dragover.prevent
                @drop.prevent="(event) => handleDrop('business_logic', event)"
                tabindex="0"
              >
                <el-upload
                  ref="businessLogicUploadRef"
                  v-model:file-list="businessLogicFileList"
                  :http-request="
                    (options) => handleImageUpload(options, 'business_logic')
                  "
                  :before-upload="beforeBusinessLogicUpload"
                  :on-exceed="handleBusinessLogicExceed"
                  :on-remove="
                    (file, fileList) =>
                      handleImageRemove(file, fileList, 'business_logic')
                  "
                  :on-preview="handleImagePreview"
                  list-type="picture-card"
                  accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
                  :limit="1"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip">
                      大小不超过10MB，只能上传1张图片<br />
                      <span class="paste-tip"
                        >支持 Ctrl+V 粘贴图片或拖拽上传</span
                      >
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技术流程图" prop="technical_flow_diagram">
              <div
                class="image-upload-container"
                @paste="(event) => handlePaste('technical_flow', event)"
                @dragover.prevent
                @drop.prevent="(event) => handleDrop('technical_flow', event)"
                tabindex="0"
              >
                <el-upload
                  ref="technicalFlowUploadRef"
                  v-model:file-list="technicalFlowFileList"
                  :http-request="
                    (options) => handleImageUpload(options, 'technical_flow')
                  "
                  :before-upload="beforeTechnicalFlowUpload"
                  :on-exceed="handleTechnicalFlowExceed"
                  :on-remove="
                    (file, fileList) =>
                      handleImageRemove(file, fileList, 'technical_flow')
                  "
                  :on-preview="handleImagePreview"
                  list-type="picture-card"
                  accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
                  :limit="1"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip">
                      大小不超过 10MB，只能上传1张图片<br />
                      <span class="paste-tip"
                        >支持 Ctrl+V 粘贴图片或拖拽上传</span
                      >
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 附件上传 -->
        <div class="form-section-title">附件</div>

        <el-form-item label="文档" prop="documents">
          <div
            class="document-upload-container"
            @paste="(event) => handlePaste('documents', event)"
            @dragover.prevent
            @drop.prevent="(event) => handleDrop('documents', event)"
            tabindex="0"
          >
            <el-upload
              ref="documentsUploadRef"
              v-model:file-list="documentsFileList"
              :http-request="(options) => handleDocumentUpload(options)"
              :on-remove="
                (file, fileList) => handleDocumentRemove(file, fileList)
              "
              :on-preview="handleDocumentPreview"
              list-type="text"
              multiple
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持拖拽上传，不限制文件类型和大小<br />
                  <span class="paste-tip">支持拖拽任意文件上传</span>
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="70%"
      append-to-body
    >
      <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
    </el-dialog>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { Plus } from '@element-plus/icons-vue';
  import { createBusiness, updateBusiness, searchAllInst } from '@/api/cmdb';
  import { uploadAttachment } from '@/api/upload';

  defineOptions({ name: 'BusinessFormNew' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    formData: Object,
    /** 是否为编辑模式 */
    isEdit: Boolean
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'business';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.isEdit && props.formData != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 图片预览相关 */
  const imagePreviewVisible = ref(false);
  const previewImageUrl = ref('');

  /** 附件文件列表 */
  const businessLogicFileList = ref([]);
  const technicalFlowFileList = ref([]);
  const documentsFileList = ref([]);

  /** 项目选项 */
  const projectOptions = ref([]);
  const projectLoading = ref(false);

  /** 表单数据 */
  const form = ref({
    bk_biz_id: '', // 添加ID字段用于编辑
    bk_biz_name: '',
    business_collection: '',
    system_level: '',
    system_version: '',
    introduction: '',
    function: '',
    system_developer: '',
    system_maintainer: '',
    development_vendor: '',
    whether_handed_over_for_maintenance: '否',
    handover_date_for_maintenance: '',
    whether_drilled: '否',
    drill_date: '',
    whether_equipped_with_cfp_framework: '否',
    used_system_framework: '',
    middleware: [],
    company_configuration_database: [],
    development_language: [],
    deployment_method: [],
    application_active_active: '否',
    application_deployment_computer_room: [],
    application_backup_method: [],
    application_backup_tool: '',
    application_backup_cycle: '',
    computer_room_for_application_backup_files: [],
    address_of_application_backup_files: '',
    database_active_active: '否',
    database_deployment_computer_room: [],
    database_version: '',
    database_backup_method: [],
    database_backup_tool: '',
    database_backup_cycle: '',
    computer_room_for_database_backup_files: [],
    address_of_database_backup_files: '',
    business_logic_diagram: [],
    technical_flow_diagram: [],
    documents: []
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    const baseRules = {
      bk_biz_name: [
        { required: true, message: '请输入业务名称', trigger: 'blur' }
      ],
      business_collection: [
        { required: true, message: '请选择所属项目', trigger: 'change' }
      ],
      system_level: [
        { required: true, message: '请选择系统级别', trigger: 'change' }
      ],
      system_version: [
        { required: true, message: '请输入系统版本', trigger: 'blur' }
      ],
      introduction: [
        { required: true, message: '请输入系统介绍', trigger: 'blur' }
      ],
      function: [
        { required: true, message: '请输入实现功能', trigger: 'blur' }
      ],
      system_developer: [
        { required: true, message: '请输入开发负责人', trigger: 'blur' }
      ],
      system_maintainer: [
        { required: true, message: '请输入运维负责人', trigger: 'blur' }
      ],
      development_vendor: [
        { required: true, message: '请输入开发厂商', trigger: 'blur' }
      ],
      whether_handed_over_for_maintenance: [
        { required: true, message: '请选择是否交维', trigger: 'change' }
      ],
      whether_drilled: [
        { required: true, message: '请选择是否演练过', trigger: 'change' }
      ],
      deployment_method: [
        { required: true, message: '请选择部署方式', trigger: 'change' }
      ],
      application_active_active: [
        { required: true, message: '请选择应用双活', trigger: 'change' }
      ],
      application_deployment_computer_room: [
        { required: true, message: '请选择应用部署机房', trigger: 'change' }
      ],
      application_backup_method: [
        { required: true, message: '请选择应用备份方式', trigger: 'change' }
      ],
      computer_room_for_application_backup_files: [
        {
          required: true,
          message: '请选择应用备份文件所在机房',
          trigger: 'change'
        }
      ],
      database_active_active: [
        { required: true, message: '请选择数据库双活', trigger: 'change' }
      ],
      database_deployment_computer_room: [
        { required: true, message: '请选择数据库部署机房', trigger: 'change' }
      ],
      database_backup_method: [
        { required: true, message: '请选择数据库备份方式', trigger: 'change' }
      ],
      computer_room_for_database_backup_files: [
        {
          required: true,
          message: '请选择数据库备份文件所在机房',
          trigger: 'change'
        }
      ]
    };

    // 条件性必填字段
    if (form.value.whether_handed_over_for_maintenance === '是') {
      baseRules.handover_date_for_maintenance = [
        { required: true, message: '请选择交维日期', trigger: 'change' }
      ];
    }

    if (form.value.whether_drilled === '是') {
      baseRules.drill_date = [
        { required: true, message: '请选择演练日期', trigger: 'change' }
      ];
    }

    return baseRules;
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const submitData = { ...form.value };

        // 处理数组字段，用逗号拼接
        Object.keys(submitData).forEach((key) => {
          if (Array.isArray(submitData[key])) {
            if (
              key === 'business_logic_diagram' ||
              key === 'technical_flow_diagram'
            ) {
              // 图片字段处理为逗号分隔的URL字符串
              submitData[key] = submitData[key].join(',');
            } else if (key === 'documents') {
              // 文档字段处理为JSON字符串
              submitData[key] = JSON.stringify(submitData[key]);
            } else {
              // 其他数组字段用逗号拼接
              submitData[key] = submitData[key].join(',');
            }
          }
        });

        // 包装为指定格式
        const requestData = {
          bizInfoMap: submitData
        };

        if (isUpdate.value) {
          // 编辑模式：添加bk_biz_id到请求参数中
          const updateData = {
            bizInfoMap: {
              ...submitData,
              bk_biz_id: submitData.bk_biz_id || props.formData?.bk_biz_id
            }
          };
          await updateBusiness(updateData);
          EleMessage.success('修改成功');
        } else {
          // 新建模式
          await createBusiness(requestData);
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    resetFormToDefault();
  };

  /** 加载项目数据 */
  const loadProjects = async () => {
    projectLoading.value = true;
    try {
      const formData = new FormData();
      formData.append('bkObjId', 'project_detail');

      const res = await searchAllInst(formData);
      if (res && res.rows && Array.isArray(res.rows)) {
        projectOptions.value = res.rows.map((item) => ({
          label: item.project_name || item.bk_inst_name || '未知项目',
          value: item.project_name || item.bk_inst_name || '未知项目'
        }));
      } else {
        projectOptions.value = [];
      }
    } catch (error) {
      console.error('加载项目数据失败:', error);
      EleMessage.error('加载项目数据失败: ' + (error.message || '未知错误'));
      projectOptions.value = [];
    } finally {
      projectLoading.value = false;
    }
  };

  /** 图片上传前校验 */
  const beforeImageUpload = (file) => {
    const validTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
    const isValidType = validTypes.includes(file.type);
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isValidType) {
      EleMessage.error('只能上传 jpg/jpeg/png/gif/bmp/webp 格式的图片文件!');
      return false;
    }
    if (!isLt10M) {
      EleMessage.error('上传图片大小不能超过 10MB!');
      return false;
    }
    return true;
  };

  /** 业务逻辑图上传前处理 */
  const beforeBusinessLogicUpload = (file) => {
    // 如果已有图片，清空文件列表以实现覆盖
    if (businessLogicFileList.value.length > 0) {
      businessLogicFileList.value = [];
      form.value.business_logic_diagram = [];
      EleMessage.info('新图片将覆盖原有图片');
    }
    return beforeImageUpload(file);
  };

  /** 技术流程图上传前处理 */
  const beforeTechnicalFlowUpload = (file) => {
    // 如果已有图片，清空文件列表以实现覆盖
    if (technicalFlowFileList.value.length > 0) {
      technicalFlowFileList.value = [];
      form.value.technical_flow_diagram = [];
      EleMessage.info('新图片将覆盖原有图片');
    }
    return beforeImageUpload(file);
  };

  /** 业务逻辑图超出限制处理 */
  const handleBusinessLogicExceed = (files) => {
    const file = files[0]; // 取第一个文件
    if (beforeImageUpload(file)) {
      // 清空现有文件列表
      businessLogicFileList.value = [];
      form.value.business_logic_diagram = [];
      EleMessage.info('新图片将覆盖原有图片');

      // 手动创建文件项并上传
      const fileItem = {
        name: file.name,
        raw: file,
        status: 'uploading',
        uid: Date.now()
      };
      businessLogicFileList.value.push(fileItem);

      // 执行上传
      handleImageUpload(
        {
          file: file,
          onSuccess: (response) => {
            const index = businessLogicFileList.value.findIndex(
              (f) => f.uid === fileItem.uid
            );
            if (index !== -1) {
              businessLogicFileList.value[index].status = 'success';
              businessLogicFileList.value[index].url = response.data.url;
            }
            form.value.business_logic_diagram = [response.data.url];
          },
          onError: (error) => {
            const index = businessLogicFileList.value.findIndex(
              (f) => f.uid === fileItem.uid
            );
            if (index !== -1) {
              businessLogicFileList.value.splice(index, 1);
            }
            EleMessage.error('图片上传失败: ' + error.message);
          }
        },
        'business_logic'
      );
    }
  };

  /** 技术流程图超出限制处理 */
  const handleTechnicalFlowExceed = (files) => {
    const file = files[0]; // 取第一个文件
    if (beforeImageUpload(file)) {
      // 清空现有文件列表
      technicalFlowFileList.value = [];
      form.value.technical_flow_diagram = [];
      EleMessage.info('新图片将覆盖原有图片');

      // 手动创建文件项并上传
      const fileItem = {
        name: file.name,
        raw: file,
        status: 'uploading',
        uid: Date.now()
      };
      technicalFlowFileList.value.push(fileItem);

      // 执行上传
      handleImageUpload(
        {
          file: file,
          onSuccess: (response) => {
            const index = technicalFlowFileList.value.findIndex(
              (f) => f.uid === fileItem.uid
            );
            if (index !== -1) {
              technicalFlowFileList.value[index].status = 'success';
              technicalFlowFileList.value[index].url = response.data.url;
            }
            form.value.technical_flow_diagram = [response.data.url];
          },
          onError: (error) => {
            const index = technicalFlowFileList.value.findIndex(
              (f) => f.uid === fileItem.uid
            );
            if (index !== -1) {
              technicalFlowFileList.value.splice(index, 1);
            }
            EleMessage.error('图片上传失败: ' + error.message);
          }
        },
        'technical_flow'
      );
    }
  };

  /** 图片上传处理 */
  const handleImageUpload = async (options, type) => {
    const { file, onSuccess, onError } = options;
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'business');

      const response = await uploadAttachment(formData);
      if (response.code === 200) {
        onSuccess(response, file);

        // 更新文件列表中当前文件的url
        const fileList =
          type === 'business_logic'
            ? businessLogicFileList
            : technicalFlowFileList;
        const fileIndex = fileList.value.findIndex((f) => f.uid === file.uid);
        if (fileIndex !== -1) {
          fileList.value[fileIndex].url = response.data.url;
        }

        // 限制为单张图片，更新表单数据时只保存第一张图片的URL
        const urls = fileList.value.map((f) => f.url).filter(Boolean);
        const singleUrl = urls.length > 0 ? [urls[0]] : [];

        if (type === 'business_logic') {
          form.value.business_logic_diagram = singleUrl;
        } else if (type === 'technical_flow') {
          form.value.technical_flow_diagram = singleUrl;
        }

        EleMessage.success('图片上传成功');
      } else {
        onError(new Error(response.msg || '上传失败'));
      }
    } catch (error) {
      onError(error);
      EleMessage.error('图片上传失败: ' + error.message);
    }
  };

  /** 图片删除处理 */
  const handleImageRemove = (file, fileList, type) => {
    if (type === 'business_logic') {
      businessLogicFileList.value = fileList;
      const urls = fileList.map((f) => f.url).filter(Boolean);
      form.value.business_logic_diagram = urls;
    } else if (type === 'technical_flow') {
      technicalFlowFileList.value = fileList;
      const urls = fileList.map((f) => f.url).filter(Boolean);
      form.value.technical_flow_diagram = urls;
    }
  };

  /** 图片预览 */
  const handleImagePreview = (file) => {
    previewImageUrl.value = file.url || file.response?.data?.url || '';
    imagePreviewVisible.value = true;
  };

  /** 文档上传处理 */
  const handleDocumentUpload = async (options) => {
    const { file, onSuccess, onError } = options;
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'business');

      const response = await uploadAttachment(formData);
      if (response.code === 200) {
        onSuccess(response, file);

        // 更新文件列表中当前文件的url
        const fileIndex = documentsFileList.value.findIndex(
          (f) => f.uid === file.uid
        );
        if (fileIndex !== -1) {
          documentsFileList.value[fileIndex].url = response.data.url;
        }

        // 更新表单数据
        const documents = documentsFileList.value
          .map((f) => ({
            ossId: f.ossId || '',
            fileName: f.name,
            url: f.url
          }))
          .filter((item) => item.url);
        form.value.documents = documents;

        EleMessage.success('文档上传成功');
      } else {
        onError(new Error(response.msg || '上传失败'));
      }
    } catch (error) {
      onError(error);
      EleMessage.error('文档上传失败: ' + error.message);
    }
  };

  /** 文档删除处理 */
  const handleDocumentRemove = (file, fileList) => {
    documentsFileList.value = fileList;
    const documents = fileList
      .map((f) => ({
        ossId: f.ossId || '',
        fileName: f.name,
        url: f.url
      }))
      .filter((item) => item.url);
    form.value.documents = documents;
  };

  /** 文档预览 */
  const handleDocumentPreview = (file) => {
    const url = file.url || file.response?.data?.url || '';
    if (url) {
      window.open(url, '_blank');
    }
  };

  /** 处理粘贴和拖拽 */
  const handlePaste = (type, event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file) {
          uploadPastedFile(file, type);
        }
        break;
      }
    }
  };

  const handleDrop = (type, event) => {
    const files = event.dataTransfer?.files;
    if (!files || files.length === 0) return;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (type === 'documents') {
        uploadPastedDocumentFile(file);
      } else if (file.type.indexOf('image') !== -1) {
        uploadPastedFile(file, type);
      }
    }
  };

  /** 上传粘贴/拖拽的图片文件 */
  const uploadPastedFile = async (file, type) => {
    const fileList =
      type === 'business_logic' ? businessLogicFileList : technicalFlowFileList;

    // 限制只能上传1张图片，如果已有图片则覆盖
    if (fileList.value.length >= 1) {
      EleMessage.warning('只能上传1张图片，新图片将覆盖原有图片');
      // 清空原有文件列表
      fileList.value = [];
    }

    if (!beforeImageUpload(file)) {
      return;
    }

    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'png';
    const fileName = `paste_${timestamp}.${extension}`;
    const newFile = new File([file], fileName, { type: file.type });

    const fileItem = {
      name: fileName,
      raw: newFile,
      status: 'uploading',
      uid: Date.now() + Math.random()
    };

    fileList.value.push(fileItem);

    try {
      await handleImageUpload(
        {
          file: newFile,
          onSuccess: (response) => {
            const index = fileList.value.findIndex(
              (f) => f.uid === fileItem.uid
            );
            if (index !== -1) {
              fileList.value[index].status = 'success';
              fileList.value[index].url = response.data.url;
            }

            const urls = fileList.value.map((f) => f.url).filter(Boolean);
            if (type === 'business_logic') {
              form.value.business_logic_diagram = urls;
            } else if (type === 'technical_flow') {
              form.value.technical_flow_diagram = urls;
            }
          },
          onError: (error) => {
            const index = fileList.value.findIndex(
              (f) => f.uid === fileItem.uid
            );
            if (index !== -1) {
              fileList.value.splice(index, 1);
            }
            EleMessage.error('图片上传失败: ' + error.message);
          }
        },
        type
      );
    } catch (error) {
      const index = fileList.value.findIndex((f) => f.uid === fileItem.uid);
      if (index !== -1) {
        fileList.value.splice(index, 1);
      }
      EleMessage.error('图片上传失败');
    }
  };

  /** 上传拖拽的文档文件 */
  const uploadPastedDocumentFile = async (file) => {
    const timestamp = Date.now();
    const fileName = file.name || `document_${timestamp}`;

    const fileItem = {
      name: fileName,
      raw: file,
      status: 'uploading',
      uid: Date.now() + Math.random()
    };

    documentsFileList.value.push(fileItem);

    try {
      await handleDocumentUpload({
        file: file,
        onSuccess: (response) => {
          const index = documentsFileList.value.findIndex(
            (f) => f.uid === fileItem.uid
          );
          if (index !== -1) {
            documentsFileList.value[index].status = 'success';
            documentsFileList.value[index].url = response.data.url;
          }

          const documents = documentsFileList.value
            .map((f) => ({
              ossId: f.ossId || '',
              fileName: f.name,
              url: f.url
            }))
            .filter((item) => item.url);
          form.value.documents = documents;
        },
        onError: (error) => {
          const index = documentsFileList.value.findIndex(
            (f) => f.uid === fileItem.uid
          );
          if (index !== -1) {
            documentsFileList.value.splice(index, 1);
          }
          EleMessage.error('文档上传失败: ' + error.message);
        }
      });
    } catch (error) {
      const index = documentsFileList.value.findIndex(
        (f) => f.uid === fileItem.uid
      );
      if (index !== -1) {
        documentsFileList.value.splice(index, 1);
      }
      EleMessage.error('文档上传失败');
    }
  };

  /** 初始化表单数据 */
  const initializeFormData = (data) => {
    if (data) {
      // 复制基础数据
      Object.keys(form.value).forEach((key) => {
        if (data[key] !== undefined) {
          // 处理字符串形式的数组字段
          if (
            Array.isArray(form.value[key]) &&
            typeof data[key] === 'string' &&
            data[key]
          ) {
            form.value[key] = data[key]
              .split(',')
              .map((item) => item.trim())
              .filter(Boolean);
          } else {
            form.value[key] = data[key];
          }
        }
      });

      // 处理业务逻辑图回显 - 只显示第一张图片
      if (data.business_logic_diagram) {
        const urls = Array.isArray(data.business_logic_diagram)
          ? data.business_logic_diagram
          : data.business_logic_diagram.split(',').filter((url) => url.trim());

        // 只保留第一张图片
        const firstUrl = urls.length > 0 ? [urls[0]] : [];

        businessLogicFileList.value = firstUrl.map((url, index) => ({
          name: `business_logic`,
          url: url,
          uid: Date.now() + index
        }));
        form.value.business_logic_diagram = firstUrl;
      }

      // 处理技术流程图回显 - 只显示第一张图片
      if (data.technical_flow_diagram) {
        const urls = Array.isArray(data.technical_flow_diagram)
          ? data.technical_flow_diagram
          : data.technical_flow_diagram.split(',').filter((url) => url.trim());

        // 只保留第一张图片
        const firstUrl = urls.length > 0 ? [urls[0]] : [];

        technicalFlowFileList.value = firstUrl.map((url, index) => ({
          name: `technical_flow`,
          url: url,
          uid: Date.now() + index + 1000
        }));
        form.value.technical_flow_diagram = firstUrl;
      }

      // 处理文档回显
      if (data.documents) {
        let documents = [];
        if (typeof data.documents === 'string') {
          try {
            documents = JSON.parse(data.documents);
          } catch {
            documents = [{ url: data.documents, fileName: '文档', ossId: '' }];
          }
        } else if (Array.isArray(data.documents)) {
          documents = data.documents;
        }

        documentsFileList.value = documents.map((doc, index) => ({
          name: doc.fileName || `document_${index + 1}`,
          url: doc.url,
          ossId: doc.ossId || '',
          uid: Date.now() + index + 2000
        }));
        form.value.documents = documents;
      }
    } else {
      // 重置表单到初始状态
      resetFormToDefault();
    }
  };

  /** 重置表单到默认状态 */
  const resetFormToDefault = () => {
    Object.keys(form.value).forEach((key) => {
      if (Array.isArray(form.value[key])) {
        form.value[key] = [];
      } else if (
        key === 'whether_handed_over_for_maintenance' ||
        key === 'whether_drilled' ||
        key === 'whether_equipped_with_cfp_framework' ||
        key === 'application_active_active' ||
        key === 'database_active_active'
      ) {
        form.value[key] = '否';
      } else if (key === 'bk_biz_id') {
        // 新建模式下清空ID
        form.value[key] = '';
      } else {
        form.value[key] = '';
      }
    });
    businessLogicFileList.value = [];
    technicalFlowFileList.value = [];
    documentsFileList.value = [];
  };

  /** 监听编辑数据变化 */
  watch(
    () => props.formData,
    (newData) => {
      initializeFormData(newData);
    },
    { immediate: true }
  );

  /** 监听弹窗打开状态 */
  watch(
    () => props.modelValue,
    (visible) => {
      if (visible && !props.isEdit) {
        // 新建模式下重置表单
        resetFormToDefault();
      }
    }
  );

  /** 初始化时加载项目数据 */
  onMounted(() => {
    loadProjects();
  });
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }

  .image-upload-container,
  .document-upload-container {
    position: relative;
    border: 2px dashed transparent;
    border-radius: 6px;
    transition: all 0.3s ease;
    outline: none;
    padding: 10px;
  }

  .image-upload-container:focus,
  .document-upload-container:focus {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .image-upload-container:hover,
  .document-upload-container:hover {
    border-color: var(--el-color-primary-light-3);
  }

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  .paste-tip {
    color: var(--el-color-primary);
    font-weight: 500;
  }
</style>
