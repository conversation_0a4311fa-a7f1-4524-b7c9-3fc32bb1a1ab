<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex items-center">
        <el-icon class="text-primary mr-2"><Tools /></el-icon>
        <span class="text-lg font-semibold">维保信息</span>
      </div>
    </template>
    <div class="p-4">
      <div class="space-y-4">
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-2">维保方式</h3>
          <el-tag type="primary" effect="light">厂商远程</el-tag>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-2">维保期限</h3>
          <div class="flex items-center justify-between mb-2">
            <span>2023-01-01 至 2024-12-31</span>
            <div class="flex items-center text-warning text-sm">
              <el-icon class="mr-1"><WarningFilled /></el-icon>
              <span>剩余85天</span>
            </div>
          </div>
          <el-progress :percentage="80" color="#FF7D00" />
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-2"
            >维保厂家/联系人</h3
          >
          <p>神州数码科技有限公司 / 陈经理 (13800138000)</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-2">维保费用</h3>
          <p class="text-lg font-semibold">¥120,000.00/年</p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500 mb-2">维保服务</h3>
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <el-icon class="text-success mr-1"><Check /></el-icon>
              <span class="text-sm">维保验收材料</span>
            </div>
            <div class="flex items-center">
              <el-icon class="text-success mr-1"><Check /></el-icon>
              <span class="text-sm">到期提醒</span>
            </div>
          </div>
        </div>
        <el-button
          type="primary"
          class="w-full"
          @click="viewMaintenanceRecords"
        >
          <el-icon class="mr-2"><Document /></el-icon>
          查看维保服务记录
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
  import {
    Tools,
    WarningFilled,
    Check,
    Document
  } from '@element-plus/icons-vue';

  const viewMaintenanceRecords = () => {
    console.log('查看维保服务记录');
  };
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }

  .text-warning {
    color: #ff7d00;
  }

  .text-success {
    color: #00b42a;
  }

  .space-y-4 > * + * {
    margin-top: 1rem;
  }
</style>
