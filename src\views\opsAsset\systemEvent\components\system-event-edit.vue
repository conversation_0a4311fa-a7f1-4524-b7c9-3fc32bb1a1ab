<!-- 系统事件编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑系统事件' : '新建系统事件'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="事件标题" prop="event_name">
              <el-input
                v-model="form.event_name"
                placeholder="请输入事件标题"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生时间" prop="occurrence_time">
              <el-date-picker
                v-model="form.occurrence_time"
                type="date"
                placeholder="选择发生时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleOccurrenceTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="事件来源" prop="event_source">
              <el-input
                v-model="form.event_source"
                placeholder="请输入事件来源"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件级别" prop="event_level">
              <el-select
                v-model="form.event_level"
                placeholder="请选择事件级别"
                style="width: 100%"
              >
                <el-option label="I级" value="I" />
                <el-option label="II级" value="II" />
                <el-option label="III级" value="III" />
                <el-option label="IV级" value="IV" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="影响范围" prop="impact_scope">
              <el-input
                v-model="form.impact_scope"
                placeholder="请输入影响范围"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="影响业务" prop="affected_business">
              <el-input
                v-model="form.affected_business"
                placeholder="请输入影响业务"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="事件现象描述" prop="event_desc">
          <el-input
            v-model="form.event_desc"
            type="textarea"
            :rows="3"
            placeholder="请输入事件现象描述"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="事件现象截图" prop="event_screenshot">
          <div
            class="screenshot-upload-container"
            @paste="handlePaste"
            @dragover.prevent
            @drop.prevent="handleDrop"
            tabindex="0"
          >
            <el-upload
              ref="screenshotUploadRef"
              v-model:file-list="screenshotFileList"
              :http-request="handleScreenshotUpload"
              :before-upload="beforeScreenshotUpload"
              :on-remove="handleScreenshotRemove"
              :on-preview="handleScreenshotPreview"
              list-type="picture-card"
              accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
              :limit="10"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  支持 jpg/jpeg/png/gif/bmp/webp 格式，大小不超过 10MB<br />
                  <span class="paste-tip">支持 Ctrl+V 粘贴图片或拖拽上传</span>
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>

        <!-- 故障信息 -->
        <div class="form-section-title">故障信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="故障类别" prop="fault_category">
              <el-select
                v-model="form.fault_category"
                placeholder="请选择故障类别"
                style="width: 100%"
                @change="handleFaultCategoryChange"
              >
                <el-option label="应用服务故障" value="应用服务故障" />
                <el-option label="上线/变更引发" value="上线/变更引发" />
                <el-option label="应用设计缺陷" value="应用设计缺陷2" />
                <el-option label="业务需求缺陷" value="业务需求缺陷" />
                <el-option label="外部系统异常" value="外部系统异常" />
                <el-option label="数据库故障" value="数据库故障" />
                <el-option label="基础网络故障" value="基础网络故障" />
                <el-option label="基础硬件故障" value="基础硬件故障" />
                <el-option label="中间件故障" value="中间件故障" />
                <el-option label="运营商线路异常" value="运营商线路异常" />
                <el-option label="产品/政策配置" value="产品/政策配置" />
                <el-option label="操作/咨询类" value="操作/咨询类" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="根因故障系统" prop="root_cause_fault_system">
              <el-select
                v-model="form.root_cause_fault_system"
                placeholder="请选择根因故障系统"
                style="width: 100%"
                @change="handleSystemChange"
                filterable
                :loading="systemLoading"
              >
                <el-option
                  v-for="system in businessSystems"
                  :key="system.value"
                  :label="system.label"
                  :value="system.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 变更相关字段 (仅当故障类别为"上线/变更引发"时显示) -->
        <template v-if="form.fault_category === '上线/变更引发'">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item
                label="引发故障的变更单号"
                prop="change_order_no_causing_fault"
              >
                <el-input
                  v-model="form.change_order_no_causing_fault"
                  placeholder="请输入变更单号"
                  clearable
                  :maxlength="100"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="变更申请人" prop="change_requester">
                <el-input
                  v-model="form.change_requester"
                  placeholder="请输入变更申请人"
                  clearable
                  :maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="开发供应商" prop="development_supplier">
                <el-input
                  v-model="form.development_supplier"
                  placeholder="请输入开发供应商"
                  clearable
                  :maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-form-item label="事件原因" prop="event_cause">
          <el-input
            v-model="form.event_cause"
            type="textarea"
            :rows="3"
            placeholder="请输入事件原因"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 处理信息 -->
        <div class="form-section-title">处理信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="科创处置人员" prop="dev_handler">
              <el-input
                v-model="form.dev_handler"
                placeholder="请输入科创处置人员"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运维处置人员" prop="ops_handler">
              <el-input
                v-model="form.ops_handler"
                placeholder="请输入运维处置人员"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="事件处理过程" prop="event_handling_process">
          <el-input
            v-model="form.event_handling_process"
            type="textarea"
            :rows="3"
            placeholder="请输入事件处理过程"
            :maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item
          label="事件处理结果和依据"
          prop="event_handling_results_and_basis"
        >
          <el-input
            v-model="form.event_handling_results_and_basis"
            type="textarea"
            :rows="3"
            placeholder="请输入事件处理结果和依据"
            :maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="解决时间" prop="resolution_time">
              <el-date-picker
                v-model="form.resolution_time"
                type="datetime"
                placeholder="选择解决时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="涉及生产变更"
              prop="solution_and_whether_production_change_is_involved"
            >
              <el-radio-group
                v-model="
                  form.solution_and_whether_production_change_is_involved
                "
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="涉及OA流程"
              prop="solution_and_whether_oa_process_is_involved"
            >
              <el-radio-group
                v-model="form.solution_and_whether_oa_process_is_involved"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="是否进行复盘"
              prop="whether_review_is_conducted"
            >
              <el-radio-group
                v-model="form.whether_review_is_conducted"
                @change="handleReviewChange"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="复盘会召开日期" prop="review_meeting_date">
              <el-date-picker
                v-model="form.review_meeting_date"
                type="date"
                placeholder="选择复盘会召开日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                :disabled="form.whether_review_is_conducted !== '是'"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 事件报告上传 -->
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              label="是否形成事件报告"
              prop="whether_event_report_is_formed"
            >
              <el-radio-group
                v-model="form.whether_event_report_is_formed"
                @change="handleEventReportChange"
              >
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 事件报告上传 -->
        <el-form-item label="事件报告" prop="event_report">
          <el-upload
            ref="reportUploadRef"
            v-model:file-list="reportFileList"
            :http-request="handleReportUpload"
            :before-upload="beforeReportUpload"
            :on-remove="handleReportRemove"
            accept=".pdf,.doc,.docx"
            :limit="1"
          >
            <el-button
              type="primary"
              :disabled="form.whether_event_report_is_formed === '否'"
              >上传事件报告</el-button
            >
            <template #tip>
              <div class="el-upload__tip">
                支持 pdf/doc/docx 格式，大小不超过 50MB
              </div>
            </template>
          </el-upload>

          <!-- 已上传报告预览 -->
          <div v-if="reportFileList.length > 0" class="report-preview-section">
            <div class="report-preview-title">已上传报告：</div>
            <div
              v-for="file in reportFileList"
              :key="file.uid || file.url"
              class="report-preview-item"
            >
              <el-icon class="report-icon"><Document /></el-icon>
              <span class="report-name">{{ file.name || '事件报告' }}</span>
              <el-button type="primary" link @click="handleReportPreview(file)">
                查看
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>

  <!-- 图片预览对话框 -->
  <el-dialog
    v-model="imagePreviewVisible"
    title="图片预览"
    width="80%"
    :destroy-on-close="true"
  >
    <div class="image-preview-container">
      <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
    </div>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch, onMounted, nextTick, h } from 'vue';
  import { Plus, Document, Download } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';
  import { ElMessageBox } from 'element-plus';
  import { useUserStore } from '@/store/modules/user';
  import { createInst, updateInst, searchBusiness } from '@/api/cmdb';
  import { uploadAttachment } from '@/api/upload';

  defineOptions({ name: 'SystemEventEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const userStore = useUserStore();

  /** 模型实例ID */
  const bkObjId = 'system_events_test';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 业务系统列表 */
  const businessSystems = ref([]);

  /** 业务系统搜索loading */
  const systemLoading = ref(false);

  /** 截图文件列表 */
  const screenshotFileList = ref([]);

  /** 报告文件列表 */
  const reportFileList = ref([]);

  /** 图片预览相关 */
  const imagePreviewVisible = ref(false);
  const previewImageUrl = ref('');

  /** 表单数据 */
  const form = ref({
    event_name: '',
    recorder: '',
    event_desc: '',
    occurrence_time: '',
    event_source: '',
    event_level: '',
    impact_scope: '',
    affected_business: '',
    event_screenshot: '',
    fault_category: '', // 默认应用服务故障
    root_cause_fault_system: '',
    system_level: '',
    event_cause: '',
    dev_handler: '',
    ops_handler: '',
    event_handling_process: '',
    event_handling_results_and_basis: '',
    resolution_time: '',
    solution_and_whether_production_change_is_involved: '否',
    solution_and_whether_oa_process_is_involved: '否',
    change_order_no_causing_fault: '',
    change_requester: '',
    development_supplier: '',
    whether_review_is_conducted: '否',
    review_meeting_date: '',
    whether_event_report_is_formed: '否',
    event_report: '',
    remarks: ''
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    const baseRules = {
      event_name: [
        { required: true, message: '请输入事件标题', trigger: 'blur' }
      ],
      occurrence_time: [
        { required: true, message: '请选择发生时间', trigger: 'change' }
      ],
      event_source: [
        { required: true, message: '请输入事件来源', trigger: 'blur' }
      ],
      event_level: [
        { required: true, message: '请选择事件级别', trigger: 'change' }
      ],
      affected_business: [
        { required: true, message: '请输入影响业务', trigger: 'blur' }
      ],
      event_screenshot: [
        { required: true, message: '请上传事件现象截图', trigger: 'blur' }
      ],
      fault_category: [
        { required: true, message: '请选择故障类别', trigger: 'change' }
      ],
      root_cause_fault_system: [
        { required: true, message: '请选择根因故障系统', trigger: 'change' }
      ],
      event_cause: [
        { required: true, message: '请输入事件原因', trigger: 'blur' }
      ],
      ops_handler: [
        { required: true, message: '请输入运维处置人员', trigger: 'blur' }
      ]
    };

    // 动态添加规则
    if (form.value.fault_category === '上线/变更引发') {
      baseRules.change_order_no_causing_fault = [
        { required: true, message: '请输入引发故障的变更单号', trigger: 'blur' }
      ];
      baseRules.change_requester = [
        { required: true, message: '请输入变更申请人', trigger: 'blur' }
      ];
      baseRules.development_supplier = [
        { required: true, message: '请输入开发供应商', trigger: 'blur' }
      ];
    }

    if (form.value.whether_review_is_conducted === '是') {
      baseRules.review_meeting_date = [
        { required: true, message: '请选择复盘会召开日期', trigger: 'change' }
      ];
    }

    if (form.value.whether_event_report_is_formed === '是') {
      baseRules.event_report = [
        { required: true, message: '请上传事件报告', trigger: 'blur' }
      ];
    }

    return baseRules;
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        // 如果"是否形成事件报告"选择"否"，清空事件报告字段
        if (data.whether_event_report_is_formed === '否') {
          data.event_report = '';
        }

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: data.event_name + '_' + data.occurrence_time
            }
          });
          EleMessage.success('修改成功');
        } else {
          // 设置recorder为当前登录用户
          data.recorder = userStore.info.nickName || userStore.info.userName;
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: data.event_name + '_' + data.occurrence_time
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    screenshotFileList.value = [];
    reportFileList.value = [];
    businessSystems.value = [];
  };

  /** 发生时间变化处理 */
  const handleOccurrenceTimeChange = (_value) => {
    // 可以在这里添加发生月份的自动生成逻辑
  };

  /** 故障类别变化处理 */
  const handleFaultCategoryChange = (value) => {
    if (value !== '1') {
      // 清空变更相关字段
      form.value.change_order_no_causing_fault = '';
      form.value.change_requester = '';
      form.value.development_supplier = '';
    }
    // 重新验证表单
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  /** 根因故障系统变化处理 */
  const handleSystemChange = (value) => {
    const system = businessSystems.value.find((s) => s.value === value);
    if (system) {
      form.value.system_level = system.system_level || '';
    }
  };

  /** 加载根因故障系统数据 */
  const loadBusinessSystems = async () => {
    systemLoading.value = true;
    try {
      // 使用searchBusiness接口获取所有业务系统，不传keyword参数获取全部数据
      const res = await searchBusiness({});

      businessSystems.value = res.rows.map((item) => ({
        label: item.bk_inst_name,
        value: item.bk_biz_name, // 使用bk_biz_name作为值
        system_level: item.system_level || '' // 保存system_level用于自动填充
      }));
    } catch (e) {
      console.error('加载业务系统失败:', e);
    }
    systemLoading.value = false;
  };

  /** 复盘选择变化 */
  const handleReviewChange = (value) => {
    if (value !== '是') {
      form.value.review_meeting_date = '';
      form.value.whether_event_report_is_formed = '';
      form.value.event_report = '';
    }
    // 重新验证表单
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  /** 事件报告选择变化 */
  const handleEventReportChange = (value) => {
    if (value !== '是') {
      form.value.event_report = '';
      reportFileList.value = [];
    }
    // 重新验证表单
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };

  /** 处理粘贴事件 */
  const handlePaste = (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file) {
          uploadPastedFile(file);
        }
        break;
      }
    }
  };

  /** 处理拖拽上传 */
  const handleDrop = (event) => {
    const files = event.dataTransfer?.files;
    if (!files || files.length === 0) return;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file.type.indexOf('image') !== -1) {
        uploadPastedFile(file);
      }
    }
  };

  /** 上传粘贴/拖拽的文件 */
  const uploadPastedFile = async (file) => {
    // 检查文件数量限制
    if (screenshotFileList.value.length >= 10) {
      EleMessage.error('最多只能上传10张图片');
      return;
    }

    // 校验文件
    if (!beforeScreenshotUpload(file)) {
      return;
    }

    // 生成文件名
    const timestamp = Date.now();
    const extension = file.name.split('.').pop() || 'png';
    const fileName = `paste_${timestamp}.${extension}`;

    // 创建新的File对象，设置正确的文件名
    const newFile = new File([file], fileName, { type: file.type });

    // 添加到文件列表
    const fileItem = {
      name: fileName,
      raw: newFile,
      status: 'uploading',
      uid: Date.now() + Math.random()
    };

    screenshotFileList.value.push(fileItem);

    // 执行上传
    try {
      await handleScreenshotUpload({
        file: newFile,
        onSuccess: (response) => {
          const index = screenshotFileList.value.findIndex(
            (f) => f.uid === fileItem.uid
          );
          if (index !== -1) {
            screenshotFileList.value[index].status = 'success';
            screenshotFileList.value[index].url = response.data.url;
          }
        },
        onError: (error) => {
          const index = screenshotFileList.value.findIndex(
            (f) => f.uid === fileItem.uid
          );
          if (index !== -1) {
            screenshotFileList.value.splice(index, 1);
          }
          EleMessage.error('图片上传失败: ' + error.message);
        }
      });
    } catch (error) {
      const index = screenshotFileList.value.findIndex(
        (f) => f.uid === fileItem.uid
      );
      if (index !== -1) {
        screenshotFileList.value.splice(index, 1);
      }
      EleMessage.error('图片上传失败');
    }
  };

  /** 截图上传前校验 */
  const beforeScreenshotUpload = (file) => {
    const validTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
    const isValidType = validTypes.includes(file.type);
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isValidType) {
      EleMessage.error('只能上传 jpg/jpeg/png/gif/bmp/webp 格式的图片文件!');
      return false;
    }
    if (!isLt10M) {
      EleMessage.error('上传图片大小不能超过 10MB!');
      return false;
    }
    return true;
  };

  /** 自定义截图上传 */
  const handleScreenshotUpload = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', bkObjId);
      const response = await uploadAttachment(formData);

      if (response.code === 200) {
        onSuccess(response, file);

        // 更新文件列表中当前文件的url
        const fileIndex = screenshotFileList.value.findIndex(
          (f) => f.uid === file.uid
        );
        if (fileIndex !== -1) {
          screenshotFileList.value[fileIndex].url = response.data.url;
        }

        // 更新event_screenshot字段
        const urls = screenshotFileList.value.map((f) => f.url).filter(Boolean);
        form.value.event_screenshot = urls.join(',');

        EleMessage.success('截图上传成功');
      } else {
        onError(new Error(response.msg || '上传失败'));
      }
    } catch (error) {
      onError(error);
      EleMessage.error('截图上传失败: ' + error.message);
    }
  };

  /** 截图删除 */
  const handleScreenshotRemove = (_file, fileList) => {
    screenshotFileList.value = fileList;
    const urls = fileList.map((f) => f.url).filter(Boolean);
    form.value.event_screenshot = urls.join(',');
  };

  /** 截图预览 */
  const handleScreenshotPreview = (file) => {
    previewImageUrl.value = file.url || file.response?.data?.url || '';
    imagePreviewVisible.value = true;
  };

  /** 报告上传前校验 */
  const beforeReportUpload = (file) => {
    const validTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    const validExtensions = ['.pdf', '.doc', '.docx'];

    const isValidType =
      validTypes.includes(file.type) ||
      validExtensions.some((ext) => file.name.toLowerCase().endsWith(ext));
    const isLt50M = file.size / 1024 / 1024 < 50;

    if (!isValidType) {
      EleMessage.error('只能上传 pdf/doc/docx 格式文件!');
      return false;
    }
    if (!isLt50M) {
      EleMessage.error('上传文件大小不能超过 50MB!');
      return false;
    }
    return true;
  };

  /** 自定义报告上传 */
  const handleReportUpload = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', bkObjId);
      const response = await uploadAttachment(formData);

      if (response.code === 200) {
        onSuccess(response, file);

        // 更新文件列表中当前文件的url
        const fileIndex = reportFileList.value.findIndex(
          (f) => f.uid === file.uid
        );
        if (fileIndex !== -1) {
          reportFileList.value[fileIndex].url = response.data.url;
        }

        // 更新event_report字段
        form.value.event_report = response.data.url;

        EleMessage.success('事件报告上传成功');
      } else {
        onError(new Error(response.msg || '上传失败'));
      }
    } catch (error) {
      onError(error);
      EleMessage.error('事件报告上传失败: ' + error.message);
    }
  };

  /** 报告删除 */
  const handleReportRemove = (_file, fileList) => {
    reportFileList.value = fileList;
    form.value.event_report = '';
  };

  /** 报告预览 */
  const handleReportPreview = (file) => {
    const url = file.url || file.response?.data?.url || '';
    const fileName = file.name || '';

    if (url) {
      const lowerFileName = fileName.toLowerCase();

      if (lowerFileName.endsWith('.pdf')) {
        // PDF文档：构造内联查看URL
        openDocumentInline(url, 'pdf');
      } else if (
        lowerFileName.endsWith('.doc') ||
        lowerFileName.endsWith('.docx')
      ) {
        // Word文档：提供多种预览方式
        openWordDocument(url, fileName);
      } else {
        // 其他文档类型：尝试内联打开
        openDocumentInline(url, 'other');
      }
    }
  };

  /** 在浏览器中内联打开文档 */
  const openDocumentInline = (url, type) => {
    // 方法1: 尝试添加查询参数强制内联显示
    const inlineUrl =
      url + (url.includes('?') ? '&' : '?') + 'inline=true&view=true';

    // 方法2: 如果有预览端点，使用预览服务
    const previewUrl = url
      .replace('/download/', '/preview/')
      .replace('/attachment/', '/inline/');

    // 优先尝试预览URL，如果不行则使用内联URL
    const finalUrl = previewUrl !== url ? previewUrl : inlineUrl;

    // 在新窗口中打开
    const newWindow = window.open('about:blank', '_blank');
    if (newWindow) {
      if (type === 'pdf') {
        // PDF可以直接嵌入
        newWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>文档预览</title>
              <style>
                body { margin: 0; padding: 0; height: 100vh; }
                iframe { width: 100%; height: 100%; border: none; }
                .loading { text-align: center; padding: 50px; font-family: Arial, sans-serif; }
                .error { color: red; }
              </style>
            </head>
            <body>
              <div class="loading">正在加载文档...</div>
              <iframe src="${finalUrl}" onload="document.querySelector('.loading').style.display='none'"
                      onerror="document.querySelector('.loading').innerHTML='<div class=error>文档加载失败，<a href=${url} target=_blank>点击下载</a></div>'">
              </iframe>
            </body>
          </html>
        `);
      } else {
        // 其他类型文档尝试直接导航
        newWindow.location.href = finalUrl;
      }
    }
  };

  /** 打开Word文档 */
  const openWordDocument = (url, fileName) => {
    ElMessageBox.confirm('请选择查看方式：', '文档查看', {
      distinguishCancelAndClose: true,
      confirmButtonText: '在线预览',
      cancelButtonText: '浏览器打开',
      type: 'info'
    })
      .then(() => {
        // 在线预览
        showOnlinePreview(url);
      })
      .catch((action) => {
        if (action === 'cancel') {
          // 浏览器直接打开
          openDocumentInline(url, 'word');
        }
      });
  };

  /** 显示在线预览选项 */
  const showOnlinePreview = (url) => {
    const options = [
      {
        label: 'Google Docs 预览',
        icon: '🌐',
        action: () => {
          const viewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(url)}&embedded=true`;
          window.open(viewerUrl, '_blank');
        }
      },
      {
        label: 'Microsoft Office 预览',
        icon: '📄',
        action: () => {
          const officeUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`;
          window.open(officeUrl, '_blank');
        }
      },
      {
        label: '本地预览服务',
        icon: '🔍',
        action: () => {
          const localUrl = `${window.location.origin}/api/preview/document?url=${encodeURIComponent(url)}`;
          window.open(localUrl, '_blank');
        }
      }
    ];

    ElMessageBox({
      title: '选择预览方式',
      message: h('div', { style: 'padding: 20px 0;' }, [
        h(
          'p',
          {
            style: 'margin-bottom: 15px; color: #606266; font-size: 14px;'
          },
          '请选择一种在线预览方式：'
        ),
        ...options.map((option, index) =>
          h(
            'div',
            {
              key: index,
              style:
                'margin: 10px 0; cursor: pointer; padding: 12px; border: 1px solid #dcdfe6; border-radius: 6px; transition: all 0.3s; display: flex; align-items: center; gap: 10px;',
              onMouseover: (e) => {
                e.target.style.backgroundColor = '#f5f7fa';
                e.target.style.borderColor = '#409eff';
              },
              onMouseout: (e) => {
                e.target.style.backgroundColor = 'white';
                e.target.style.borderColor = '#dcdfe6';
              },
              onClick: () => {
                ElMessageBox.close();
                option.action();
              }
            },
            [
              h('span', { style: 'font-size: 20px;' }, option.icon),
              h('span', { style: 'font-size: 14px;' }, option.label)
            ]
          )
        )
      ]),
      showCancelButton: true,
      showConfirmButton: false,
      cancelButtonText: '取消'
    });
  };

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
        // 处理文件列表回显
        if (value.event_screenshot) {
          screenshotFileList.value = value.event_screenshot
            .split(',')
            .map((url, index) => ({
              name: `screenshot_${index + 1}`,
              url: url
            }));
        }
        if (value.event_report) {
          reportFileList.value = [
            {
              name: value.event_report_name || 'event_report.pdf',
              url: value.event_report
            }
          ];
        }
      }
    },
    { immediate: true }
  );

  onMounted(() => {
    // 初始化业务系统列表
    loadBusinessSystems();
  });
</script>

<style scoped>
  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }

  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .report-preview-section {
    margin-top: 12px;
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .report-preview-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }

  .report-preview-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
  }

  .report-icon {
    color: #409eff;
    font-size: 16px;
  }

  .report-name {
    flex: 1;
    font-size: 14px;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  .screenshot-upload-container {
    position: relative;
    border: 2px dashed transparent;
    border-radius: 6px;
    transition: all 0.3s ease;
    outline: none;
  }

  .screenshot-upload-container:focus {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .screenshot-upload-container:hover {
    border-color: var(--el-color-primary-light-3);
  }

  .paste-tip {
    color: var(--el-color-primary);
    font-weight: 500;
  }
</style>
