<template>
  <ele-drawer
    v-model="visible"
    :size="1000"
    title="项目详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="project-detail" v-if="data">
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '120px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="项目名称" :span="2">
          {{ data?.project_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目预算">
          {{ formatBudget(data?.ppb) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目负责人">
          {{ data?.project_maintainer || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目预算内存">
          {{ formatBudget(data?.project_budget_memory) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目预算存储">
          {{ formatBudget(data?.project_budget_disk) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目预算CPU">
          {{ formatBudget(data?.project_budget_cpu) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目预算NAS">
          {{ formatBudget(data?.project_budget_nas) }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ data?.projcet_mark || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'ProjectDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 格式化预算数据 */
  const formatBudget = (value) => {
    if (value === null || value === undefined || value === '') {
      return '-';
    }
    return `${Number(value).toLocaleString()}`;
  };

  /** 关闭抽屉 */
  const handleClose = () => {
    visible.value = false;
  };
</script>

<style scoped>
  .project-detail {
    padding: 0 8px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0;
  }
</style>
