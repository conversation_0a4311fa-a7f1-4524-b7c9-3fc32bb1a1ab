<template>
  <el-drawer
    :model-value="visible"
    title="演练记录详情"
    :size="600"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
  >
    <div class="detail-content">
      <el-descriptions
        :column="1"
        border
        label-class-name="detail-label"
        content-class-name="detail-content"
      >
        <el-descriptions-item label="系统名称">
          {{ data?.system_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="演练日期">
          {{ data?.drill_date || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="所属周期">
          {{ data?.affiliated_cycle || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="演练场景">
          {{ data?.drill_scenario || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="负责人">
          {{ data?.pic || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="结果">
          <el-tag :type="getResultTagType(data?.result)" effect="plain">
            {{ data?.result || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="下次演练">
          {{ data?.next_drill || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          <div class="remark-content">
            {{ data?.remark || '-' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'DrillRecordDetail' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 详情数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 获取结果标签类型 */
  const getResultTagType = (result) => {
    switch (result) {
      case '成功':
        return 'success';
      case '部分成功':
        return 'warning';
      case '失败':
        return 'danger';
      default:
        return 'info';
    }
  };
</script>

<style scoped>
  .detail-content {
    padding: 20px;
  }

  :deep(.detail-label) {
    width: 120px;
    background-color: var(--el-fill-color-light);
    font-weight: 500;
  }

  :deep(.detail-content) {
    background-color: var(--el-bg-color);
  }

  .remark-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
  }
</style>
