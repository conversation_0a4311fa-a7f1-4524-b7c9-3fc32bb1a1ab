<!-- 主机批量编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑主机"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="device_role">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.device_role" />
                    <span class="label-text">主机角色</span>
                  </div>
                </template>
                <el-select
                  v-model="form.device_role"
                  placeholder="请选择主机角色"
                  style="width: 100%"
                  :disabled="!fieldEnabled.device_role"
                >
                  <el-option label="应用" value="0" />
                  <el-option label="数据库" value="1" />
                  <el-option label="中间件" value="2" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="environment_code">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.environment_code" />
                    <span class="label-text">设备环境</span>
                  </div>
                </template>
                <el-select
                  v-model="form.environment_code"
                  placeholder="请选择设备环境"
                  style="width: 100%"
                  :disabled="!fieldEnabled.environment_code"
                >
                  <el-option label="生产" value="0" />
                  <el-option label="测试" value="1" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="lifecycle_code">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.lifecycle_code" />
                    <span class="label-text">使用状态</span>
                  </div>
                </template>
                <el-select
                  v-model="form.lifecycle_code"
                  placeholder="请选择使用状态"
                  style="width: 100%"
                  :disabled="!fieldEnabled.lifecycle_code"
                >
                  <el-option label="已下线" value="0" />
                  <el-option label="在线" value="1" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="monitor_status">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.monitor_status" />
                    <span class="label-text">监控状态</span>
                  </div>
                </template>
                <el-select
                  v-model="form.monitor_status"
                  placeholder="请选择监控状态"
                  style="width: 100%"
                  :disabled="!fieldEnabled.monitor_status"
                >
                  <el-option label="正常" value="0" />
                  <el-option label="异常" value="1" />
                  <el-option label="未知" value="2" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="system_name">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.system_name" />
                    <span class="label-text">系统名称</span>
                  </div>
                </template>
                <el-select
                  v-model="form.system_name"
                  placeholder="请选择系统名称"
                  style="width: 100%"
                  filterable
                  :loading="businessLoading"
                  :disabled="!fieldEnabled.system_name"
                  @change="handleSystemNameChange"
                >
                  <el-option
                    v-for="item in businessOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="project_name">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.project_name" />
                    <span class="label-text">项目名称</span>
                  </div>
                </template>
                <el-input
                  v-model="form.project_name"
                  placeholder="根据系统名称自动带出"
                  readonly
                  style="background-color: #f5f7fa"
                  :disabled="!fieldEnabled.project_name"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="technician_owner">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.technician_owner" />
                    <span class="label-text">技术负责人</span>
                  </div>
                </template>
                <el-input
                  v-model="form.technician_owner"
                  placeholder="请输入技术负责人"
                  clearable
                  :maxlength="50"
                  :disabled="!fieldEnabled.technician_owner"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="technician_owner_id">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.technician_owner_id" />
                    <span class="label-text">技术负责人ID</span>
                  </div>
                </template>
                <el-input
                  v-model="form.technician_owner_id"
                  placeholder="请输入技术负责人ID"
                  clearable
                  :maxlength="20"
                  :disabled="!fieldEnabled.technician_owner_id"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <div class="field-with-checkbox">
          <el-form-item prop="os_version">
            <template #label>
              <div class="form-label-with-checkbox">
                <el-checkbox v-model="fieldEnabled.os_version" />
                <span class="label-text">操作系统版本</span>
              </div>
            </template>
            <el-input
              v-model="form.os_version"
              type="textarea"
              placeholder="请输入操作系统版本"
              :rows="3"
              :maxlength="500"
              :disabled="!fieldEnabled.os_version"
            />
          </el-form-item>
        </div>

        <div class="field-with-checkbox">
          <el-form-item prop="remark">
            <template #label>
              <div class="form-label-with-checkbox">
                <el-checkbox v-model="fieldEnabled.remark" />
                <span class="label-text">备注信息</span>
              </div>
            </template>
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入备注信息"
              :rows="3"
              :maxlength="500"
              :disabled="!fieldEnabled.remark"
            />
          </el-form-item>
        </div>

        <!-- 硬件配置 -->
        <div class="form-section-title">硬件配置</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="disk">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.disk" />
                    <span class="label-text">数据盘大小</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.disk"
                  placeholder="请输入数据盘大小(GB)"
                  clearable
                  :disabled="!fieldEnabled.disk"
                >
                  <template #append>GB</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="total_disks">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.total_disks" />
                    <span class="label-text">磁盘总大小</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.total_disks"
                  placeholder="请输入磁盘总大小(GB)"
                  clearable
                  :disabled="!fieldEnabled.total_disks"
                >
                  <template #append>GB</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="memory">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.memory" />
                    <span class="label-text">内存容量</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.memory"
                  placeholder="请输入内存容量(GB)"
                  clearable
                  :disabled="!fieldEnabled.memory"
                >
                  <template #append>GB</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="nas">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.nas" />
                    <span class="label-text">NAS存储</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.nas"
                  placeholder="请输入NAS存储(GB)"
                  clearable
                  :disabled="!fieldEnabled.nas"
                >
                  <template #append>GB</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="cpu">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.cpu" />
                    <span class="label-text">CPU逻辑核心数</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.cpu"
                  placeholder="请输入CPU逻辑核心数"
                  clearable
                  :disabled="!fieldEnabled.cpu"
                >
                  <template #append>核</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="system_disk">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.system_disk" />
                    <span class="label-text">系统盘</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.system_disk"
                  placeholder="请输入系统盘大小(GB)"
                  clearable
                  :disabled="!fieldEnabled.system_disk"
                >
                  <template #append>GB</template>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <!-- 申请信息 -->
        <div class="form-section-title">申请信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="apply_department">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.apply_department" />
                    <span class="label-text">申请部门</span>
                  </div>
                </template>
                <el-input
                  v-model="form.apply_department"
                  placeholder="请输入申请部门"
                  clearable
                  :maxlength="100"
                  :disabled="!fieldEnabled.apply_department"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="apply_date">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.apply_date" />
                    <span class="label-text">申请日期</span>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.apply_date"
                  type="date"
                  placeholder="选择申请日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled="!fieldEnabled.apply_date"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="apply_username">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.apply_username" />
                    <span class="label-text">申请人</span>
                  </div>
                </template>
                <el-input
                  v-model="form.apply_username"
                  placeholder="请输入申请人"
                  clearable
                  :maxlength="50"
                  :disabled="!fieldEnabled.apply_username"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="apply_user_id">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.apply_user_id" />
                    <span class="label-text">申请人工号</span>
                  </div>
                </template>
                <el-input
                  v-model="form.apply_user_id"
                  placeholder="请输入申请人工号"
                  clearable
                  :maxlength="20"
                  :disabled="!fieldEnabled.apply_user_id"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div class="selected-records-info">
        <el-alert
          :title="`已选择 ${selectedRecords.length} 个主机进行批量编辑`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="updateVisible(false)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst } from '@/api/cmdb';
  import { searchBusiness } from '@/api/cmdb';

  const emit = defineEmits(['update:model-value', 'done']);

  const props = defineProps({
    modelValue: Boolean,
    selectedRecords: {
      type: Array,
      default: () => []
    }
  });

  const bkObjId = 'host_resource';
  const formRef = ref(null);
  const loading = ref(false);
  const businessLoading = ref(false);
  const businessOptions = ref([]);

  const visible = computed(() => props.modelValue);

  // 字段启用状态
  const fieldEnabled = reactive({
    device_role: false,
    system_name: false,
    project_name: false,
    apply_department: false,
    apply_date: false,
    apply_username: false,
    apply_user_id: false,
    monitor_status: false,
    disk: false,
    total_disks: false,
    memory: false,
    nas: false,
    cpu: false,
    system_disk: false,
    environment_code: false,
    lifecycle_code: false,
    technician_owner: false,
    technician_owner_id: false,
    os_version: false,
    remark: false
  });

  // 表单数据
  const form = reactive({
    device_role: null,
    system_name: '',
    project_name: '',
    apply_department: '',
    apply_date: '',
    apply_username: '',
    apply_user_id: '',
    monitor_status: null,
    disk: null,
    total_disks: null,
    memory: null,
    nas: null,
    cpu: null,
    system_disk: null,
    environment_code: null,
    lifecycle_code: null,
    technician_owner: '',
    technician_owner_id: '',
    os_version: '',
    remark: ''
  });

  // 表单验证规则
  const rules = reactive({
    device_role: [
      { required: false, message: '请选择主机角色', trigger: 'change' }
    ],
    system_name: [
      { required: false, message: '请选择系统名称', trigger: 'change' }
    ]
  });

  // 更新弹窗状态
  const updateVisible = (value) => {
    emit('update:model-value', value);
  };

  // 加载业务系统列表
  const loadBusinessSystems = async () => {
    try {
      businessLoading.value = true;
      const result = await searchBusiness({});
      if (result && result.rows) {
        businessOptions.value = result.rows.map((item) => ({
          label: item.bk_inst_name,
          value: item.bk_biz_name,
          business_collection: item.business_collection || ''
        }));
      }
    } catch (error) {
      console.error('加载业务系统失败:', error);
      businessOptions.value = [];
    } finally {
      businessLoading.value = false;
    }
  };

  // 系统名称变化时自动带出项目名称
  const handleSystemNameChange = (value) => {
    const selectedBusiness = businessOptions.value.find(
      (item) => item.value === value
    );
    if (selectedBusiness && selectedBusiness.business_collection) {
      form.project_name = selectedBusiness.business_collection;
      fieldEnabled.project_name = true;
    }
  };

  // 弹窗关闭回调
  const handleClosed = () => {
    resetForm();
  };

  // 重置表单
  const resetForm = () => {
    Object.keys(form).forEach((key) => {
      if (typeof form[key] === 'number') {
        form[key] = null;
      } else {
        form[key] = '';
      }
    });
    Object.keys(fieldEnabled).forEach((key) => {
      fieldEnabled[key] = false;
    });
    businessOptions.value = [];
    formRef.value?.clearValidate();
  };

  // 提交表单
  const submit = async () => {
    // 检查是否至少启用一个字段
    const hasEnabledField = Object.values(fieldEnabled).some(
      (enabled) => enabled
    );
    if (!hasEnabledField) {
      EleMessage.warning('请至少启用一个字段进行编辑');
      return;
    }

    // 构造要更新的数据，只包含启用的字段
    const updateData = {};
    Object.keys(fieldEnabled).forEach((key) => {
      if (fieldEnabled[key]) {
        let value = form[key];
        // 对数字字段进行类型转换
        if (
          ['apply_user_id', 'technician_owner_id'].includes(key) &&
          value !== null &&
          value !== ''
        ) {
          value = Number(value);
        }
        updateData[key] = value;
      }
    });

    loading.value = true;
    try {
      const bkInstId = props.selectedRecords.map((record) => record.bk_inst_id);
      await batchUpdateInst({
        bkObjId,
        bkInstId,
        instInfoMap: {
          ...updateData
        }
      });
      EleMessage.success(`成功批量编辑 ${props.selectedRecords.length} 条记录`);
      updateVisible(false);
      emit('done');
    } catch (error) {
      EleMessage.error(error.message || '批量编辑失败');
    } finally {
      loading.value = false;
    }
  };

  // 监听抽屉显示状态，加载业务系统列表
  watch(visible, async (newVisible) => {
    if (newVisible) {
      await loadBusinessSystems();
    }
  });
</script>

<style lang="scss" scoped>
  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }

  .field-with-checkbox {
    .form-label-with-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;

      .label-text {
        font-weight: 500;
        color: #606266;
      }
    }
  }

  .selected-records-info {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }

  .dialog-footer {
    text-align: right;
  }

  .el-input[readonly] {
    background-color: #f5f7fa;
  }
</style>
