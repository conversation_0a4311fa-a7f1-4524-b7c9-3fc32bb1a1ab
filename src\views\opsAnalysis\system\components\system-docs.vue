<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <el-icon class="text-primary mr-2"><Document /></el-icon>
          <span class="text-lg font-semibold">系统文档</span>
        </div>
        <el-link type="primary" :underline="false">全部文档</el-link>
      </div>
    </template>
    <div class="divide-y divide-gray-100">
      <div
        v-for="doc in documents"
        :key="doc.id"
        class="p-4 hover:bg-gray-50 transition-all-300 flex items-center justify-between"
      >
        <div class="flex items-center">
          <div
            class="w-10 h-10 rounded flex items-center justify-center mr-3"
            :class="doc.iconBg"
          >
            <el-icon :class="doc.iconColor">
              <component :is="doc.icon" />
            </el-icon>
          </div>
          <div>
            <h3 class="font-medium">{{ doc.name }}</h3>
            <p class="text-xs text-gray-500">{{ doc.size }} · {{ doc.date }}</p>
          </div>
        </div>
        <el-button size="small" text type="info" @click="downloadDoc(doc)">
          <el-icon><BellFilled /></el-icon>
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
  import {
    Document,
    BellFilled,
    Check,
    Setting
  } from '@element-plus/icons-vue';

  const documents = [
    {
      id: 1,
      name: '系统需求规格说明书',
      size: '2.4MB',
      date: '2023-01-15',
      icon: BellFilled,
      iconBg: 'bg-red-100',
      iconColor: 'text-red-500'
    },
    {
      id: 2,
      name: '数据字典表',
      size: '1.2MB',
      date: '2023-02-20',
      icon: Check,
      iconBg: 'bg-green-100',
      iconColor: 'text-green-500'
    },
    {
      id: 3,
      name: '用户操作手册',
      size: '3.7MB',
      date: '2023-03-10',
      icon: Document,
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-500'
    },
    {
      id: 4,
      name: 'API接口文档',
      size: '1.8MB',
      date: '2023-04-05',
      icon: Setting,
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-500'
    }
  ];

  const downloadDoc = (doc) => {
    console.log('下载文档:', doc.name);
  };
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-primary {
    color: #165dff;
  }
</style>
