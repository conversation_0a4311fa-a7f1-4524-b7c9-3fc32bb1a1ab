<template>
  <el-dialog
    :model-value="visible"
    title="拒绝工单并转派"
    width="600px"
    :destroy-on-close="true"
    @update:model-value="updateVisible"
  >
    <div class="reject-content">
      <div class="warning-info">
        <el-icon class="warning-icon"><WarningFilled /></el-icon>
        <div>
          <p>确认要拒绝以下工单吗？</p>
          <p class="sub-text"
            >拒绝后工单将自动转派给流程中的最后一个处理人，并在工单内容中添加拒绝原因</p
          >
        </div>
      </div>

      <div class="ticket-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="工单ID">
            {{ ticketData?.ticket_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单类型">
            系统事件
          </el-descriptions-item>
          <el-descriptions-item label="工单等级">
            <el-tag
              v-if="ticketData?.level"
              :type="getLevelType(ticketData.level)"
              size="small"
            >
              {{ ticketData.level }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="工单内容">
            {{ ticketData?.comment || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="将转派给">
            {{ getLastAssignUser() }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent=""
      >
        <el-form-item label="拒绝原因" prop="rejectReason">
          <el-input
            v-model="form.rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="danger" :loading="loading" @click="handleConfirm">
          确认拒绝
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { WarningFilled } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';
  import { transferTicket } from '@/api/ticket';

  defineOptions({ name: 'TicketReject' });

  const props = defineProps({
    modelValue: Boolean,
    ticketData: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const visible = computed(() => props.modelValue);

  const formRef = ref(null);
  const loading = ref(false);

  const form = ref({
    rejectReason: ''
  });

  const rules = {
    rejectReason: [
      { required: true, message: '请输入拒绝原因', trigger: 'blur' }
    ]
  };

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleConfirm = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        // 获取转派历史中的最后一个用户
        const transferHistory = props.ticketData?.transferHistory || [];
        if (transferHistory.length === 0) {
          EleMessage.error('无法获取工单转派历史，无法执行拒绝操作');
          loading.value = false;
          return;
        }

        // 获取最后一个转派记录
        const lastTransfer = transferHistory[transferHistory.length - 1];
        const lastUserId = lastTransfer.assign_user_id;
        const lastUserName = lastTransfer.assign_user_name;

        if (!lastUserId || !lastUserName) {
          EleMessage.error('无法获取最后一个处理人信息');
          loading.value = false;
          return;
        }

        // 组合新的comment：原始内容 + 拒绝原因
        const originalComment = props.ticketData?.comment || '';
        const newComment = `${originalComment} - 拒绝原因：${form.value.rejectReason}`;

        // 转派给最后一个用户
        await transferTicket({
          follow_field: ['bk_inst_name'], // 默认关联字段
          executor_user_name: lastUserName,
          executor_user_id: lastUserId,
          comment: newComment,
          ticket_id: props.ticketData?.ticket_id,
          system_item: props.ticketData?.system_item || null
        });

        EleMessage.success('工单已拒绝并转派给原处理人');
        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '拒绝工单失败');
      }
      loading.value = false;
    });
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const getLevelType = (level) => {
    const levelTypes = {
      p1: 'danger',
      p2: 'warning',
      p3: 'primary',
      p4: 'info'
    };
    return levelTypes[level] || 'info';
  };

  // 获取最后一个分配用户
  const getLastAssignUser = () => {
    const transferHistory = props.ticketData?.transferHistory || [];
    if (transferHistory.length === 0) {
      return '无法获取转派目标';
    }
    const lastTransfer = transferHistory[transferHistory.length - 1];
    return lastTransfer.assign_user_name || '未知用户';
  };

  watch(
    () => props.modelValue,
    (value) => {
      if (!value) {
        form.value.rejectReason = '';
        formRef.value?.resetFields();
      }
    }
  );
</script>

<style scoped>
  .reject-content {
    max-height: 60vh;
    overflow-y: auto;
  }

  .warning-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px;
    background-color: #fef0f0;
    border: 1px solid #fde2e2;
    border-radius: 4px;
  }

  .warning-icon {
    color: #f56c6c;
    font-size: 20px;
    margin-right: 8px;
  }

  .warning-info p {
    margin: 0;
    color: #f56c6c;
    font-weight: 500;
  }

  .warning-info .sub-text {
    margin-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #909399;
  }

  .ticket-info {
    margin-bottom: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
</style>
