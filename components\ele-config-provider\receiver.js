import { inject, computed } from 'vue';
import { STR_KEY as VAL_KEY } from '../utils/common';
import { CONFIG_KEY } from './props';
import defaultLocale from '../lang/zh_CN';
export { VAL_KEY };

/**
 * 获取全局配置
 */
export function useReceiver() {
  return inject(CONFIG_KEY, {});
}

/**
 * 获取全局属性
 */
export function useGlobalProps(name) {
  const globalConfig = useReceiver();
  return computed(() => globalConfig[name] ?? {});
}

/**
 * 获取国际化
 */
export function useLocale(name, props) {
  const globalConfig = useReceiver();
  const lang = computed(() => {
    const temp = globalConfig.locale ?? defaultLocale;
    if (name) {
      try {
        return Object.assign({}, temp[name] ?? {}, props?.locale);
      } catch (e) {
        console.error(e, VAL_KEY);
      }
    }
    return temp;
  });
  return { lang, globalConfig };
}
