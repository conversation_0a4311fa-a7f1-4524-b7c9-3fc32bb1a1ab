<template>
  <el-drawer
    :model-value="visible"
    title="审核工单"
    :size="700"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <div class="ticket-info">
        <h4>工单信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单ID">
            {{ ticketData?.ticket_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单类型">
            {{ ticketData?.ticket_type || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag
              v-if="ticketData?.level"
              :type="getLevelType(ticketData.level)"
              size="small"
            >
              {{ ticketData.level }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ ticketData?.create_user_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(ticketData?.ticket_start_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag
              v-if="ticketData?.ticket_status !== undefined"
              :type="getStatusType(ticketData.ticket_status)"
              size="small"
            >
              {{ getStatusText(ticketData.ticket_status) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" span="2">
            {{ ticketData?.comment || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
        @submit.prevent=""
      >
        <el-form-item label="审核结果" prop="approval">
          <el-radio-group v-model="form.approval">
            <el-radio :value="true">通过</el-radio>
            <el-radio :value="false">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 通过时显示的字段 -->
        <template v-if="form.approval === true">
          <el-form-item label="" prop="">
            <div class="form-tip">
              <el-icon><InfoFilled /></el-icon>
              <span>审核通过后，相关数据录入到系统事件表中</span>
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        确认
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { InfoFilled } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';
  import { closeTicket } from '@/api/ticket';

  defineOptions({ name: 'TicketApprove' });

  const props = defineProps({
    modelValue: Boolean,
    ticketData: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const visible = computed(() => props.modelValue);

  const formRef = ref(null);
  const loading = ref(false);

  const form = ref({
    approval: null
  });

  const rules = computed(() => ({
    approval: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
  }));

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleClosed = () => {
    resetFields();
  };

  const resetFields = () => {
    formRef.value?.resetFields();
    form.value = {
      approval: null
    };
  };

  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        // 提交审核结果
        await closeTicket({
          ticketId: props.ticketData.ticket_id,
          approval: form.value.approval
        });

        EleMessage.success('审核完成');
        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '审核失败');
      }
      loading.value = false;
    });
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const getLevelType = (level) => {
    const levelTypes = {
      p1: 'danger',
      p2: 'warning',
      p3: 'primary',
      p4: 'info'
    };
    return levelTypes[level] || 'info';
  };

  const getStatusType = (status) => {
    const statusTypes = {
      0: 'warning',
      1: 'success',
      2: 'danger'
    };
    return statusTypes[status] || 'info';
  };

  const getStatusText = (status) => {
    const statusTexts = {
      0: '待处理',
      1: '已完成',
      2: '已关闭'
    };
    return statusTexts[status] || '未知';
  };

  const formatTime = (time) => {
    if (!time) return '-';
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .ticket-info {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .ticket-info h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .form-tip {
    display: flex;
    align-items: center;
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
  }

  .form-tip .el-icon {
    margin-right: 4px;
  }
</style>
