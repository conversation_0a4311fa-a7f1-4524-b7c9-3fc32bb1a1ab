<template>
  <ele-page>
    <div class="coming-soon-container">
      <div class="coming-soon-card">
        <div class="icon">
          <el-icon size="80" color="#409EFF">
            <Clock />
          </el-icon>
        </div>
        <h2>功能即将上线</h2>
        <p>该页面功能将于 <strong>2025年10月30日</strong> 正式上线</p>
        <p class="sub-text">敬请期待</p>
      </div>
    </div>
  </ele-page>
</template>

<script setup>
  import { Clock } from '@element-plus/icons-vue';
</script>

<style scoped>
  .coming-soon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    padding: 40px 20px;
  }

  .coming-soon-card {
    text-align: center;
    padding: 60px 40px;
    border-radius: 12px;
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    max-width: 500px;
  }

  .icon {
    margin-bottom: 30px;
  }

  h2 {
    color: var(--el-text-color-primary);
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 20px 0;
  }

  p {
    color: var(--el-text-color-regular);
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 10px 0;
  }

  .sub-text {
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-top: 20px;
  }

  strong {
    color: var(--el-color-primary);
    font-weight: 600;
  }
</style>
