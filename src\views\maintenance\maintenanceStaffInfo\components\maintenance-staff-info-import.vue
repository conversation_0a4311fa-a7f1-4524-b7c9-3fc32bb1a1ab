<template>
  <ele-modal
    :width="460"
    title="批量导入"
    :body-style="{ paddingTop: '8px' }"
    v-model="visible"
  >
    <div v-loading="importLoading" class="maintenance-staff-import-upload">
      <el-upload
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        accept=".xlsx,.xls"
        :show-upload-list="false"
        :before-upload="beforeUpload"
      >
        <ele-text
          type="primary"
          :icon="CloudUploadOutlined"
          :icon-props="{ size: 52 }"
          style="margin-bottom: 10px"
        />
        <ele-text type="placeholder">将文件拖到此处, 或点击上传</ele-text>
      </el-upload>
    </div>
    <div style="display: flex; align-items: center">
      <ele-text size="sm" type="secondary" style="line-height: 17px; flex: 1">
        <span style="padding-right: 8px">只能上传 xls、xlsx 文件，</span>
        <el-link
          type="primary"
          underline="never"
          style="font-size: inherit; line-height: inherit; vertical-align: 0"
          @click="downloadTemplate"
        >
          下载模板
        </el-link>
      </ele-text>
    </div>
  </ele-modal>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { CloudUploadOutlined } from '@/components/icons';
  import { importTemplate, importExcel } from '@/api/cmdb/index';

  defineOptions({ name: 'MaintenanceStaffInfoImport' });

  const props = defineProps({
    modelValue: Boolean
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'maintenance_staff_info';

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 上传状态 */
  const importLoading = ref(false);

  /** 文件变化 */
  const handleFileChange = (file) => {
    if (!file.raw) return;

    ElMessageBox.confirm('确定导入该文件吗？', '系统提示', {
      type: 'warning'
    })
      .then(async () => {
        importLoading.value = true;
        try {
          const formData = new FormData();
          formData.append('file', file.raw);
          formData.append('bkObjId', bkObjId);

          const res = await importExcel(formData);
          if (res.code === 200) {
            EleMessage.success('导入成功');
            visible.value = false;
            emit('done');
          } else {
            EleMessage.error(res.msg || '导入失败');
          }
        } catch (e) {
          EleMessage.error(e.message || '导入失败');
        }
        importLoading.value = false;
      })
      .catch(() => {});
  };

  /** 上传前校验 */
  const beforeUpload = (file) => {
    const isExcel =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel';
    const isLt5M = file.size / 1024 / 1024 < 5;

    if (!isExcel) {
      EleMessage.error('只能上传 Excel 文件!');
      return false;
    }
    if (!isLt5M) {
      EleMessage.error('上传文件大小不能超过 5MB!');
      return false;
    }
    return true;
  };

  /** 下载模板 */
  const downloadTemplate = async () => {
    try {
      const loading = EleMessage.loading('正在下载模板...');
      const res = await importTemplate({
        bkObjId
      });

      if (res) {
        // 创建下载链接
        const blob = new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '维保人员管理导入模板.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);

        EleMessage.success('模板下载成功');
      }
      loading.close();
    } catch (e) {
      EleMessage.error(e.message || '模板下载失败');
    }
  };
</script>

<style scoped>
  .maintenance-staff-import-upload {
    margin: 10px 0 20px;
  }

  :deep(.el-upload-dragger) {
    padding: 30px;
  }
</style>