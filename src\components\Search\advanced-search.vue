<template>
  <ele-modal
    v-model="visible"
    :width="800"
    title="高级搜索"
    :destroy-on-close="true"
    custom-class="advanced-search-modal"
  >
    <div class="advanced-search-content">
      <div class="search-conditions">
        <div
          v-for="(condition, index) in conditions"
          :key="index"
          class="condition-row"
        >
          <div class="condition-number">
            <ele-text type="primary" size="sm" class="number-badge">
              {{ index + 1 }}
            </ele-text>
          </div>

          <div class="condition-form">
            <el-form-item>
              <el-select
                v-model="condition.field"
                placeholder="选择字段"
                @change="handleFieldChange(condition)"
                style="width: 100%"
              >
                <el-option
                  v-for="field in searchFields"
                  :key="field.prop"
                  :label="field.label"
                  :value="field.prop"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-select
                v-model="condition.operator"
                placeholder="选择条件"
                style="width: 100%"
              >
                <el-option
                  v-for="op in getOperatorOptions(condition.field)"
                  :key="op.value"
                  :label="op.label"
                  :value="op.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item v-if="!isNoValueOperator(condition.operator)">
              <!-- 日期时间范围选择 -->
              <el-date-picker
                v-if="getFieldType(condition.field) === 'daterange'"
                v-model="condition.value"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="getFieldType(condition.field) === 'date'"
                v-model="condition.value"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
              <!-- 下拉选择 -->
              <el-select
                v-else-if="getFieldType(condition.field) === 'select'"
                v-model="condition.value"
                placeholder="请选择值"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in getFieldOptions(condition.field)"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <!-- 数字输入 -->
              <el-input-number
                v-else-if="getFieldType(condition.field) === 'number'"
                v-model="condition.value"
                placeholder="请输入数字"
                style="width: 100%"
              />
              <!-- 文本输入 -->
              <el-input
                v-else
                v-model="condition.value"
                placeholder="请输入值"
                clearable
              />
            </el-form-item>
          </div>

          <div class="condition-actions">
            <el-button
              type="danger"
              size="small"
              @click="removeCondition(index)"
              :disabled="conditions.length === 1"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <div class="add-condition">
        <el-button type="primary" @click="addCondition" class="add-btn">
          <el-icon><Plus /></el-icon>
          添加搜索条件
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <el-button @click="handleReset">
          <el-icon><RefreshLeft /></el-icon>
          重置条件
        </el-button>
        <div class="footer-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { Search, Plus, RefreshLeft } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';

  defineOptions({ name: 'AdvancedSearch' });

  const props = defineProps({
    /** 弹窗是否可见 */
    modelValue: {
      type: Boolean,
      default: false
    },
    /** 搜索字段配置 */
    searchFields: {
      type: Array,
      default: () => []
    },
    /** 初始条件 */
    initialConditions: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits(['update:modelValue', 'search', 'close']);

  /** 弹窗可见性 */
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  });

  /** 搜索条件 */
  const conditions = ref([
    {
      field: '',
      operator: '$regex',
      value: ''
    }
  ]);

  /** 监听弹窗打开 */
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal) {
        initConditions();
      }
    }
  );

  /** 监听初始条件变化 */
  watch(
    () => props.initialConditions,
    (newConditions) => {
      if (props.modelValue && newConditions?.length > 0) {
        loadConditions(newConditions);
      }
    },
    { immediate: true, deep: true }
  );

  /** 初始化条件 */
  const initConditions = () => {
    if (props.initialConditions?.length > 0) {
      loadConditions(props.initialConditions);
    } else {
      resetConditions();
    }
  };

  /** 加载条件 */
  const loadConditions = (conditionList) => {
    conditions.value = conditionList.map((condition) => ({
      field: condition.field || '',
      operator: condition.operator || '$regex',
      value: condition.value || ''
    }));

    if (conditions.value.length === 0) {
      resetConditions();
    }
  };

  /** 重置条件 */
  const resetConditions = () => {
    conditions.value = [
      {
        field: '',
        operator: '$regex',
        value: ''
      }
    ];
  };

  /** 获取字段类型 */
  const getFieldType = (fieldProp) => {
    const field = props.searchFields.find((f) => f.prop === fieldProp);
    return field?.type || 'text';
  };

  /** 获取字段选项 */
  const getFieldOptions = (fieldProp) => {
    const field = props.searchFields.find((f) => f.prop === fieldProp);
    return field?.options || [];
  };

  /** 获取操作符选项 */
  const getOperatorOptions = (fieldProp) => {
    const fieldType = getFieldType(fieldProp);
    const baseOperators = [
      { label: '模糊匹配', value: '$regex' },
      { label: '等于', value: '$eq' },
      { label: '不等于', value: '$ne' }
    ];

    if (fieldType === 'number' || fieldType === 'date') {
      baseOperators.push(
        { label: '大于', value: '$gt' },
        { label: '小于', value: '$lt' },
        { label: '大于等于', value: '$gte' },
        { label: '小于等于', value: '$lte' }
      );
    }

    if (fieldType === 'select') {
      baseOperators.push(
        { label: '包含于', value: '$in' },
        { label: '不包含于', value: '$nin' }
      );
    }

    return baseOperators;
  };

  /** 判断是否为不需要值的操作符 */
  const isNoValueOperator = (operator) => {
    const noValueOperators = ['$null', '$not_null', '$exist', '$not_exist'];
    return noValueOperators.includes(operator);
  };

  /** 字段变化处理 */
  const handleFieldChange = (condition) => {
    condition.operator = '$regex';
    condition.value = '';
  };

  /** 添加条件 */
  const addCondition = () => {
    conditions.value.push({
      field: '',
      operator: '$regex',
      value: ''
    });
  };

  /** 移除条件 */
  const removeCondition = (index) => {
    if (conditions.value.length > 1) {
      conditions.value.splice(index, 1);
    }
  };

  /** 执行搜索 */
  const handleSearch = () => {
    // 验证条件
    const validConditions = conditions.value.filter((condition) => {
      if (!condition.field || !condition.operator) {
        return false;
      }

      if (isNoValueOperator(condition.operator)) {
        return true;
      }

      return (
        condition.value !== '' &&
        condition.value !== null &&
        condition.value !== undefined
      );
    });

    if (validConditions.length === 0) {
      EleMessage.warning('请至少设置一个有效的搜索条件');
      return;
    }

    // 确保返回正确的条件格式
    const formattedConditions = validConditions.map((condition) => ({
      field: condition.field,
      operator: condition.operator,
      value: condition.value
    }));

    emit('search', formattedConditions);
    handleClose();
  };

  /** 重置表单 */
  const handleReset = () => {
    resetConditions();
  };

  /** 关闭弹窗 */
  const handleClose = () => {
    visible.value = false;
    emit('close');
  };
</script>

<style lang="scss" scoped>
  .advanced-search-content {
    padding: 16px 0;
  }

  .search-tip {
    margin-bottom: 20px;
    padding: 12px;
    border-radius: 6px;
    background: var(--el-color-info-light-9);
    border: 1px solid var(--el-color-info-light-7);
  }

  .search-conditions {
    margin-bottom: 20px;
  }

  .condition-row {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    padding: 16px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;
    background: var(--el-bg-color-page);

    &:hover {
      border-color: var(--el-color-primary-light-7);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    }
  }

  .condition-number {
    flex-shrink: 0;

    .number-badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: var(--el-color-primary);
      color: white;
      font-size: 12px;
      font-weight: 600;
    }
  }

  .condition-form {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr 1.5fr;
    gap: 16px;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }

  .condition-actions {
    flex-shrink: 0;

    .el-button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .add-condition {
    text-align: center;
    padding: 16px 0;
    border-top: 1px solid var(--el-border-color-lighter);

    .add-btn {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-5);
      color: var(--el-color-primary);
      display: flex;
      align-items: center;
      gap: 6px;

      &:hover {
        background: var(--el-color-primary-light-8);
        border-color: var(--el-color-primary-light-3);
      }

      .el-icon {
        font-size: 14px;
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0 0;

    .el-button {
      display: flex;
      align-items: center;
      gap: 6px;

      .el-icon {
        font-size: 14px;
      }
    }
  }

  .footer-actions {
    display: flex;
    gap: 8px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .condition-form {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .condition-row {
      flex-direction: column;
      gap: 12px;
      padding: 12px;
    }

    .condition-number {
      align-self: center;
      margin-top: 0;
    }

    .condition-actions {
      align-self: center;
      margin-top: 0;
    }
  }
</style>
