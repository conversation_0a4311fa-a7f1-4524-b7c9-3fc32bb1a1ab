/** 默认主题 */
@use 'sass:map';
@use '../util.scss' as *;

// 过渡动画时间
$transition-base: 0.2s !default;
// 过渡动画慢速
$transition-slow: 0.3s !default;
// 美化滚动条样式选择器
$scrollbar-selector: '' !default;
// 输入框清空图标
$icon-clear-path: 'M512 43C768 43 981 256 981 512S768 981 512 981 43 768 43 512 256 43 512 43ZM683 277 512 448 341 277 277 341 448 512 277 683 341 747 512 576 683 747 747 683 576 512 747 341Z' !default;
// 表单验证通过图标
$icon-validate-path: 'M512 43C768 43 981 256 981 512S768 981 512 981 43 768 43 512 256 43 512 43ZM448 619 299 470 235 534 448 747 811 384 747 320Z' !default;
// 树表格展开图标
$icon-expand-path: 'M362 811 662 512 362 213' !default;
// 树表格加载图标
$icon-loading-path: 'M512 128V299M789 235 672 352M896 512H725M789 789 672 672M512 896V725M235 789 352 672M128 512H299M235 235 352 352' !default;
// 字体
$logo-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
  'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
  'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !default;

// 主题变量
$ele: () !default;
$ele: map.deep-merge(
  (
    'scrollbar': (
      // 滚动条
      'size': 10px,
      'radius': 6px,
      'padding': 2px,
      'color': #cfcfcf,
      'hover-color': #b6b6b6,
      'bg': transparent
    ),
    'layout': (
      // 布局
      'bg': elVar('bg-color', 'page'),
      'index': 99,
      'mask-index': calc(#{elVar('index', 'popper')} + 2000)
    ),
    'header': (
      // 顶栏
      'height': 54px,
      'bg': elVar('bg-color'),
      'shadow': 0 1px 4px rgba(0, 21, 41, 0.08),
      'line-color': elVar('border-color', 'extra-light'),
      'menu-height': eleVar('header', 'height'),
      'tools-padding': 12px,
      'tool-padding': 12px,
      'tool-height': eleVar('header', 'height'),
      'tool-radius': 0,
      'tool-hover-bg': rgba(0, 0, 0, 0.04),
      'dark-tool-hover-bg': rgba(255, 255, 255, 0.12),
      'dark-color': #fff,
      'dark-bg': #001529,
      'dark-shadow': 0 1px 4px rgba(0, 0, 0, 0.1),
      'primary-bg': elVar('color-primary'),
      'primary-shadow': eleVar('header', 'dark-shadow'),
      'primary-active-bg': transparent,
      'ghost-bg': hsla(0, 0%, 100%, 0.4),
      'ghost-filter': blur(8px)
    ),
    'logo': (
      // Logo
      'size': '18px',
      'family': $logo-family,
      'color': elVar('text-color', 'primary'),
      'dark-color': #fff,
      'shadow': 1px 2px 3px rgba(0, 21, 41, 0.08),
      'dark-shadow': 0 3px 4px rgba(0, 0, 0, 0.35)
    ),
    'sidebar': (
      // 侧栏
      'width': 220px,
      'collapse-width': 54px,
      'mix-width': 160px,
      'bg': elVar('bg-color'),
      'dark-bg': #001529,
      'shadow': 1px 0 3px rgba(0, 21, 41, 0.08),
      'dark-shadow': 0 0 4px rgba(0, 0, 0, 0.35),
      'transition': (width $transition-base, left $transition-base),
      'tool-height': 40px,
      'mobile-backdrop-filter': none,
      'ghost-bg': elVar('fill-color', 'lighter')
    ),
    'sidebox': (
      // 双侧栏一级
      'width': 80px
    ),
    'menu-thumb': (
      // 菜单滚动条
      'size': 8px,
      'radius': 4px,
      'padding': 2px,
      'color': #cfcfcf,
      'hover-color': #b6b6b6
    ),
    'menu-dark-thumb': (
      // 菜单暗色主题滚动条
      'color': #4d5b69,
      'hover-color': #66737f
    ),
    'alert': (
      // 警告提示
      'color': elVar('text-color', 'regular'),
      'size': elVar('font-size', 'small'),
      'padding': 6px 12px,
      'radius': elVar('border-radius', 'base'),
      'title-color': elVar('text-color', 'primary'),
      'title-size': elVar('font-size', 'base'),
      'title-line-height': 24px,
      'icon-size': elVar('font-size', 'medium'),
      'icon-margin': 0 8px 0 0,
      'close-size': 18px,
      'close-margin': 0 -4px 0 4px,
      'close-radius': elVar('border-radius', 'small'),
      'close-font-size': 13px,
      'close-color': elVar('text-color', 'placeholder'),
      'close-hover-color': elVar('text-color', 'primary'),
      'close-hover-bg': transparent,
      'rich-padding': 12px 14px,
      'rich-icon-size': 26px
    ),
    'alert-dark': (
      // 警告提示 dark 主题
      'color': rgba(255, 255, 255, 0.88),
      'title-color': #fff,
      'close-color': rgba(255, 255, 255, 0.88),
      'close-hover-color': #fff,
      'close-hover-bg': transparent
    ),
    'backtop': (
      // 返回顶部
      'color': elVar('color-primary'),
      'bg': elVar('bg-color', 'overlay'),
      'shadow': elVar('box-shadow', 'light')
    ),
    'message': (
      // 消息提示
      'color': elVar('text-color', 'regular'),
      'size': elVar('font-size', 'base'),
      'space': 16px,
      'padding': 12px 14px,
      'radius': elVar('border-radius', 'base'),
      'shadow': elVar('box-shadow', 'light'),
      'icon-size': elVar('font-size', 'large'),
      'icon-margin': 0 8px 0 0,
      'close-size': 18px,
      'close-margin': 0 -4px 0 8px,
      'close-radius': elVar('border-radius', 'small'),
      'close-font-size': elVar('font-size', 'base'),
      'close-color': elVar('text-color', 'placeholder'),
      'close-hover-color': elVar('text-color', 'primary'),
      'close-hover-bg': transparent,
      'plain-bg': elVar('bg-color', 'overlay'),
      'alert-padding': 10px 14px,
      'mask-color': elVar('overlay-color', 'lighter'),
      'index': calc(#{elVar('index', 'popper')} + 2002)
    ),
    'bottom-bar': (
      // 底部工具栏
      'padding': 8px 16px,
      'bg': elVar('bg-color', 'overlay'),
      'shadow': 0 -1px 5px rgba(0, 0, 0, 0.08)
    ),
    'card': (
      // 卡片标题
      'padding': 20px,
      'bg': elVar('bg-color'),
      'border': 1px solid elVar('border-color', 'light'),
      'radius': elVar('border-radius', 'base'),
      'shadow': elVar('box-shadow', 'lighter'),
      'hover-shadow': (
        0 1px 2px -2px rgba(0, 0, 0, 0.16),
        0 3px 6px 0 rgba(0, 0, 0, 0.12),
        0 5px 12px 4px rgba(0, 0, 0, 0.09)
      ),
      'header-padding': 14px 22px,
      'header-color': elVar('text-color', 'primary'),
      'header-font-size': elVar('font-size', 'medium'),
      'header-line-height': 24px,
      'header-font-weight': normal,
      'header-border': 1px solid elVar('border-color', 'light'),
      'footer-padding': 14px 22px,
      'footer-border': 1px solid elVar('border-color', 'light'),
      'collapse-size': 24px,
      'collapse-font-size': 16px,
      'collapse-color': elVar('text-color', 'secondary'),
      'collapse-radius': elVar('border-radius', 'small'),
      'collapse-margin': 1px -6px 0 0,
      'collapse-hover-color': elVar('text-color', 'primary'),
      'collapse-hover-bg': transparent
    ),
    'check-card': (
      // 可选卡片
      'radius': elVar('border-radius', 'base'),
      'border-color': elVar('border-color', 'light'),
      'hover-border-color': elVar('color-primary'),
      'hover-bg': transparent,
      'active-border-color': elVar('color-primary'),
      'active-bg': elVar('color-primary', 'light-9'),
      'disabled-border-color': elVar('disabled', 'border-color'),
      'disabled-bg': elVar('disabled', 'bg-color'),
      'disabled-opacity': 0.75,
      'arrow-color': elVar('color-primary'),
      'arrow-size': 6px,
      'arrow-radius': elVar('border-radius', 'small'),
      'arrow-disabled-color': elVar('disabled', 'text-color')
    ),
    'drawer': (
      // 抽屉
      'bg': elVar('bg-color', 'overlay'),
      'header-color': elVar('text-color', 'primary'),
      'header-font-size': elVar('font-size', 'medium'),
      'header-font-weight': normal,
      'header-line-height': 24px,
      'header-padding': 14px 16px,
      'header-border': 1px solid elVar('border-color', 'light'),
      'icon-size': 22px,
      'icon-font-size': 16px,
      'icon-color': elVar('text-color', 'secondary'),
      'icon-hover-color': elVar('text-color', 'primary'),
      'icon-hover-bg': transparent,
      'icon-radius': elVar('border-radius', 'small'),
      'body-padding': 20px,
      'footer-padding': 10px 16px,
      'footer-border': 1px solid elVar('border-color', 'light')
    ),
    'dropdown': (
      // 下拉菜单
      'padding': 6px 0,
      'item-height': 32px,
      'item-margin': 0,
      'item-padding': 0 16px,
      'item-radius': 0,
      'item-font-size': elVar('font-size', 'base'),
      'item-color': elVar('text-color', 'regular'),
      'item-hover-color': elVar('text-color', 'primary'),
      'item-hover-bg': elVar('fill-color', 'light'),
      'item-active-color': elVar('color-primary'),
      'item-active-bg': elVar('color-primary', 'light-9'),
      'item-active-hover-bg': elVar('color-primary', 'light-8'),
      'item-active-font-weight': bold,
      'item-danger-color': elVar('color-danger'),
      'item-danger-bg': elVar('color-danger', 'light-9'),
      'item-danger-hover-bg': elVar('color-danger', 'light-8'),
      'item-disabled-color': elVar('disabled', 'text-color'),
      'item-disabled-bg': elVar('disabled', 'bg-color'),
      'icon-size': 16px,
      'icon-margin': 0 8px 0 -2px,
      'arrow-size': 14px,
      'arrow-color': elVar('text-color', 'placeholder'),
      'arrow-padding': 0 0 0 6px,
      'arrow-margin': 0 -8px 0 auto,
      'divider': 1px solid elVar('border-color', 'light'),
      'divider-margin': 6px 0,
      'sm-padding': 3px 0,
      'sm-item-height': 24px,
      'sm-item-padding': 0 12px,
      'sm-item-font-size': elVar('font-size', 'extra-small'),
      'sm-icon-size': 14px,
      'sm-divider-margin': 3px 0,
      'lg-padding': 8px 0,
      'lg-item-height': 36px,
      'lg-item-padding': 0 20px,
      'lg-item-font-size': elVar('font-size', 'base'),
      'lg-icon-size': 16px,
      'lg-divider-margin': 8px 0
    ),
    'file': (
      // 文件列表
      'selector-index': 999,
      'selector-border': 1px solid #2f54eb,
      'selector-bg': rgba(0, 120, 215, 0.2),
      'header-height': 38px,
      'header-padding': 16px,
      'item-width': 110px,
      'item-space': 14px,
      'item-radius': elVar('border-radius', 'base'),
      'item-padding': 6px 4px,
      'item-border': 1px solid transparent,
      'item-active-border-color': elVar('color-primary', 'light-7'),
      'item-active-bg': elVar('color-primary', 'light-9'),
      'icon-height': 84px,
      'icon-size': 56px,
      'image-size': 84px,
      'image-radius': elVar('border-radius', 'base'),
      'title-margin': 4px,
      'td-height': 44px,
      'td-padding': 0 10px,
      'td-icon-szie': 24px,
      'td-icon-margin': 10px,
      'td-image-radius': elVar('border-radius', 'small'),
      'sort-color': hsla(0, 0%, 60%, 0.6),
      'sort-active-color': elVar('color-primary')
    ),
    'icon': (
      // 图标选择器
      'header-padding': 0 12px,
      'search-width': 120px,
      'search-padding': 8px 0,
      'search-icon-size': elVar('font-size', 'extra-small'),
      'tab-height': 42px,
      'tab-space': 16px,
      'tab-padding': 0 2px,
      'tab-radius': 0,
      'tab-font-size': elVar('font-size', 'base'),
      'tab-color': elVar('text-color', 'regular'),
      'tab-hover-color': elVar('color-primary'),
      'tab-hover-bg': transparent,
      'tab-active-color': elVar('color-primary'),
      'tab-active-bg': transparent,
      'tab-active-font-weight': normal,
      'tab-active-line': 2px,
      'menus-width': 120px,
      'menus-padding': 12px 0,
      'menu-height': 34px,
      'menu-space': 2px,
      'menu-padding': 0 12px,
      'menu-radius': 0,
      'menu-font-size': elVar('font-size', 'base'),
      'menu-color': elVar('text-color', 'regular'),
      'menu-hover-color': elVar('color-primary'),
      'menu-hover-bg': transparent,
      'menu-active-color': elVar('color-primary'),
      'menu-active-bg': elVar('color-primary', 'light-9'),
      'menu-active-font-weight': normal,
      'menu-active-line': 2.5px,
      'body-padding': 12px,
      'radius': elVar('border-radius', 'base'),
      'border': 1px solid elVar('border-color', 'light'),
      'font-size': 20px,
      'color': elVar('text-color', 'regular'),
      'hover-color': elVar('color-primary'),
      'hover-bg': elVar('color-primary', 'light-9'),
      'hover-border': 1px solid elVar('color-primary', 'light-7'),
      'active-color': elVar('color-primary'),
      'active-bg': elVar('color-primary', 'light-9'),
      'active-border': 2px solid elVar('color-primary')
    ),
    'loading': (
      // 加载
      'bg': elVar('mask-color'),
      'size': 19px,
      'space': 5px,
      'index': 2000,
      'sm-size': 13px,
      'sm-space': 3px,
      'lg-size': 29px,
      'lg-space': 8px
    ),
    'map': (
      // 地图位置选择
      'search-padding': 6px 12px
    ),
    'menu': (
      // 菜单
      'disabled-opacity': 0.46,
      'badge-margin': 0 -2px 0 6px,
      'icon-font-size': 15px,
      'icon-margin': 0 8px 0 0,
      'arrow-size': 14px,
      'arrow-margin': 0 -4px 0 6px,
      'bg': elVar('bg-color'),
      'border-color': elVar('border-color', 'light'),
      'padding': 4px 0,
      'base-level': 16px,
      'child-bg': rgba(0, 0, 0, 0.02),
      'collapse-width': 58px,
      'collapse-icon-size': 16px,
      'item-height': 40px,
      'item-margin': 6px,
      'item-padding': 0,
      'item-radius': 0,
      'item-color': elVar('text-color', 'primary'),
      'item-hover-color': elVar('color', 'primary'),
      'item-hover-bg': transparent,
      'item-focus-bg': transparent,
      'item-active-color': elVar('color-primary'),
      'item-active-bg': elVar('color-primary', 'light-9'),
      'item-active-line': 3px,
      'compact-font-size': 12px,
      'compact-icon-font-size': 18px,
      'compact-title-margin': 6px,
      'compact-item-padding': 12px 0,
      'compact-collapse-item-padding': 12px 0
    ),
    'menu-popup': (
      // 气泡菜单
      'min-width': 148px,
      'max-width': 260px,
      'max-height': calc(100vh - 10px),
      'bg': elVar('bg-color', 'overlay'),
      'radius': elVar('border-radius', 'base'),
      'shadow': elVar('box-shadow', 'light'),
      'item-padding': 16px,
      'item-radius': eleVar('menu', 'item-radius'),
      'dark-bg': elVar('menu', 'bg-color'),
      'dark-shadow': elVar('box-shadow', 'dark')
    ),
    'menu-horizontal': (
      // 水平菜单
      'padding': 0,
      'height': 56px,
      'item-padding': 16px,
      'item-margin': 0,
      'item-radius': 0,
      'hover-bg': transparent,
      'focus-bg': transparent,
      'active-bg': transparent,
      'arrow-margin': 0 -2px 0 6px,
      'line-margin': 14px,
      'line-size': 2px,
      'dark-hover-bg': transparent,
      'dark-focus-bg': transparent,
      'dark-active-bg': transparent,
      'popup-margin': 0px
    ),
    'menu-group': (
      // 分组菜单
      'color': elVar('text-color', 'secondary'),
      'font-size': 13px,
      'font-weight': normal,
      'icon-font-size': 12px,
      'padding-top': 4px,
      'padding-bottom': 2px,
      'padding-right': elVar('menu', 'base-level-padding'),
      'dark-color': rgba(255, 255, 255, 0.6)
    ),
    'menu-dark': (
      // 暗色菜单
      'bg': #001529,
      'child-bg': #000c17,
      'color': rgba(255, 255, 255, 0.8),
      'hover-color': #fff,
      'hover-bg': transparent,
      'focus-bg': transparent,
      'active-color': #fff,
      'active-bg': elVar('color-primary')
    ),
    'menu-colorful': (
      // 菜单彩色图标
      'icon-radius': 50%,
      'icon-size': 24px,
      'icon-color': #fff,
      'icon-font-size': 14px,
      'dot-size': 8px,
      'dot-margin': 0 8px 0 0,
      'dot-color': #d9d9d9,
      'dot-hover-color': #b6b6b6,
      'dot-active-color': elVar('color-primary'),
      'dark-dot-color': #888888,
      'dark-dot-hover-color': #dddddd,
      'dark-dot-active-color': #fff,
      'bg-1': #61b2fc,
      'bg-2': #7dd733,
      'bg-3': #32a2d4,
      'bg-4': #7383cf,
      'bg-5': #f5686f,
      'bg-6': #2bccce,
      'bg-7': #7dd733,
      'bg-8': #faad14
    ),
    'modal': (
      // 弹窗
      'bg': elVar('bg-color', 'overlay'),
      'radius': elVar('border-radius', 'base'),
      'header-color': elVar('text-color', 'primary'),
      'header-font-size': elVar('font-size', 'medium'),
      'header-font-weight': normal,
      'header-line-height': 24px,
      'header-padding': 14px 20px,
      'header-border': 1px solid elVar('border-color', 'light'),
      'icon-size': 22px,
      'icon-font-size': 16px,
      'icon-color': elVar('text-color', 'secondary'),
      'icon-hover-color': elVar('text-color', 'primary'),
      'icon-hover-bg': transparent,
      'icon-radius': elVar('border-radius', 'small'),
      'icon-space': 8px,
      'body-padding': 24px 22px,
      'form-body-padding': 24px 22px 12px 20px,
      'footer-padding': 10px 20px,
      'footer-border': 1px solid elVar('border-color', 'light'),
      'fullscreen-border': 1px solid elVar('border-color', 'light'),
      'mobile-space': 16px
    ),
    'page': (
      // 页面容器
      'padding': 16px,
      'max-width': 1120px
    ),
    'pagination': (
      // 分页
      'height': 32px,
      'padding': 0 6px,
      'radius': 32px,
      'space': 6px,
      'gap': 8px 12px,
      'color': elVar('text-color', 'regular'),
      'font-size': 14px,
      'bg': none,
      'border': none,
      'hover-color': elVar('text-color', 'primary'),
      'hover-bg': elVar('fill-color', 'light'),
      'hover-border': none,
      'active-color': #fff,
      'active-bg': elVar('color', 'primary'),
      'active-border': none,
      'active-font-weight': normal,
      'disabled-color': elVar('text-color', 'placeholder'),
      'disabled-bg': none,
      'disabled-border': none,
      'disabled-active-color': elVar('text-color', 'secondary'),
      'disabled-active-bg': elVar('fill-color'),
      'disabled-active-border': none,
      'sm-height': 24px,
      'sm-padding': 0 4px,
      'sm-radius': 24px,
      'sm-space': 4px,
      'sm-gap': 8px,
      'sm-font-size': 14px,
      'lg-height': 40px,
      'lg-padding': 0 8px,
      'lg-radius': 40px,
      'lg-space': 8px,
      'lg-gap': 8px 12px,
      'lg-font-size': 14px
    ),
    'pro-form': (
      // 高级表单
      'descriptions-label-wdith': 110px,
      'handle-color': #fff,
      'handle-font-size': 12px,
      'handle-height': 18px,
      'handle-bg': elVar('color-primary'),
      'handle-shadow': 2px 2px 4px 0 rgba(255, 255, 255, 0.6),
      'handle-padding': 0 4px,
      'handle-space': 4px,
      'handle-index': 9999,
      'item-wrapper-padding': 4px,
      'item-wrapper-border': 1px dashed elVar('border-color'),
      'item-wrapper-hover-border': 1px dashed elVar('color-primary'),
      'item-wrapper-active-border': 2px solid elVar('color-primary'),
      'item-wrapper-active-border-offset': -1px,
      'item-wrapper-active-index': 8,
      'container-wrapper-border-offset': -1px,
      'container-wrapper-active-border-offset': -2px,
      'container-wrapper-min-height': 48px,
      'container-div-min-height': 24px,
      'container-description-min-height': 32px
    ),
    'pro-form-builder': (
      // 表单构建器
      'group-label-bg': elVar('bg-color'),
      'group-label-backdrop-filter': none
    ),
    'segmented': (
      // 分段器
      'padding': 3px,
      'bg': elVar('fill-color', 'light'),
      'radius': 2px,
      'height': 28px,
      'font-size': 14px,
      'item-padding': 12px,
      'color': elVar('text-color', 'secondary'),
      'hover-color': elVar('text-color', 'primary'),
      'hover-bg': rgba(0, 0, 0, 0.04),
      'active-color': elVar('text-color', 'primary'),
      'active-bg': elVar('bg-color', 'overlay'),
      'active-shadow': 0 1px 3px 0 rgba(0, 0, 0, 0.08),
      'disabled-color': elVar('text-color', 'placeholder')
    ),
    'segmented-large': (
      // 分段器大号
      'padding': 4px,
      'radius': 4px,
      'height': 36px,
      'font-size': 16px,
      'item-padding': 16px
    ),
    'segmented-small': (
      // 分段器小号
      'padding': 2px,
      'radius': 2px,
      'height': 20px,
      'font-size': 13px,
      'item-padding': 7px
    ),
    'step': (
      // 步骤条
      'bg': elVar('bg-color'),
      'backdrop-filter': none,
      'line-size': 1px,
      'line-color': elVar('border-color', 'light'),
      'line-space': 8px,
      'icon-size': 32px,
      'icon-color': elVar('text-color', 'secondary'),
      'icon-font-size': elVar('font-size', 'base'),
      'icon-font-weight': normal,
      'icon-bg': elVar('bg-color'),
      'icon-border': 1px solid elVar('border-color'),
      'color': elVar('text-color', 'placeholder'),
      'font-size': elVar('font-size', 'medium'),
      'font-weight': normal,
      'line-height': 32px,
      'margin': 8px,
      'text-color': elVar('text-color', 'placeholder'),
      'text-font-size': elVar('font-size', 'base'),
      'text-line-height': 22px,
      'text-margin': 2px,
      'active-icon-color': #fff,
      'active-icon-bg': elVar('color-primary'),
      'active-icon-border': 1px solid elVar('color-primary'),
      'active-color': elVar('text-color', 'primary'),
      'active-text-color': elVar('text-color', 'regular'),
      'finish-icon-color': elVar('color-primary'),
      'finish-icon-font-size': elVar('font-size', 'large'),
      'finish-icon-bg': elVar('bg-color'),
      'finish-icon-border': 1px solid elVar('color-primary'),
      'finish-color': elVar('text-color', 'regular'),
      'finish-text-color': elVar('text-color', 'secondary'),
      'finish-line-color': elVar('color-primary'),
      'vertical-margin': 16px
    ),
    'tab-bar': (
      // 标签栏
      'padding': 0,
      'line-color': elVar('border-color', 'light'),
      'height': 40px,
      'space': 16px,
      'font-size': elVar('font-size', 'base'),
      'color': elVar('text-color', 'regular'),
      'hover-color': elVar('color-primary'),
      'hover-bg': transparent,
      'active-color': elVar('color-primary'),
      'active-bg': transparent,
      'active-font-weight': normal,
      'active-line-size': 2px,
      'active-line-color': elVar('color-primary'),
      'item-padding': 0 2px,
      'item-radius': 0
    ),
    'tab': (
      // 标签页
      'bg': elVar('bg-color'),
      'header-bg': elVar('fill-color', 'lighter'),
      'line-color': elVar('border-color', 'light'),
      'height': 40px,
      'padding': 16px,
      'color': elVar('text-color', 'regular'),
      'font-size': elVar('font-size', 'base'),
      'hover-color': elVar('color-primary'),
      'active-color': elVar('color-primary'),
      'close-size': elVar('font-size', 'base'),
      'close-color': elVar('text-color', 'secondary'),
      'close-font-size': elVar('font-size', 'base'),
      'close-radius': 50%,
      'close-hover-color': #fff,
      'close-hover-bg': elVar('color-error'),
      'active-close-color': elVar('text-color', 'secondary'),
      'active-close-hover-color': #fff,
      'active-close-hover-bg': elVar('color-error'),
      'icon-font-size': 15px,
      'tool-color': elVar('text-color', 'secondary'),
      'tool-font-size': elVar('font-size', 'base'),
      'tool-hover-color': elVar('text-color', 'primary'),
      'hover-index': 2,
      'active-index': 3,
      'scroll-left-shadow': inset 10px 0 10px -10px rgba(0, 0, 0, 0.15),
      'scroll-right-shadow': inset -10px 0 10px -10px rgba(0, 0, 0, 0.15),
      'sm-height': 32px,
      'sm-padding': 12px,
      'sm-font-size': elVar('font-size', 'small'),
      'sm-close-size': elVar('font-size', 'small'),
      'sm-close-font-size': elVar('font-size', 'small'),
      'sm-icon-font-size': 14px,
      'sm-tool-font-size': elVar('font-size', 'small'),
      'lg-height': 52px,
      'lg-padding': 22px,
      'lg-font-size': elVar('font-size', 'medium'),
      'lg-close-size': elVar('font-size', 'medium'),
      'lg-close-font-size': elVar('font-size', 'medium'),
      'lg-icon-font-size': 17px,
      'lg-tool-font-size': elVar('font-size', 'medium'),
      'simple-radius': 0,
      'simple-hover-color': elVar('text-color', 'primary'),
      'simple-hover-bg': rgba(0, 0, 0, 0.025),
      'simple-active-color': elVar('color-primary'),
      'simple-active-bg': elVar('color-primary', 'light-9'),
      'simple-active-weight': normal,
      'simple-active-line': 2px,
      'simple-line-display': block,
      'simple-angle-size': 0px,
      'simple-angle-display': none,
      'simple-tool-hover-bg': eleVar('tab', 'simple-hover-bg'),
      'indicator-dot-size': 8px,
      'indicator-dot-color': #d9d9d9,
      'indicator-dot-hover-color': #b6b6b6,
      'indicator-dot-active-color': elVar('color-primary'),
      'indicator-margin': 6px,
      'tag-space': 4px,
      'tag-radius': elVar('border-radius', 'base'),
      'button-space': 8px,
      'button-radius': 4px,
      'button-bg': hsla(0, 0%, 100%, 0.66),
      'button-hover-color': elVar('color-primary'),
      'button-hover-bg': elVar('bg-color'),
      'button-active-color': elVar('color-primary'),
      'button-active-bg': elVar('bg-color', 'overlay'),
      'button-active-shadow': none,
      'button-active-weight': normal
    ),
    'text': (
      // 文本
      'heading-weight': normal,
      'xxl': calc(#{elVar('font-size', 'extra-large')} + 6px),
      'xxxl': calc(#{elVar('font-size', 'extra-large')} + 34px)
    ),
    'timeline': (
      // 时间线
      'body-padding': 8px 16px 48px 16px,
      'title-font-size': 16px,
      'title-margin': 0 0 4px 0,
      'description-font-size': 13px,
      'icon-margin': 0 8px,
      'icon-font-size': 22px,
      'icon-color': elVar('text-color', 'placeholder'),
      'line-size': 2px,
      'line-color': elVar('border-color'),
      'turn-line-offset': 11px
    ),
    'table': (
      // 表格
      'radius': 0,
      'color': elVar('text-color', 'regular'),
      'border-color': elVar('border-color', 'light'),
      'font-size': elVar('font-size', 'base'),
      'line-height': 1.71428565,
      'padding': 9px 12px,
      'th-color': elVar('text-color', 'primary'),
      'th-font-weight': normal,
      'th-bg': elVar('fill-color', 'lighter'),
      'tr-bg': elVar('bg-color'),
      'tr-hover-bg': elVar('fill-color', 'light'),
      'tr-active-bg': elVar('color-primary', 'light-9'),
      'tr-active-hover-bg': elVar('color-primary', 'light-8'),
      'even-bg': elVar('fill-color', 'lighter'),
      'lg-size': elVar('font-size', 'base'),
      'lg-padding': 13px 16px,
      'lg-radius': 0,
      'sm-size': elVar('font-size', 'extra-small'),
      'sm-padding': 4px 8px,
      'sm-radius': 0,
      'icon-color': elVar('text-color', 'disabled'),
      'icon-hover-color': elVar('text-color', 'secondary'),
      'icon-hover-bg': rgba(0, 0, 0, 0.08),
      'icon-radius': elVar('border-radius', 'small'),
      'icon-margin': 6px,
      'sort-size': 5px,
      'sort-space': 4px,
      'sort-hover-bg': elVar('fill-color'),
      'filter-size': 14px,
      'filter-padding': 4px 2px,
      'filter-margin': 0 -6px 0 4px,
      'expand-margin': 0 4px 0 -2px,
      'fixed-left-shadow': inset 10px 0 10px -10px rgba(0, 0, 0, 0.15),
      'fixed-right-shadow': inset -10px 0 10px -10px rgba(0, 0, 0, 0.15),
      'fixed-backdrop-filter': none,
      'sticky-top': 0,
      'fixed-header-sticky-top': 94px,
      'maximized-sticky-top': 40px,
      'fix-height': 0.45px
    ),
    'table-filter': (
      // 表格筛选框
      'max-height': 386px,
      'padding': 6px 0,
      'item-height': 32px,
      'item-margin': 0,
      'item-padding': 0 16px,
      'item-radius': 0,
      'item-color': elVar('text-color', 'regular'),
      'item-hover-color': elVar('text-color', 'primary'),
      'item-hover-bg': elVar('fill-color', 'light'),
      'item-active-color': elVar('color-primary'),
      'item-active-bg': elVar('color-primary', 'light-9'),
      'item-active-hover-bg': elVar('color-primary', 'light-8'),
      'item-active-font-weight': bold,
      'footer-padding': 6px 10px,
      'footer-border': 1px solid elVar('border-color', 'light')
    ),
    'tool-column': (
      // 高级表格列配置
      'width': 220px,
      'max-height': 480px,
      'header-padding': 6px 14px 4px 14px,
      'header-border': 1px solid elVar('border-color', 'light'),
      'body-padding': 6px 0,
      'item-padding': 14px,
      'item-height': 28px,
      'item-hover-bg': elVar('color-primary', 'light-9'),
      'item-radius': 0,
      'btn-space': 1px,
      'btn-size': 18px,
      'btn-offset': -6px,
      'btn-radius': elVar('border-radius', 'small'),
      'btn-color': elVar('text-color', 'placeholder'),
      'btn-hover-color': elVar('text-color', 'primary'),
      'btn-hover-bg': transparent,
      'btn-active-color': elVar('color-primary'),
      'btn-active-bg': transparent
    ),
    'toolbar': (
      // 工具栏
      'padding': 0 16px,
      'bg': elVar('fill-color', 'lighter'),
      'border': 1px solid elVar('border-color', 'light'),
      'radius': 0,
      'space': 8px
    ),
    'toolbar-plain': (
      // 工具栏朴素风格
      'margin': 0 0 4px 0
    ),
    'tool': (
      // 工具栏按钮
      'font-size': elVar('font-size', 'base'),
      'padding': 6px,
      'border': 1px solid elVar('border-color'),
      'radius': elVar('border-radius', 'small'),
      'hover-bg': rgba(0, 0, 0, 0.025)
    ),
    'tool-plain': (
      // 工具栏按钮朴素风格
      'font-size': 16px
    ),
    'table-select': (
      // 下拉表格选择
      'padding': 10px,
      'th-bg': elVar('fill-color', 'light'),
      'tr-bg': elVar('bg-color', 'overlay'),
      'even-bg': elVar('fill-color', 'light'),
      'mobile-space': 16px
    ),
    'tree-select': (
      // 下拉树选择
      'padding': 6px 0,
      'item-disabled-color': elVar('disabled', 'text-color')
    ),
    'upload-list': (
      // 文件上传
      'width': 108px,
      'height': 108px,
      'padding': 6px,
      'gap': 8px,
      'border': 1px solid elVar('border-color'),
      'radius': elVar('border-radius', 'base'),
      'bg': elVar('bg-color'),
      'btn-color': elVar('text-color', 'secondary'),
      'btn-font-size': 22px,
      'btn-bg': elVar('fill-color', 'lighter'),
      'btn-border': 1px dashed elVar('border-color'),
      'btn-hover-border': 1px dashed elVar('color-primary'),
      'btn-drag-bg': elVar('color-primary', 'light-9'),
      'btn-disabled-color': elVar('disabled', 'text-color'),
      'btn-disabled-bg': elVar('disabled', 'bg-color'),
      'btn-disabled-border': 1px dashed elVar('disabled', 'border-color'),
      'tool-color': #fff,
      'tool-font-size': 12px,
      'tool-padding': 4px 0,
      'tool-bg': hsla(0, 0%, 40%, 0.8),
      'tool-hover-bg': hsla(0, 0%, 60%, 0.8),
      'del-size': 18px,
      'del-icon-margin': -2px -3px 0 0,
      'del-hover-bg': elVar('color-danger'),
      'del-shadow': 0 0 2px 0 rgba(255, 255, 255, 0.6),
      'thumbnail-color': elVar('text-color', 'regular'),
      'thumbnail-font-size': 12px,
      'thumbnail-icon-color': elVar('text-color', 'placeholder'),
      'thumbnail-icon-font-size': 24px,
      'thumbnail-gap': 4px,
      'progress-padding': 0 6px,
      'progress-bg': rgba(0, 0, 0, 0.6),
      'progress-color': #fff,
      'progress-font-size': 12px,
      'progress-tool-font-size': 12px,
      'progress-tool-padding': 4px,
      'progress-tool-radius': elVar('border-radius', 'small'),
      'progress-tool-gap': 4px,
      'progress-tool-bg': hsla(0, 0%, 60%, 0.4),
      'progress-tool-hover-bg': hsla(0, 0%, 60%, 0.8),
      'file-height': 26px,
      'file-padding': 8px,
      'file-btn-font-size': 15px,
      'file-tool-size': 20px,
      'file-tool-color': elVar('text-color', 'secondary'),
      'file-tool-radius': elVar('border-radius', 'small'),
      'file-tool-gap': 2px,
      'file-tool-hover-color': elVar('color-primary'),
      'file-tool-hover-bg': transparent,
      'file-del-margin': 0 -4px 0 2px,
      'file-del-hover-color': elVar('color-danger'),
      'file-thumbnail-font-size': 14px,
      'file-thumbnail-icon-color': elVar('text-color', 'secondary'),
      'file-thumbnail-icon-font-size': 14px
    ),
    'viewer': (
      'bg': radial-gradient(
          #{elVar('text-color', 'placeholder')} 8%,
          #{elVar('bg-color', 'page')} 8%
        )
        center / 16px 16px repeat
    ),
    'popper': (
      // 底层气泡
      'bg': elVar('bg-color', 'overlay'),
      'border': none,
      'radius': elVar('border-radius', 'base'),
      'shadow': elVar('box-shadow', 'light'),
      'arrow-bg': eleVar('popper', 'bg'),
      'arrow-size': 8px,
      'arrow-offset': -4px,
      'arrow-shadow': 0 0 8px 0 rgba(0, 0, 0, 0.06)
    ),
    'tooltip': (
      // 文字提示
      'color': #fff,
      'font-size': elVar('font-size', 'small'),
      'line-height': 22px,
      'padding': 6px 12px,
      'bg': #000,
      'border': none,
      'radius': elVar('border-radius', 'base'),
      'shadow': 0 0 8px 0 rgba(0, 0, 0, 0.06),
      'arrow-bg': eleVar('tooltip', 'bg'),
      'arrow-shadow': 0 0 8px 0 rgba(0, 0, 0, 0.06)
    ),
    'tooltip-light': (
      // 文字提示亮色风格
      'color': elVar('text-color', 'primary'),
      'bg': elVar('bg-color', 'overlay'),
      'border': none,
      'shadow': eleVar('popper', 'shadow'),
      'arrow-bg': eleVar('tooltip-light', 'bg'),
      'arrow-shadow': eleVar('popper', 'arrow-shadow')
    ),
    'popover': (
      // 气泡卡片
      'color': elVar('text-color', 'regular'),
      'font-size': elVar('font-size', 'base'),
      'padding': 12px 16px,
      'title-color': elVar('text-color', 'primary'),
      'title-font-size': elVar('font-size', 'base'),
      'title-font-weight': bold,
      'title-padding': 8px 16px,
      'title-border': 1px solid elVar('border-color', 'light')
    ),
    'popconfirm': (
      // 气泡确认框
      'padding': 14px 12px,
      'title-color': elVar('text-color', 'primary'),
      'title-font-size': elVar('font-size', 'base'),
      'title-font-weight': bold,
      'title-margin': 6px,
      'icon-font-size': 17px,
      'icon-margin': 2px 8px 0 0,
      'action-margin': 10px,
      'action-space': 8px
    ),
    'select': (
      // 下拉框
      'padding': 6px 0,
      'item-height': 32px,
      'item-margin': 0,
      'item-padding': 0 16px,
      'item-radius': 0,
      'item-active-bg': elVar('color-primary', 'light-9'),
      'item-active-hover-bg': elVar('color-primary', 'light-8'),
      'item-active-font-weight': bold,
      'icon-size': 16px,
      'icon-margin': 0 -4px 0 4px
    ),
    'autocomplete': (
      // 自动补全输入框
      'padding': 6px 0,
      'item-height': 32px,
      'item-margin': 0,
      'item-padding': 0 16px,
      'item-radius': 0
    ),
    'cascader': (
      // 级联选择器
      'menu-padding': 6px 0,
      'item-height': 32px,
      'item-margin': 0,
      'item-padding': 0 16px,
      'item-radius': 0,
      'item-active-bg': elVar('color-primary', 'light-9'),
      'item-active-hover-bg': elVar('color-primary', 'light-8'),
      'item-active-font-weight': bold
    ),
    'message-box': (
      // 消息弹出框
      'width': 380px,
      'radius': elVar('border-radius', 'base'),
      'header-color': elVar('text-color', 'primary'),
      'header-font-size': elVar('font-size', 'medium'),
      'header-font-weight': normal,
      'header-line-height': 24px,
      'header-padding': 18px 20px 8px 20px,
      'header-border': none,
      'icon-size': 22px,
      'icon-font-size': 18px,
      'icon-color': elVar('text-color', 'secondary'),
      'icon-hover-color': elVar('text-color', 'primary'),
      'icon-hover-bg': transparent,
      'icon-radius': elVar('border-radius', 'small'),
      'body-padding': 12px 20px,
      'status-size': 22px,
      'input-margin': 8px 0 6px 0,
      'footer-padding': 8px 20px 18px 20px,
      'footer-border': none,
      'mobile-space': 16px
    ),
    'notification': (
      // 通知
      'width': 360px,
      'border': none,
      'padding': 20px,
      'radius': elVar('border-radius', 'base'),
      'shadow': elVar('box-shadow', 'light'),
      'color': elVar('text-color', 'regular'),
      'size': elVar('font-size', 'base'),
      'icon-size': 26px,
      'icon-margin': -2px 12px 0 0,
      'title-color': elVar('text-color', 'primary'),
      'title-size': elVar('font-size', 'medium'),
      'title-font-weight': normal,
      'title-line-height': 22px,
      'title-padding': 0 22px 0 0,
      'body-margin': 8px,
      'close-size': 22px,
      'close-margin': 20px 24px 0 0,
      'close-radius': elVar('border-radius', 'small'),
      'close-font-size': 18px,
      'close-color': elVar('text-color', 'secondary'),
      'close-hover-color': elVar('text-color', 'primary'),
      'close-hover-bg': transparent
    ),
    'radio': (
      // 单选框
      'color': elVar('text-color', 'regular'),
      'font-size': elVar('font-size', 'base'),
      'size': 15px,
      'radius': 50%,
      'space': 16px,
      'bg': elVar('bg-color'),
      'border': 1px solid elVar('border-color'),
      'hover-border': 1px solid elVar('color-primary'),
      'active-color': elVar('text-color', 'regular'),
      'active-bg': elVar('color-primary'),
      'active-border': 1px solid elVar('color-primary'),
      'dot-size': 7px,
      'dot-color': #fff,
      'dot-radius': 50%,
      'disabled-color': elVar('disabled', 'text-color'),
      'disabled-bg': elVar('disabled', 'bg-color'),
      'disabled-border': 1px solid elVar('disabled', 'border-color'),
      'disabled-dot-color': elVar('disabled', 'text-color'),
      'sm-font-size': elVar('font-size', 'extra-small'),
      'sm-size': 13px,
      'sm-radius': 50%,
      'sm-dot-size': 5px,
      'sm-dot-radius': 50%,
      'lg-font-size': elVar('font-size', 'base'),
      'lg-size': 16px,
      'lg-radius': 50%,
      'lg-dot-size': 8px,
      'lg-dot-radius': 50%
    ),
    'checkbox': (
      // 多选框
      'color': elVar('text-color', 'regular'),
      'font-size': elVar('font-size', 'base'),
      'size': 16px,
      'radius': elVar('border-radius', 'small'),
      'space': 16px,
      'bg': elVar('bg-color'),
      'border': 1px solid elVar('border-color'),
      'hover-border': 1px solid elVar('color-primary'),
      'active-color': elVar('text-color', 'regular'),
      'icon-width': 5.7142857142857135px,
      'icon-height': 9.142857142857142px,
      'icon-left': 21.5%,
      'disabled-color': elVar('disabled', 'text-color'),
      'disabled-bg': elVar('disabled', 'bg-color'),
      'disabled-border': 1px solid elVar('disabled', 'border-color'),
      'sm-font-size': elVar('font-size', 'extra-small'),
      'sm-size': 14px,
      'sm-radius': elVar('border-radius', 'small'),
      'sm-icon-width': 4.5px,
      'sm-icon-height': 7px,
      'sm-icon-left': 24.5%,
      'lg-font-size': elVar('font-size', 'base'),
      'lg-size': 16px,
      'lg-radius': elVar('border-radius', 'small'),
      'lg-icon-width': 5.7142857142857135px,
      'lg-icon-height': 9.142857142857142px,
      'lg-icon-left': 21.5%
    ),
    'tag': (
      // 标签
      'size': 13px,
      'height': 24px,
      'padding': 0 8px,
      'radius': elVar('border-radius', 'small'),
      'close-size': 14px,
      'close-padding': 0,
      'close-margin': 0 -4px 0 2px,
      'close-radius': 3px,
      'color': elVar('color-primary'),
      'bg': elVar('color-primary', 'light-9'),
      'border-color': elVar('color-primary', 'light-7'),
      'close-color': elVar('color-primary'),
      'close-hover-color': elVar('color-primary'),
      'close-hover-bg': transparent,
      'info-color': elVar('text-color', 'regular'),
      'info-bg': elVar('color-info', 'light-9'),
      'info-border-color': elVar('border-color'),
      'info-close-color': elVar('text-color', 'secondary'),
      'info-close-hover-color': elVar('text-color', 'primary'),
      'info-close-hover-bg': transparent,
      'success-color': elVar('color-success'),
      'success-bg': elVar('color-success', 'light-9'),
      'success-border-color': elVar('color-success', 'light-7'),
      'success-close-color': elVar('color-success'),
      'success-close-hover-color': elVar('color-success'),
      'success-close-hover-bg': transparent,
      'warning-color': elVar('color-warning'),
      'warning-bg': elVar('color-warning', 'light-9'),
      'warning-border-color': elVar('color-warning', 'light-7'),
      'warning-close-color': elVar('color-warning'),
      'warning-close-hover-color': elVar('color-warning'),
      'warning-close-hover-bg': transparent,
      'danger-color': elVar('color-error'),
      'danger-bg': elVar('color-error', 'light-9'),
      'danger-border-color': elVar('color-error', 'light-7'),
      'danger-close-color': elVar('color-error'),
      'danger-close-hover-color': elVar('color-error'),
      'danger-close-hover-bg': transparent,
      'round-radius': 12px,
      'round-padding': 0 10px,
      'sm-size': 12px,
      'sm-height': 20px,
      'sm-padding': 0 4px,
      'sm-radius': elVar('border-radius', 'small'),
      'sm-close-size': 13px,
      'sm-close-padding': 0,
      'sm-close-margin': 0 -2px 0 2px,
      'sm-close-radius': 2px,
      'sm-round-radius': 10px,
      'sm-round-padding': 0 6px,
      'lg-size': 14px,
      'lg-height': 32px,
      'lg-padding': 0 12px,
      'lg-radius': elVar('border-radius', 'base'),
      'lg-close-size': 15px,
      'lg-close-padding': 2px,
      'lg-close-margin': 0 -8px 0 4px,
      'lg-close-radius': 4px,
      'lg-round-radius': 16px,
      'lg-round-padding': 0 14px,
      'dark-color': #fff,
      'dark-bg': elVar('color-primary'),
      'dark-close-color': #fff,
      'dark-close-hover-color': #fff,
      'dark-close-hover-bg': transparent,
      'dark-info-color': elVar('text-color', 'primary'),
      'dark-info-bg': elVar('color-info', 'light-7'),
      'dark-info-close-color': elVar('text-color', 'secondary'),
      'dark-info-close-hover-color': elVar('text-color', 'primary'),
      'dark-info-close-hover-bg': transparent,
      'dark-success-color': #fff,
      'dark-success-bg': elVar('color-success'),
      'dark-success-close-color': #fff,
      'dark-success-close-hover-color': #fff,
      'dark-success-close-hover-bg': transparent,
      'dark-warning-color': #fff,
      'dark-warning-bg': elVar('color-warning'),
      'dark-warning-close-color': #fff,
      'dark-warning-close-hover-color': #fff,
      'dark-warning-close-hover-bg': transparent,
      'dark-danger-color': #fff,
      'dark-danger-bg': elVar('color-error'),
      'dark-danger-close-color': #fff,
      'dark-danger-close-hover-color': #fff,
      'dark-danger-close-hover-bg': transparent,
      'plain-color': elVar('color-primary'),
      'plain-border-color': elVar('color-primary'),
      'plain-close-color': elVar('color-primary'),
      'plain-close-hover-color': elVar('color-primary'),
      'plain-close-hover-bg': transparent,
      'plain-info-color': elVar('text-color', 'regular'),
      'plain-info-border-color': elVar('color-info', 'light-3'),
      'plain-info-close-color': elVar('text-color', 'secondary'),
      'plain-info-close-hover-color': elVar('text-color', 'primary'),
      'plain-info-close-hover-bg': transparent,
      'plain-success-color': elVar('color-success'),
      'plain-success-border-color': elVar('color-success'),
      'plain-success-close-color': elVar('color-success'),
      'plain-success-close-hover-color': elVar('color-success'),
      'plain-success-close-hover-bg': transparent,
      'plain-warning-color': elVar('color-warning'),
      'plain-warning-border-color': elVar('color-warning'),
      'plain-warning-close-color': elVar('color-warning'),
      'plain-warning-close-hover-color': elVar('color-warning'),
      'plain-warning-close-hover-bg': transparent,
      'plain-danger-color': elVar('color-error'),
      'plain-danger-border-color': elVar('color-error'),
      'plain-danger-close-color': elVar('color-error'),
      'plain-danger-close-hover-color': elVar('color-error'),
      'plain-danger-close-hover-bg': transparent,
      'hit-border-color': elVar('color-primary'),
      'hit-info-border-color': elVar('color-info', 'light-3'),
      'hit-success-border-color': elVar('color-success'),
      'hit-warning-border-color': elVar('color-warning'),
      'hit-danger-border-color': elVar('color-error')
    ),
    'tree': (
      // 树
      'item-height': 30px,
      'item-line-height': 16px,
      'item-margin': 0,
      'item-padding': 0 16px,
      'item-radius': 0,
      'item-active-color': elVar('color-primary'),
      'item-active-weight': bold,
      'item-active-bg': elVar('color-primary', 'light-9'),
      'item-active-hover-bg': elVar('color-primary', 'light-8'),
      'expand-size': 12px,
      'expand-padding': 3px,
      'expand-margin': 0 2px 0 4px,
      'expand-radius': elVar('border-radius', 'small'),
      'expand-hover-bg': transparent
    ),
    'image-viewer': (
      // 图片预览
      'tool-bg': hsla(0, 0%, 40%, 0.6),
      'tool-hover-bg': hsla(0, 0%, 36%, 0.8),
      'tool-size': 42px,
      'tool-font-size': 20px,
      'tool-action-size': 42px,
      'tool-action-font-size': 18px,
      'tool-action-gap': 0,
      'tool-action-hover-bg': hsla(0, 0%, 32%, 0.4),
      'tool-shadow': 0 0 1px 0 rgba(255, 255, 255, 0.2),
      'disabled-opacity': 0.4
    ),
    'descriptions': (
      // 描述列表
      'radius': 0,
      'border-color': elVar('border-color', 'light'),
      'bg': elVar('fill-color', 'lighter')
    ),
    'datepicker': (
      // 日期选择器
      'header-height': 42px,
      'header-padding': 0 12px,
      'header-border': 1px solid elVar('border-color', 'light'),
      'label-size': 15px,
      'label-weight': bold,
      'label-color': elVar('text-color', 'regular'),
      'label-hover-color': elVar('color', 'primary'),
      'label-space': 8px,
      'icon-size': 15px,
      'icon-color': elVar('text-color', 'secondary'),
      'icon-hover-color': elVar('text-color', 'primary'),
      'icon-disabled-color': elVar('disabled', 'text-color'),
      'icon-space': 8px,
      'body-width': 288px,
      'body-padding': 8px 12px,
      'color': elVar('text-color', 'regular'),
      'font-size': elVar('font-size', 'base'),
      'off-color': elVar('text-color', 'placeholder'),
      'hover-color': elVar('text-color', 'primary'),
      'hover-bg': elVar('fill-color', 'light'),
      'cell-radius': 14px,
      'today-color': inherit,
      'today-border': 1px solid elVar('color-primary'),
      'today-weight': normal,
      'active-color': #fff,
      'active-bg': elVar('color-primary'),
      'disabled-color': elVar('disabled', 'text-color'),
      'disabled-bg': elVar('disabled', 'bg-color'),
      'year-width': 58px,
      'year-height': 26px,
      'year-radius': 16px,
      'year-padding': 22px 0,
      'year-space': 4px 0,
      'month-padding': 22px 0,
      'month-space': 4px 0,
      'end-margin': 3.5px,
      'footer-padding': 8px 12px,
      'footer-border': 1px solid elVar('border-color', 'light'),
      'inrange-bg': elVar('color-primary', 'light-9'),
      'inrange-hover-bg': elVar('color-primary', 'light-8'),
      'inner-border': elVar('border-color', 'light'),
      'sidebar-width': 120px,
      'sidebar-padding': 8px 0,
      'sidebar-border': 1px solid elVar('border-color', 'light'),
      'shortcut-height': 32px,
      'shortcut-size': elVar('font-size', 'base'),
      'shortcut-color': elVar('text-color', 'regular'),
      'shortcut-padding': 0 14px,
      'shortcut-margin': 0,
      'shortcut-radius': 0,
      'shortcut-hover-color': elVar('text-color', 'primary'),
      'shortcut-hover-bg': elVar('fill-color', 'light'),
      'range-icon-color': elVar('text-color', 'placeholder'),
      'range-icon-size': elVar('font-size', 'base')
    ),
    'timepicker': (
      // 时间选择器
      'width': 180px,
      'height': 220px,
      'padding': 8px 4px,
      'item-size': elVar('font-size', 'base'),
      'item-color': elVar('text-color', 'regular'),
      'item-height': 28px,
      'item-radius': 14px,
      'item-hover-bg': elVar('fill-color', 'light'),
      'item-hover-color': elVar('text-color', 'primary'),
      'item-active-bg': elVar('color-primary', 'light-9'),
      'item-active-color': elVar('color-primary'),
      'item-disabled-color': elVar('disabled', 'text-color'),
      'item-disabled-bg': transparent,
      'line': 1px solid elVar('border-color', 'light'),
      'line-padding': 3px,
      'line-margin': 4px,
      'arrow-color': elVar('text-color', 'secondary'),
      'arrow-size': elVar('font-size', 'base'),
      'footer-padding': 6px 10px,
      'button-height': 24px,
      'button-padding': 0 10px,
      'button-radius': elVar('border-radius', 'small'),
      'button-size': 12px,
      'button-space': 8px,
      'header-height': 42px,
      'header-border': 1px solid elVar('border-color', 'light'),
      'header-size': 14px,
      'range-space': 6px
    ),
    'input': (
      // 输入框
      'padding-left': 10px,
      'padding-right': 10px,
      'bg': transparent,
      'border': 1px solid elVar('border-color'),
      'radius': elVar('border-radius', 'base'),
      'hover-bg': transparent,
      'hover-border': 1px solid elVar('color-primary'),
      'hover-shadow': none,
      'focus-bg': transparent,
      'focus-border': 1px solid elVar('color-primary'),
      'focus-shadow': 0 0 0 2px elVar('color-primary', 'light-8'),
      'disabled-color': elVar('disabled', 'text-color'),
      'disabled-bg': elVar('disabled', 'bg-color'),
      'disabled-border': 1px solid elVar('disabled', 'border-color'),
      'icon-size': 14px,
      'icon-space': 6px,
      'icon-color': elVar('text-color', 'placeholder'),
      'icon-hover-color': elVar('text-color', 'secondary'),
      'eye-margin': 0 -2px 0 6px,
      'clear-margin': 0 -2px 0 6px,
      'clear-color': elVar('text-color', 'disabled'),
      'clear-hover-color': elVar('text-color', 'secondary'),
      'status-margin': 0 -2px 0 6px,
      'status-color': elVar('color-success'),
      'count-size': elVar('font-size', 'small'),
      'count-color': elVar('text-color', 'secondary'),
      'extra-color': elVar('text-color', 'regular'),
      'extra-bg': elVar('fill-color', 'light'),
      'extra-padding': 0 10px,
      'extra-line-height': normal,
      'extra-preset-margin': -1px -11px
    ),
    'input-error': (
      // 输入框验证失败
      'bg': transparent,
      'border': 1px solid elVar('color-error'),
      'hover-bg': transparent,
      'hover-border': 1px solid elVar('color-error'),
      'hover-shadow': none,
      'focus-bg': transparent,
      'focus-border': 1px solid elVar('color-error'),
      'focus-shadow': 0 0 0 2px elVar('color-error', 'light-8'),
      'disabled-bg': elVar('disabled', 'bg-color'),
      'disabled-border': 1px solid elVar('disabled', 'border-color'),
      'status-color': elVar('color-error')
    ),
    'input-sm': (
      // 输入框小尺寸
      'padding-left': 8px,
      'padding-right': 8px
    ),
    'input-lg': (
      // 输入框大尺寸
      'padding-left': 12px,
      'padding-right': 12px
    ),
    'textarea': (
      // 文本域
      'padding-top': 4px,
      'padding-bottom': 4px,
      'count-bg': elVar('bg-color'),
      'count-padding': 0,
      'count-right': 12px,
      'count-bottom': 6px
    ),
    'textarea-sm': (
      // 文本域小尺寸
      'padding-top': 2px,
      'padding-bottom': 2px
    ),
    'textarea-lg': (
      // 文本域大尺寸
      'padding-top': 8px,
      'padding-bottom': 8px
    )
  ),
  $ele
);
