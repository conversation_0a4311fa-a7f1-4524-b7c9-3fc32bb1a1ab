<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>应用系统全景视图</title>
    <script src="https://res.gemcoder.com/js/reload.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <script>
      tailwind.config = {
                  theme: {
                      extend: {
                          colors: {
                              primary: '#165DFF',
                              secondary: '#36CFC9',
                              accent: '#722ED1',
                              warning: '#FF7D00',
                              danger: '#F53F3F',
                              success: '#00B42A',
                              info: '#86909C',
                              light: '#F2F3F5',
                              dark: '#1D2129',
                              'primary-light': '#E8F3FF',
                              'secondary-light': '#E6FFFA',
                              'accent-light': '#F9F0FF'
                          },
                          fontFamily: {
                              inter: ['Inter', 'system-ui', 'sans-serif'],
                          },
                          boxShadow: {
                              'card': '0 2px 14px 0 rgba(0, 0, 0, 0.06)',
                              'card-hover': '0 4px 20px 0 rgba(0, 0, 0, 0.1)',
                          }
                      },
                  }
              }
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
          .content-auto {
              content-visibility: auto;
          }
          .scrollbar-hide {
              -ms-overflow-style: none;
              scrollbar-width: none;
          }
          .scrollbar-hide::-webkit-scrollbar {
              display: none;
          }
          .text-shadow {
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
          .bg-gradient-blue {
              background: linear-gradient(135deg, #165DFF 0%, #0E42D2 100%);
          }
          .transition-all-300 {
              transition: all 0.3s ease;
          }
      }
    </style>
  </head>
  <body class="font-inter bg-gray-50 text-dark min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50"></header>
    <!-- 主内容区 -->
    <main class="flex-grow container mx-auto px-4 py-6">
      <!-- 系统概览卡片 -->
      <section class="mb-8">
        <div class="bg-gradient-blue rounded-2xl p-6 text-white shadow-lg">
          <div
            class="flex flex-col md:flex-row md:items-center justify-between"
          >
            <div>
              <h2
                class="text-[clamp(1.5rem,3vw,2rem)] font-bold mb-2"
                data-yteditvalue="XX系统办公系统 (OA)"
              >
                XX系统办公系统 (OA)
              </h2>
              <p
                class="text-blue-100 max-w-2xl"
                data-yteditvalue="系统介绍，功能描述......     "
              >
                系统介绍，功能描述......
              </p>
            </div>
            <div class="mt-6 md:mt-0 flex space-x-4"></div>
          </div>
          <div
            class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mt-8"
          >
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">系统等级</p>
              <p class="font-semibold" data-yteditvalue="C">C</p>
            </div>
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">开发负责人</p>
              <p class="font-semibold">张明</p>
            </div>
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">运维负责人</p>
              <p class="font-semibold">李强</p>
            </div>
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">开发厂商</p>
              <p class="font-semibold" data-yteditvalue="泛微 ">泛微</p>
            </div>
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">是否交维</p>
              <p class="font-semibold flex items-center">
                <i class="fas fa-check-circle text-green-300 mr-1"> </i>
                是
              </p>
            </div>
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">交维日期</p>
              <p class="font-semibold">2023-01-15</p>
            </div>
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">是否演练过</p>
              <p class="font-semibold flex items-center">
                <i class="fas fa-check-circle text-green-300 mr-1"> </i>
                是
              </p>
            </div>
            <div
              class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
            >
              <p class="text-blue-100 text-sm mb-1">演练日期</p>
              <p class="font-semibold">2023-06-20</p>
            </div>
          </div>
        </div>
      </section>
      <!-- 主要内容网格 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 左侧列 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 技术栈信息 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-code text-primary mr-2"> </i>
                技术栈信息
              </h2>
            </div>
            <div class="p-5">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">
                    基础框架
                  </h3>
                  <div class="flex items-center mb-2">
                    <i class="fas fa-check text-success mr-2"> </i>
                    <span> 采用CFP框架开发 </span>
                  </div>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">中间件</h3>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="px-3 py-1 bg-primary-light text-primary text-sm rounded-full"
                    >
                      MQ
                    </span>
                    <span
                      class="px-3 py-1 bg-primary-light text-primary text-sm rounded-full"
                    >
                      Nacos
                    </span>
                    <span
                      class="px-3 py-1 bg-primary-light text-primary text-sm rounded-full"
                    >
                      Redis
                    </span>
                    <span
                      class="px-3 py-1 bg-primary-light text-primary text-sm rounded-full"
                    >
                      Nginx
                    </span>
                  </div>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">数据库</h3>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="px-3 py-1 bg-secondary-light text-secondary text-sm rounded-full"
                    >
                      MySQL 8
                    </span>
                    <span
                      class="px-3 py-1 bg-secondary-light text-secondary text-sm rounded-full"
                    >
                      Oracle 19c
                    </span>
                  </div>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">
                    开发语言
                  </h3>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="px-3 py-1 bg-accent-light text-accent text-sm rounded-full"
                    >
                      Java
                    </span>
                    <span
                      class="px-3 py-1 bg-accent-light text-accent text-sm rounded-full"
                    >
                      JavaScript
                    </span>
                    <span
                      class="px-3 py-1 bg-accent-light text-accent text-sm rounded-full"
                    >
                      Python
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <!-- 部署方式 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-server text-primary mr-2"> </i>
                部署方式
              </h2>
            </div>
            <div class="p-5">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">
                    部署模式
                  </h3>
                  <div class="flex items-center">
                    <span
                      class="px-4 py-2 bg-primary/10 text-primary rounded-lg font-medium"
                    >
                      PaaS云平台
                    </span>
                  </div>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">
                    高可用配置
                  </h3>
                  <div class="grid grid-cols-2 gap-3">
                    <div class="flex items-center">
                      <span class="text-sm mr-2"> 应用双活: </span>
                      <span class="flex items-center text-success">
                        <i class="fas fa-check-circle mr-1"> </i>
                        是
                      </span>
                    </div>
                    <div class="flex items-center">
                      <span class="text-sm mr-2"> 数据库双活: </span>
                      <span class="flex items-center text-success">
                        <i class="fas fa-check-circle mr-1"> </i>
                        是
                      </span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">
                    部署机房
                  </h3>
                  <div class="grid grid-cols-2 gap-3">
                    <div class="flex items-center">
                      <span class="text-sm mr-2"> 应用部署: </span>
                      <span> 主+备+贵阳三机房 </span>
                    </div>
                    <div class="flex items-center">
                      <span class="text-sm mr-2"> 数据库部署: </span>
                      <span> 主备机房 </span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-3">
                    备份策略
                  </h3>
                  <div class="grid grid-cols-2 gap-3">
                    <div class="flex items-center">
                      <span class="text-sm mr-2"> 备份类型: </span>
                      <span> 冷备+热备 </span>
                    </div>
                    <div class="flex items-center">
                      <span class="text-sm mr-2"> 备份周期: </span>
                      <span> 每日增量,每周全量 </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <!-- 资源使用情况 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-microchip text-primary mr-2"> </i>
                资源使用情况
              </h2>
            </div>
            <div class="p-5">
              <div class="flex border-b border-gray-100 mb-4">
                <button
                  class="px-4 py-2 font-medium text-primary border-b-2 border-primary"
                >
                  生产环境
                </button>
                <button
                  class="px-4 py-2 font-medium text-gray-500 hover:text-gray-700"
                >
                  测试环境
                </button>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- CPU使用率 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex justify-between items-start mb-3">
                    <div>
                      <h3 class="text-sm text-gray-500">CPU使用</h3>
                      <p class="text-2xl font-semibold mt-1">32核</p>
                    </div>
                    <div
                      class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary"
                    >
                      <i class="fas fa-microchip"> </i>
                    </div>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-primary h-2 rounded-full"
                      style="width: 65%"
                    ></div>
                  </div>
                </div>
                <!-- 内存使用 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex justify-between items-start mb-3">
                    <div>
                      <h3 class="text-sm text-gray-500">内存使用</h3>
                      <p class="text-2xl font-semibold mt-1">128GB</p>
                    </div>
                    <div
                      class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-success"
                    >
                      <i class="fas fa-memory"> </i>
                    </div>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-success h-2 rounded-full"
                      style="width: 42%"
                    ></div>
                  </div>
                </div>
                <!-- 磁盘使用 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex justify-between items-start mb-3">
                    <div>
                      <h3 class="text-sm text-gray-500">磁盘使用</h3>
                      <p class="text-2xl font-semibold mt-1">2TB</p>
                    </div>
                    <div
                      class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center text-warning"
                    >
                      <i class="fas fa-hdd"> </i>
                    </div>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-warning h-2 rounded-full"
                      style="width: 78%"
                    ></div>
                  </div>
                </div>
                <!-- NAS使用 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex justify-between items-start mb-3">
                    <div>
                      <h3 class="text-sm text-gray-500">NAS使用</h3>
                      <p class="text-2xl font-semibold mt-1">5TB</p>
                    </div>
                    <div
                      class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-accent"
                    >
                      <i class="fas fa-network-wired"> </i>
                    </div>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-accent h-2 rounded-full"
                      style="width: 35%"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <!-- 监控信息 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2
                class="text-lg font-semibold flex items-center"
                data-yteditvalue="监控信息  "
              >
                <i class="fas fa-chart-line text-primary mr-2"> </i>
                监控信息
              </h2>
            </div>
            <div class="p-5">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 综合技术监控 -->
                <div data-ytextravalue="extra-abewa2pez">
                  <h3
                    class="text-base font-medium mb-4"
                    contenteditable="true"
                    data-yteditvalue="技术监控"
                    data-ytindex="0"
                    data-ytoriginindex="0"
                    data-ytparentvalue="extra-abewa2pez"
                    style="outline: rgb(60, 142, 255) dashed 2px; opacity: 1;left:-14px;position:relative"
                  >
                    技术监控
                  </h3>
                  <div class="h-64" data-ytindex="1" data-ytoriginindex="1">
                    <div
                      _echarts_instance_="ec_1755084572337"
                      class="w-full h-full"
                      id="comprehensiveTechMonitorChart"
                      style="user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); position: relative;"
                    >
                      <div
                        style="position: relative; width: 465px; height: 256px; padding: 0px; margin: 0px; border-width: 0px; cursor: default;"
                      >
                        <canvas
                          data-zr-dom-id="zr_0"
                          height="409"
                          style="position: absolute; left: 0px; top: 0px; width: 465px; height: 256px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"
                          width="744"
                        >
                        </canvas>
                      </div>
                      <div class></div>
                    </div>
                  </div>
                </div>
                <!-- 业务监控 -->
                <div>
                  <h3 class="text-base font-medium mb-4">业务监控</h3>
                  <div class="h-64">
                    <div class="w-full h-full" id="businessMonitorChart"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <!-- 系统事件记录 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div
              class="p-5 border-b border-gray-100 flex justify-between items-center"
            >
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-exclamation-triangle text-warning mr-2"> </i>
                系统事件记录
              </h2>
              <a
                class="text-primary text-sm hover:underline"
                href="javascript:void(0);"
              >
                查看全部
              </a>
            </div>
            <div class="p-5">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- 事件级别统计 -->
                <div>
                  <h3 class="text-base font-medium mb-4">事件级别统计</h3>
                  <div class="h-48">
                    <div class="w-full h-full" id="eventLevelChart"></div>
                  </div>
                </div>
                <!-- 故障类别统计 -->
                <div>
                  <h3 class="text-base font-medium mb-4">故障类别统计</h3>
                  <div class="h-48">
                    <div class="w-full h-full" id="faultCategoryChart"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr
                    class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    <th class="px-5 py-3" data-yteditvalue="事件标题 ">
                      事件标题
                    </th>
                    <th class="px-5 py-3">事件级别</th>
                    <th class="px-5 py-3">故障类别</th>
                    <th class="px-5 py-3">时间</th>
                    <th class="px-5 py-3" data-yteditvalue="影响范围  ">
                      影响范围
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                  <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-5 py-4">
                      <div class="flex items-center">
                        <span class="w-2 h-2 rounded-full bg-danger mr-2">
                        </span>
                        <span data-yteditvalue="联合贷款事件   ">
                          联合贷款事件
                        </span>
                      </div>
                    </td>
                    <td class="px-5 py-4">
                      <span
                        class="px-2 py-1 text-xs rounded-full bg-red-100 text-danger"
                      >
                        I级
                      </span>
                    </td>
                    <td class="px-5 py-4 text-sm">应用服务故障(1)</td>
                    <td class="px-5 py-4 text-sm">2023-10-15 08:30</td>
                    <td class="px-5 py-4">
                      <span
                        class="px-2 py-1 text-xs rounded-full bg-red-100 text-danger"
                        data-yteditvalue="8笔放款  "
                      >
                        8笔放款
                      </span>
                    </td>
                  </tr>
                  <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-5 py-4">
                      <div class="flex items-center">
                        <span class="w-2 h-2 rounded-full bg-warning mr-2">
                        </span>
                        <span data-yteditvalue="自动审批事件   ">
                          自动审批事件
                        </span>
                      </div>
                    </td>
                    <td class="px-5 py-4">
                      <span
                        class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-warning"
                      >
                        II级
                      </span>
                    </td>
                    <td class="px-5 py-4 text-sm">中间件故障(8)</td>
                    <td class="px-5 py-4 text-sm">2023-10-14 16:45</td>
                    <td class="px-5 py-4">
                      <span
                        class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-warning"
                        data-yteditvalue="保单OCKR识别  "
                      >
                        保单OCKR识别
                      </span>
                    </td>
                  </tr>
                  <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-5 py-4">
                      <div class="flex items-center">
                        <span class="w-2 h-2 rounded-full bg-success mr-2">
                        </span>
                        <span data-yteditvalue="业务反馈事件    ">
                          业务反馈事件
                        </span>
                      </div>
                    </td>
                    <td class="px-5 py-4">
                      <span
                        class="px-2 py-1 text-xs rounded-full bg-green-100 text-success"
                      >
                        III级
                      </span>
                    </td>
                    <td class="px-5 py-4 text-sm">基础网络故障(6)</td>
                    <td class="px-5 py-4 text-sm">2023-10-14 10:15</td>
                    <td class="px-5 py-4">
                      <span
                        class="px-2 py-1 text-xs rounded-full bg-green-100 text-success"
                        data-yteditvalue="给别人操作    "
                      >
                        给别人操作
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>
          <!-- 系统变更记录 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden mt-6"
          >
            <div
              class="p-5 border-b border-gray-100 flex justify-between items-center"
            >
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-sync-alt text-primary mr-2"> </i>
                系统变更记录
              </h2>
              <a
                class="text-primary text-sm hover:underline"
                href="javascript:void(0);"
              >
                查看全部
              </a>
            </div>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr
                    class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    <th class="px-5 py-3">变更内容</th>
                    <th class="px-5 py-3">时间</th>
                    <th class="px-5 py-3">操作人</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                  <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-5 py-4 text-sm">
                      V2.3.0版本发布，新增报表导出功能
                    </td>
                    <td class="px-5 py-4 text-sm">2023-10-10</td>
                    <td class="px-5 py-4 text-sm">王开发</td>
                  </tr>
                  <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-5 py-4 text-sm">修复用户权限管理bug</td>
                    <td class="px-5 py-4 text-sm">2023-10-05</td>
                    <td class="px-5 py-4 text-sm">李工程师</td>
                  </tr>
                  <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-5 py-4 text-sm">数据库性能优化</td>
                    <td class="px-5 py-4 text-sm">2023-09-28</td>
                    <td class="px-5 py-4 text-sm">张DBA</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>
        </div>
        <!-- 右侧列 -->
        <div class="space-y-6">
          <!-- 业务流程图 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-project-diagram text-primary mr-2"> </i>
                业务流程图
              </h2>
            </div>
            <div class="p-5">
              <div
                class="relative rounded-lg overflow-hidden bg-gray-50 aspect-video"
              >
                <img
                  alt="业务流程图"
                  class="w-full h-full object-cover"
                  src="https://design.gemcoder.com/staticResource/echoAiSystemImages/6107aaf6424ebd759dd0f81602ff07ad.png"
                />
                <div
                  class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity"
                >
                  <button
                    class="bg-white text-primary px-4 py-2 rounded-lg font-medium"
                  >
                    <i class="fas fa-expand-alt mr-2"> </i>
                    查看大图
                  </button>
                </div>
              </div>
            </div>
          </section>
          <!-- 系统架构图 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-sitemap text-primary mr-2"> </i>
                系统架构图
              </h2>
            </div>
            <div class="p-5">
              <div class="flex justify-center">
                <img
                  alt="系统架构图"
                  class="max-w-full h-auto rounded-lg shadow-sm"
                  src="https://design.gemcoder.com/staticResource/echoAiSystemImages/2887cd19572de41f02615586f860dcd6.png"
                />
              </div>
            </div>
          </section>
          <!-- 维保信息 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-shield-alt text-primary mr-2"> </i>
                维保信息
              </h2>
            </div>
            <div class="p-5">
              <div class="space-y-4">
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-2">
                    维保方式
                  </h3>
                  <span
                    class="px-3 py-1 bg-primary-light text-primary text-sm rounded-full"
                  >
                    厂商远程
                  </span>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-2">
                    维保期限
                  </h3>
                  <div class="flex items-center justify-between">
                    <span> 2023-01-01 至 2024-12-31 </span>
                    <span class="text-warning text-sm">
                      <i class="fas fa-exclamation-circle mr-1"> </i>
                      剩余85天
                    </span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      class="bg-warning h-2 rounded-full"
                      style="width: 80%"
                    ></div>
                  </div>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-2">
                    维保厂家/联系人
                  </h3>
                  <p>神州数码科技有限公司 / 陈经理 (13800138000)</p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-2">
                    维保费用
                  </h3>
                  <p class="text-lg font-semibold">¥120,000.00/年</p>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-500 mb-2">
                    维保服务
                  </h3>
                  <div class="flex items-center">
                    <div class="flex items-center mr-4">
                      <i class="fas fa-check text-success mr-1"> </i>
                      <span class="text-sm"> 维保验收材料 </span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-check text-success mr-1"> </i>
                      <span class="text-sm"> 到期提醒 </span>
                    </div>
                  </div>
                </div>
                <button
                  class="w-full bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-all-300 flex items-center justify-center"
                >
                  <i class="fas fa-file-alt mr-2"> </i>
                  查看维保服务记录
                </button>
              </div>
            </div>
          </section>
          <!-- 系统文档 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div
              class="p-5 border-b border-gray-100 flex justify-between items-center"
            >
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-file-text text-primary mr-2"> </i>
                系统文档
              </h2>
              <a
                class="text-primary text-sm hover:underline"
                href="javascript:void(0);"
              >
                全部文档
              </a>
            </div>
            <div class="divide-y divide-gray-100">
              <div
                class="p-4 hover:bg-gray-50 transition-all-300 flex items-center justify-between"
              >
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded bg-red-100 flex items-center justify-center text-red-500 mr-3"
                  >
                    <i class="fas fa-file-pdf"> </i>
                  </div>
                  <div>
                    <h3 class="font-medium">系统需求规格说明书</h3>
                    <p class="text-xs text-gray-500">2.4MB · 2023-01-15</p>
                  </div>
                </div>
                <button
                  class="text-gray-400 hover:text-primary transition-colors"
                >
                  <i class="fas fa-download"> </i>
                </button>
              </div>
              <div
                class="p-4 hover:bg-gray-50 transition-all-300 flex items-center justify-between"
              >
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded bg-green-100 flex items-center justify-center text-green-500 mr-3"
                  >
                    <i class="fas fa-file-excel"> </i>
                  </div>
                  <div>
                    <h3 class="font-medium">数据字典表</h3>
                    <p class="text-xs text-gray-500">1.2MB · 2023-02-20</p>
                  </div>
                </div>
                <button
                  class="text-gray-400 hover:text-primary transition-colors"
                >
                  <i class="fas fa-download"> </i>
                </button>
              </div>
              <div
                class="p-4 hover:bg-gray-50 transition-all-300 flex items-center justify-between"
              >
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded bg-blue-100 flex items-center justify-center text-blue-500 mr-3"
                  >
                    <i class="fas fa-file-word"> </i>
                  </div>
                  <div>
                    <h3 class="font-medium">用户操作手册</h3>
                    <p class="text-xs text-gray-500">3.7MB · 2023-03-10</p>
                  </div>
                </div>
                <button
                  class="text-gray-400 hover:text-primary transition-colors"
                >
                  <i class="fas fa-download"> </i>
                </button>
              </div>
              <div
                class="p-4 hover:bg-gray-50 transition-all-300 flex items-center justify-between"
              >
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded bg-purple-100 flex items-center justify-center text-purple-500 mr-3"
                  >
                    <i class="fas fa-file-code"> </i>
                  </div>
                  <div>
                    <h3 class="font-medium">API接口文档</h3>
                    <p class="text-xs text-gray-500">1.8MB · 2023-04-05</p>
                  </div>
                </div>
                <button
                  class="text-gray-400 hover:text-primary transition-colors"
                >
                  <i class="fas fa-download"> </i>
                </button>
              </div>
            </div>
          </section>
          <!-- 快速操作 -->
          <section
            class="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all-300 overflow-hidden"
          >
            <div class="p-5 border-b border-gray-100">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fas fa-tasks text-primary mr-2"> </i>
                运维工单统计
              </h2>
            </div>
            <div class="p-5">
              <div class="h-64 mb-6">
                <div class="w-full h-full" id="workOrderChart"></div>
              </div>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <div class="flex items-center">
                    <span class="w-3 h-3 rounded-full bg-success mr-2"> </span>
                    <span class="text-sm"> 已上线 </span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-lg font-semibold mr-1"> 128 </span>
                    <span class="text-xs text-gray-500"> 工单 </span>
                  </div>
                </div>
                <div class="flex justify-between items-center">
                  <div class="flex items-center">
                    <span class="w-3 h-3 rounded-full bg-warning mr-2"> </span>
                    <span class="text-sm"> 执行上线 </span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-lg font-semibold mr-1"> 45 </span>
                    <span class="text-xs text-gray-500"> 工单 </span>
                  </div>
                </div>
                <div class="flex justify-between items-center">
                  <div class="flex items-center">
                    <span class="w-3 h-3 rounded-full bg-danger mr-2"> </span>
                    <span class="text-sm"> 终止 </span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-lg font-semibold mr-1"> 12 </span>
                    <span class="text-xs text-gray-500"> 工单 </span>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
    <!-- 图表初始化脚本 -->
    <script>
      // 页面加载时初始化渲染
              document.addEventListener('DOMContentLoaded', () => {
                  initCharts();
              });

      function initCharts() {
                  // 综合监控信息  图表 - 合并主机、中间件和数据库监控
                  const comprehensiveTechMonitorChart = echarts.init(document.getElementById('comprehensiveTechMonitorChart'));
                  const comprehensiveTechMonitorOption = {
                      color: ['#165DFF', '#36CFC9', '#722ED1', '#FF7D00', '#F53F3F', '#00B42A', '#86909C'],
                      tooltip: {
                          trigger: 'item'
                      },
                      legend: {
                          orient: 'vertical',
                          left: 10
                      },
                      series: [
                          {
                              name: '技术资源类型',
                              type: 'pie',
                              radius: ['40%', '70%'],
                              avoidLabelOverlap: false,
                              itemStyle: {
                                  borderRadius: 10,
                                  borderColor: '#fff',
                                  borderWidth: 2
                              },
                              label: {
                                  show: false,
                                  position: 'center'
                              },
                              emphasis: {
                                  label: {
                                      show: true,
                                      fontSize: 16,
                                      fontWeight: 'bold'
                                  }
                              },
                              labelLine: {
                                  show: false
                              },
                              data: [
                                  { value: 35, name: '物理机' },
                                  { value: 45, name: '虚拟机' },
                                  { value: 20, name: '容器' },
                                  { value: 30, name: 'Web服务器' },
                                  { value: 25, name: '应用服务器' },
                                  { value: 40, name: '关系型数据库' },
                                  { value: 30, name: 'NoSQL数据库' }
                              ]
                          }
                      ]
                  };
                  comprehensiveTechMonitorChart.setOption(comprehensiveTechMonitorOption);

                  // 业务监控图表 - x轴最近3年，Y轴数量
                  const businessMonitorChart = echarts.init(document.getElementById('businessMonitorChart'));
                  const businessMonitorOption = {
                      color: ['#165DFF'],
                      tooltip: {
                          trigger: 'axis',
                          axisPointer: {
                              type: 'shadow'
                          }
                      },
                      grid: {
                          left: '3%',
                          right: '4%',
                          bottom: '3%',
                          containLabel: true
                      },
                      xAxis: {
                          type: 'category',
                          data: ['2021', '2022', '2023']
                      },
                      yAxis: {
                          type: 'value',
                          name: '数量'
                      },
                      series: [
                          {
                              name: '业务交易量',
                              type: 'bar',
                              data: [1200, 1900, 2500]
                          }
                      ]
                  };
                  businessMonitorChart.setOption(businessMonitorOption);

                  // 事件级别统计图表
                  const eventLevelChart = echarts.init(document.getElementById('eventLevelChart'));
                  const eventLevelOption = {
                      color: ['#F53F3F', '#FF7D00', '#00B42A', '#86909C'],
                      tooltip: {
                          trigger: 'item'
                      },
                      legend: {
                          orient: 'vertical',
                          left: 10
                      },
                      series: [
                          {
                              name: '事件级别',
                              type: 'pie',
                              radius: '60%',
                              data: [
                                  { value: 15, name: 'I级' },
                                  { value: 30, name: 'II级' },
                                  { value: 45, name: 'III级' },
                                  { value: 10, name: 'IV级' }
                              ],
                              itemStyle: {
                                  borderRadius: 4,
                                  borderColor: '#fff',
                                  borderWidth: 2
                              }
                          }
                      ]
                  };
                  eventLevelChart.setOption(eventLevelOption);

                  // 故障类别统计图表
                  const faultCategoryChart = echarts.init(document.getElementById('faultCategoryChart'));
                  const faultCategoryOption = {
                      color: ['#165DFF', '#36CFC9', '#722ED1', '#FF7D00', '#F53F3F', '#00B42A', '#86909C', '#FF5722', '#52C41A', '#FAAD14', '#7CB305'],
                      tooltip: {
                          trigger: 'axis',
                          axisPointer: {
                              type: 'shadow'
                          }
                      },
                      grid: {
                          left: '3%',
                          right: '4%',
                          bottom: '15%',
                          containLabel: true
                      },
                      xAxis: {
                          type: 'category',
                          data: ['应用服务故障', '应用设计缺陷', '业务需求缺陷', '外部系统异常', '数据库故障', '基础网络故障', '基础硬件故障', '中间件故障', '运商线路异常', '产品配置', '操作咨询类'],
                          axisLabel: {
                              interval: 0,
                              rotate: 45,
                              align: 'right'
                          }
                      },
                      yAxis: {
                          type: 'value',
                          name: '数量'
                      },
                      series: [
                          {
                              name: '故障数量',
                              type: 'bar',
                              data: [12, 8, 5, 7, 10, 15, 6, 9, 4, 3, 11]
                          }
                      ]
                  };
      faultCategoryChart.setOption(faultCategoryOption);

                  // 运维工单统计图表
                  const workOrderChart = echarts.init(document.getElementById('workOrderChart'));
                  const workOrderOption = {
                      color: ['#00B42A', '#FF7D00', '#F53F3F'],
                      tooltip: {
                          trigger: 'item'
                      },
                      series: [
                          {
                              name: '工单状态',
                              type: 'pie',
                              radius: '80%',
                              center: ['50%', '50%'],
                              data: [
                                  { value: 128, name: '已上线' },
                                  { value: 45, name: '执行上线' },
                                  { value: 12, name: '终止' }
                              ],
                              itemStyle: {
                                  borderRadius: 4,
                                  borderColor: '#fff',
                                  borderWidth: 2
                              },
                              label: {
                                  show: true,
                                  position: 'outside',
                                  formatter: '{b}: {c} ({d}%)'
                              }
                          }
                      ]
                  };
                  workOrderChart.setOption(workOrderOption);

                  // 响应窗口大小变化
                  window.addEventListener('resize', () => {
                      comprehensiveTechMonitorChart.resize();
                      businessMonitorChart.resize();
      eventLevelChart.resize();
                      faultCategoryChart.resize();
                      workOrderChart.resize();
                  });
              }
    </script>
  </body>
</html>
