<template>
  <el-drawer
    :model-value="visible"
    title="编辑工单"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="form-container">
      <!-- 工单信息列表 -->
      <div class="info-list">
        <div class="info-item">
          <span class="info-label">工单名称：</span>
          <span class="info-value">{{ form.tickets_name || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">创建人：</span>
          <span class="info-value">{{ form.tickets_applicant || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">部门名称：</span>
          <span class="info-value">{{ form.department_name || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">终止时间：</span>
          <span class="info-value">{{ formatTime(form.tickets_endtime) }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">上线时间：</span>
          <span class="info-value">{{
            formatTime(form.reality_start_date)
          }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">工单状态：</span>
          <span class="info-value">{{ form.tickets_status || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">流程ID：</span>
          <span class="info-value">{{ form.flow_instance_id || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">工单描述：</span>
          <span class="info-value">{{ form.description || '-' }}</span>
        </div>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 系统名称字段（可编辑） -->
        <el-form-item label="系统名称" prop="system_name">
          <el-select
            v-model="form.system_name"
            placeholder="请选择系统名称"
            style="width: 100%"
            filterable
            :loading="systemLoading"
          >
            <el-option
              v-for="system in systemOptions"
              :key="system.value"
              :label="system.label"
              :value="system.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSave">
          更新
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { updateInst, searchBusiness } from '@/api/cmdb';

  defineOptions({ name: 'WorkOrderEdit' });

  const props = defineProps({
    modelValue: Boolean,
    data: {
      type: Object,
      required: true
    }
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const visible = computed(() => props.modelValue);

  const formRef = ref(null);
  const loading = ref(false);
  const systemLoading = ref(false);
  const systemOptions = ref([]);

  const form = ref({
    tickets_name: '',
    tickets_applicant: '',
    system_name: '',
    department_name: '',
    tickets_endtime: '',
    reality_start_date: '',
    tickets_status: 0,
    flow_instance_id: '',
    description: ''
  });

  const rules = {
    system_name: [
      { required: true, message: '请选择系统名称', trigger: 'change' }
    ]
  };

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleClosed = () => {
    // 可以在这里添加关闭时的清理逻辑
  };

  const loadSystemOptions = async () => {
    systemLoading.value = true;
    try {
      const res = await searchBusiness();
      systemOptions.value = res.rows.map((item) => ({
        label: item.bk_inst_name,
        value: item.bk_biz_name
      }));
    } catch (e) {
      console.error('加载系统列表失败:', e);
    }
    systemLoading.value = false;
  };

  const formatTime = (timeValue) => {
    if (!timeValue) return '-';
    try {
      const date = new Date(timeValue);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return '-';
    }
  };

  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = {
          bkObjId: 'tickets',
          instInfoMap: {
            bk_inst_id: props.data.bk_inst_id,
            system_name: form.value.system_name
          }
        };

        await updateInst(data);
        EleMessage.success('更新成功');
        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '更新失败');
      }
      loading.value = false;
    });
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        loadSystemOptions();
        // 填充表单数据
        Object.assign(form.value, props.data);
      }
    }
  );

  onMounted(() => {
    loadSystemOptions();
  });
</script>

<style scoped>
  .form-container {
    padding: 0 16px;
  }

  .info-list {
    background: #f5f7fa;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
  }

  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    line-height: 1.5;
  }

  .info-item:last-child {
    margin-bottom: 0;
  }

  .info-label {
    flex-shrink: 0;
    width: 120px;
    color: #606266;
    font-weight: 500;
  }

  .info-value {
    flex: 1;
    color: #303133;
    word-break: break-all;
  }

  .drawer-footer {
    text-align: right;
    padding: 16px;
    border-top: 1px solid #e4e7ed;
  }

  .drawer-footer .el-button {
    margin-left: 8px;
  }
</style>
