<!-- 操作列按钮 -->
<template>
  <ElLink
    v-if="editLinkProps !== false"
    type="primary"
    underline="never"
    v-bind="(editLinkProps === true ? void 0 : editLinkProps) || {}"
    @click="handleEditBtnClick"
  >
    {{ lang.edit }}
  </ElLink>
  <ElDivider direction="vertical" />
  <ElLink
    v-if="delLinkProps !== false"
    type="danger"
    underline="never"
    v-bind="(delLinkProps === true ? void 0 : delLinkProps) || {}"
    @click="handleDelBtnClick"
  >
    {{ lang.delete }}
  </ElLink>
  <slot></slot>
</template>

<script setup>
  import { ElLink, ElDivider } from 'element-plus';

  defineOptions({ name: 'TableAction' });

  const props = defineProps({
    /** 修改按钮属性 */
    editLinkProps: [Boolean, Object],
    /** 删除按钮属性 */
    delLinkProps: [Boolean, Object],
    /** 数据 */
    item: Object,
    /** 国际化 */
    lang: {
      type: Object,
      required: true
    }
  });

  const emit = defineEmits({
    /** 操作按钮点击事件 */
    btnClick: (_action, _e, _item) => true
  });

  /** 按钮点击事件 */
  const handleBtnClick = (action, e) => {
    emit('btnClick', action, e, props.item);
  };

  /** 修改按钮点击事件 */
  const handleEditBtnClick = (e) => {
    handleBtnClick('edit', e);
  };

  /** 除按钮点击事件 */
  const handleDelBtnClick = (e) => {
    handleBtnClick('del', e);
  };
</script>
