<template>
  <ele-page>
    <!-- 搜索组件 -->
    <simple-search
      :fields="searchFields"
      :default-field="defaultField"
      :advanced-conditions="advancedConditions"
      @search="handleSimpleSearch"
      @reset="handleSearchReset"
      @advanced-search="showAdvancedModal"
      @clear-advanced="clearAdvancedConditions"
      @remove-advanced-condition="removeAdvancedCondition"
    />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <cmdb-pro-table
        ref="tableRef"
        row-key="bk_biz_id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '应用系统管理' }"
        cache-key="businessTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="handleCreate"
            >新建
          </el-button>
          <el-button
            type="danger"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            :disabled="selections.length === 0"
            @click="handleBatchDelete"
            >批量删除
          </el-button>
        </template>

        <template #system_level="{ row }">
          <el-tag :type="getLevelType(row.system_level)" size="small">
            {{ row.system_level || '-' }}
          </el-tag>
        </template>

        <template #whether_handed_over_for_maintenance="{ row }">
          <el-tag
            :type="
              row.whether_handed_over_for_maintenance === '是'
                ? 'success'
                : 'danger'
            "
            size="small"
          >
            {{ row.whether_handed_over_for_maintenance || '-' }}
          </el-tag>
        </template>

        <template #whether_drilled="{ row }">
          <el-tag
            :type="row.whether_drilled === '是' ? 'success' : 'danger'"
            size="small"
          >
            {{ row.whether_drilled || '-' }}
          </el-tag>
        </template>

        <template #whether_equipped_with_cfp_framework="{ row }">
          <el-tag
            :type="
              row.whether_equipped_with_cfp_framework === '是'
                ? 'success'
                : 'info'
            "
            size="small"
          >
            {{ row.whether_equipped_with_cfp_framework || '否' }}
          </el-tag>
        </template>

        <template #application_active_active="{ row }">
          <el-tag
            :type="row.application_active_active === '是' ? 'success' : 'info'"
            size="small"
          >
            {{ row.application_active_active || '否' }}
          </el-tag>
        </template>

        <template #database_active_active="{ row }">
          <el-tag
            :type="row.database_active_active === '是' ? 'success' : 'info'"
            size="small"
          >
            {{ row.database_active_active || '否' }}
          </el-tag>
        </template>

        <template #business_logic_diagram="{ row }">
          <div
            v-if="hasAttachments(row.business_logic_diagram)"
            class="image-list"
          >
            <el-popover placement="bottom" width="auto" trigger="click">
              <template #reference>
                <div class="image-item">
                  <el-icon class="image-icon">
                    <Picture />
                  </el-icon>
                  <span
                    v-if="getAttachmentCount(row.business_logic_diagram) > 1"
                    class="image-count-text"
                  >
                    【{{ getAttachmentCount(row.business_logic_diagram) }}】
                  </span>
                </div>
              </template>
              <div class="image-gallery">
                <div
                  v-for="(url, index) in getImageUrls(
                    row.business_logic_diagram
                  )"
                  :key="index"
                  class="gallery-item"
                >
                  <img
                    :src="url"
                    :alt="`业务逻辑图${index + 1}`"
                    class="gallery-thumbnail"
                    @click="openImagePreview(url)"
                  />
                </div>
              </div>
            </el-popover>
          </div>
          <span v-else>-</span>
        </template>

        <template #technical_flow_diagram="{ row }">
          <div
            v-if="hasAttachments(row.technical_flow_diagram)"
            class="image-list"
          >
            <el-popover placement="bottom" width="auto" trigger="click">
              <template #reference>
                <div class="image-item">
                  <el-icon class="image-icon">
                    <Picture />
                  </el-icon>
                  <span
                    v-if="getAttachmentCount(row.technical_flow_diagram) > 1"
                    class="image-count-text"
                  >
                    【{{ getAttachmentCount(row.technical_flow_diagram) }}】
                  </span>
                </div>
              </template>
              <div class="image-gallery">
                <div
                  v-for="(url, index) in getImageUrls(
                    row.technical_flow_diagram
                  )"
                  :key="index"
                  class="gallery-item"
                >
                  <img
                    :src="url"
                    :alt="`技术流程图${index + 1}`"
                    class="gallery-thumbnail"
                    @click="openImagePreview(url)"
                  />
                </div>
              </div>
            </el-popover>
          </div>
          <span v-else>-</span>
        </template>

        <template #documents="{ row }">
          <div v-if="hasDocuments(row.documents)" class="file-list">
            <el-popover placement="bottom" width="auto" trigger="click">
              <template #reference>
                <div class="file-item document-item">
                  <el-icon class="file-icon document-icon">
                    <component
                      :is="
                        getDocumentIcon(
                          getDocuments(row.documents)[0]?.fileName || '文档'
                        )
                      "
                    />
                  </el-icon>
                  <span
                    v-if="getDocumentCount(row.documents) > 1"
                    class="file-count-text"
                  >
                    【{{ getDocumentCount(row.documents) }}】
                  </span>
                </div>
              </template>
              <div class="attachment-options">
                <div
                  v-for="(document, index) in getDocuments(row.documents)"
                  :key="index"
                  class="attachment-item"
                >
                  <div class="attachment-info">
                    <el-icon class="attachment-icon">
                      <component
                        :is="getDocumentIcon(document.fileName || '文档')"
                      />
                    </el-icon>
                    <span class="attachment-name">{{
                      document.fileName || `文档${index + 1}`
                    }}</span>
                  </div>
                  <div class="attachment-actions">
                    <el-button
                      type="primary"
                      size="small"
                      link
                      @click="previewDocument(document)"
                    >
                      预览
                    </el-button>
                    <el-button
                      type="default"
                      size="small"
                      link
                      @click="downloadDocument(document)"
                    >
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </el-popover>
          </div>
          <span v-else>-</span>
        </template>

        <template #action="{ row }">
          <el-link type="info" underline="never" @click="handleView(row)">
            详情
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" underline="never" @click="handleEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" underline="never" @click="handleDelete(row)">
            删除
          </el-link>
        </template>
      </cmdb-pro-table>
    </ele-card>

    <!-- 高级搜索弹窗 -->
    <advanced-search
      v-model="showAdvanced"
      :search-fields="searchFields"
      :initial-conditions="advancedConditions"
      @search="handleAdvancedSearch"
      @close="closeAdvancedModal"
    />

    <!-- 新增/编辑弹窗 -->
    <BusinessForm
      v-model="formVisible"
      :form-data="currentBusiness"
      :is-edit="isEdit"
      @done="handleRefresh"
    />

    <!-- 详情弹窗 -->
    <BusinessDetail v-model="detailVisible" :business-data="currentBusiness" />

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="70%"
      append-to-body
    >
      <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
    </el-dialog>

    <!-- 文件预览组件 -->
    <FilePreview ref="filePreviewRef" v-model="showFilePreview" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { searchBusinessByCondition, updateBusinessStatus } from '@/api/cmdb';
  import BusinessForm from './components/business-form.vue';
  import BusinessDetail from './components/business-detail.vue';
  import SimpleSearch from '@/components/Search/simple-search.vue';
  import AdvancedSearch from '@/components/Search/advanced-search.vue';
  import { PlusOutlined, DeleteOutlined } from '@/components/icons';
  import {
    Document,
    DocumentCopy,
    Reading,
    Picture
  } from '@element-plus/icons-vue';
  import FilePreview from '@/components/FilePreview/index.vue';
  defineOptions({ name: 'BusinessManage' });

  // 搜索相关状态
  const currentSearchParams = ref({});
  const advancedConditions = ref([]);
  const showAdvanced = ref(false);

  // 搜索字段配置
  const searchFields = ref([
    { prop: 'bk_biz_name', label: '业务名称', type: 'text' },
    { prop: 'business_collection', label: '所属项目', type: 'text' },
    {
      prop: 'system_level',
      label: '系统级别',
      type: 'select',
      options: [
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' }
      ]
    },
    { prop: 'system_version', label: '系统版本', type: 'text' },
    { prop: 'system_developer', label: '开发负责人', type: 'text' },
    { prop: 'system_maintainer', label: '运维负责人', type: 'text' },
    { prop: 'development_vendor', label: '开发厂商', type: 'text' },
    {
      prop: 'whether_handed_over_for_maintenance',
      label: '是否交维',
      type: 'select',
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
      ]
    },
    {
      prop: 'handover_date_for_maintenance',
      label: '交维日期',
      type: 'daterange'
    },
    {
      prop: 'whether_drilled',
      label: '是否演练过',
      type: 'select',
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
      ]
    },
    { prop: 'drill_date', label: '演练日期', type: 'daterange' },
    {
      prop: 'whether_equipped_with_cfp_framework',
      label: '是否有CFP框架',
      type: 'select',
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
      ]
    },
    { prop: 'used_system_framework', label: '使用系统框架', type: 'text' },
    {
      prop: 'application_active_active',
      label: '应用双活',
      type: 'select',
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
      ]
    },
    {
      prop: 'database_active_active',
      label: '数据库双活',
      type: 'select',
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
      ]
    }
  ]);

  // 表格选中数据
  const selections = ref([]);

  // 表格实例
  const tableRef = ref(null);

  // 弹窗状态
  const formVisible = ref(false);
  const detailVisible = ref(false);
  const isEdit = ref(false);

  // 当前操作的业务数据
  const currentBusiness = ref(null);

  // 图片预览相关
  const imagePreviewVisible = ref(false);
  const previewImageUrl = ref('');

  // 文件预览相关
  const filePreviewRef = ref(null);
  const showFilePreview = ref(false);

  // 默认搜索字段
  const defaultField = ref({
    prop: 'bk_biz_name',
    label: '应用名称'
  });

  // 表格列配置
  const columns = computed(() => [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'bk_biz_name',
      label: '业务名称',
      width: 200,
      fixed: 'left',
      showOverflowTooltip: true
    },
    {
      prop: 'business_collection',
      label: '所属项目',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'system_level',
      label: '系统级别',
      width: 100,
      align: 'center',
      slot: 'system_level'
    },
    {
      prop: 'system_version',
      label: '系统版本',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'introduction',
      label: '系统介绍',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'function',
      label: '实现功能',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'system_developer',
      label: '开发负责人',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'system_maintainer',
      label: '运维负责人',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'development_vendor',
      label: '开发厂商',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'whether_handed_over_for_maintenance',
      label: '是否交维',
      width: 100,
      align: 'center',
      slot: 'whether_handed_over_for_maintenance'
    },
    {
      prop: 'handover_date_for_maintenance',
      label: '交维日期',
      width: 120,
      formatter: (row) => formatDate(row.handover_date_for_maintenance)
    },
    {
      prop: 'whether_drilled',
      label: '是否演练过',
      width: 110,
      align: 'center',
      slot: 'whether_drilled'
    },
    {
      prop: 'drill_date',
      label: '演练日期',
      width: 120,
      formatter: (row) => formatDate(row.drill_date)
    },
    {
      prop: 'whether_equipped_with_cfp_framework',
      label: 'CFP框架',
      width: 100,
      align: 'center',
      slot: 'whether_equipped_with_cfp_framework'
    },
    {
      prop: 'used_system_framework',
      label: '系统框架',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'middleware',
      label: '中间件',
      width: 150,
      showOverflowTooltip: true
      // formatter: (row) => Array.isArray(row.middleware) ? row.middleware.join(', ') : (row.middleware || '-')
    },
    {
      prop: 'company_configuration_database',
      label: '配置数据库',
      width: 150,
      showOverflowTooltip: true
      // formatter: (row) => Array.isArray(row.company_configuration_database) ? row.company_configuration_database.join(', ') : (row.company_configuration_database || '-')
    },
    {
      prop: 'development_language',
      label: '开发语言',
      width: 120,
      showOverflowTooltip: true
      // formatter: (row) => Array.isArray(row.development_language) ? row.development_language.join(', ') : (row.development_language || '-')
    },
    {
      prop: 'deployment_method',
      label: '部署方式',
      width: 150,
      align: 'center',
      slot: 'deployment_method'
    },
    {
      prop: 'application_active_active',
      label: '应用双活',
      width: 100,
      align: 'center',
      slot: 'application_active_active'
    },
    {
      prop: 'application_deployment_computer_room',
      label: '应用部署机房',
      width: 150,
      showOverflowTooltip: true,
      formatter: (row) =>
        Array.isArray(row.application_deployment_computer_room)
          ? row.application_deployment_computer_room.join(', ')
          : row.application_deployment_computer_room || '-'
    },
    {
      prop: 'application_backup_method',
      label: '应用备份方式',
      width: 150,
      showOverflowTooltip: true,
      formatter: (row) =>
        Array.isArray(row.application_backup_method)
          ? row.application_backup_method.join(', ')
          : row.application_backup_method || '-'
    },
    {
      prop: 'application_backup_tool',
      label: '应用备份工具',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'application_backup_cycle',
      label: '应用备份周期',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'computer_room_for_application_backup_files',
      label: '应用备份文件机房',
      width: 180,
      showOverflowTooltip: true,
      formatter: (row) =>
        Array.isArray(row.computer_room_for_application_backup_files)
          ? row.computer_room_for_application_backup_files.join(', ')
          : row.computer_room_for_application_backup_files || '-'
    },
    {
      prop: 'address_of_application_backup_files',
      label: '应用备份文件地址',
      width: 180,
      showOverflowTooltip: true
    },
    {
      prop: 'database_active_active',
      label: '数据库双活',
      width: 120,
      align: 'center',
      slot: 'database_active_active'
    },
    {
      prop: 'database_deployment_computer_room',
      label: '数据库部署机房',
      width: 160,
      showOverflowTooltip: true,
      formatter: (row) =>
        Array.isArray(row.database_deployment_computer_room)
          ? row.database_deployment_computer_room.join(', ')
          : row.database_deployment_computer_room || '-'
    },
    {
      prop: 'database_version',
      label: '数据库版本',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'database_backup_method',
      label: '数据库备份方式',
      width: 150,
      showOverflowTooltip: true,
      formatter: (row) =>
        Array.isArray(row.database_backup_method)
          ? row.database_backup_method.join(', ')
          : row.database_backup_method || '-'
    },
    {
      prop: 'database_backup_tool',
      label: '数据库备份工具',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'database_backup_cycle',
      label: '数据库备份周期',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'computer_room_for_database_backup_files',
      label: '数据库备份文件机房',
      width: 180,
      showOverflowTooltip: true,
      formatter: (row) =>
        Array.isArray(row.computer_room_for_database_backup_files)
          ? row.computer_room_for_database_backup_files.join(', ')
          : row.computer_room_for_database_backup_files || '-'
    },
    {
      prop: 'address_of_database_backup_files',
      label: '数据库备份文件地址',
      width: 180,
      showOverflowTooltip: true
    },
    {
      prop: 'business_logic_diagram',
      label: '业务逻辑图',
      width: 120,
      align: 'center',
      slot: 'business_logic_diagram'
    },
    {
      prop: 'technical_flow_diagram',
      label: '技术流程图',
      width: 120,
      align: 'center',
      slot: 'technical_flow_diagram'
    },
    {
      prop: 'documents',
      label: '文档',
      width: 100,
      align: 'center',
      slot: 'documents'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  // 数据源函数
  const datasource = async ({ pages }) => {
    let conditions = [];

    // 处理简单搜索条件
    if (currentSearchParams.value.condition) {
      Object.keys(currentSearchParams.value.condition).forEach((field) => {
        const value = currentSearchParams.value.condition[field];
        if (typeof value === 'object' && value !== null) {
          // 处理操作符对象格式，例如 { $regex: "keyword" }, { $ne: "value" }
          Object.keys(value).forEach((operator) => {
            if (
              operator === '$regex' ||
              operator === '$eq' ||
              operator === '$ne'
            ) {
              conditions.push({
                field,
                operator,
                value: value[operator]
              });
            }
          });
        } else if (value !== undefined && value !== null && value !== '') {
          // 处理直接值
          conditions.push({
            field,
            operator: '$eq',
            value
          });
        }
      });
    }

    // 处理高级搜索条件
    if (advancedConditions.value.length > 0) {
      advancedConditions.value.forEach((condition) => {
        conditions.push(condition);
      });
    }

    const params = {
      condition: conditions.reduce((acc, condition) => {
        if (condition.operator === '$eq') {
          acc[condition.field] = condition.value;
        } else if (condition.operator === '$regex') {
          acc[condition.field] = { $regex: condition.value };
        } else if (condition.operator === '$ne') {
          acc[condition.field] = { $ne: condition.value };
        }
        return acc;
      }, {}),
      page: {
        start: (pages.pageNum - 1) * pages.pageSize,
        limit: pages.pageSize,
        sort: 'bk_biz_id'
      }
    };

    try {
      const result = await searchBusinessByCondition(params);
      return result;
    } catch (error) {
      EleMessage.error(error.message || '获取数据失败');
      return {
        rows: [],
        total: 0
      };
    }
  };

  // 刷新表格数据
  const handleRefresh = () => {
    selections.value = [];
    tableRef.value?.reload?.();
  };

  // 处理简单搜索
  const handleSimpleSearch = (params) => {
    currentSearchParams.value = params;
    handleRefresh();
  };

  // 处理搜索重置
  const handleSearchReset = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    selections.value = [];
    handleRefresh();
  };

  // 显示高级搜索弹窗
  const showAdvancedModal = () => {
    showAdvanced.value = true;
  };

  // 关闭高级搜索弹窗
  const closeAdvancedModal = () => {
    showAdvanced.value = false;
  };

  // 处理高级搜索
  const handleAdvancedSearch = (conditions) => {
    advancedConditions.value = conditions;
    handleRefresh();
  };

  // 清空高级搜索条件
  const clearAdvancedConditions = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    selections.value = [];
    handleRefresh();
  };

  // 移除单个高级搜索条件
  const removeAdvancedCondition = (index) => {
    advancedConditions.value.splice(index, 1);
    selections.value = [];
    handleRefresh();
  };

  // 新增
  const handleCreate = () => {
    currentBusiness.value = null;
    isEdit.value = false;
    formVisible.value = true;
  };

  // 编辑
  const handleEdit = (row) => {
    currentBusiness.value = { ...row };
    isEdit.value = true;
    formVisible.value = true;
  };

  // 删除
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定删除业务"${row.bk_biz_name}"吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
      const params = {
        bizInfoMap: {
          bk_biz_id: row.bk_biz_id,
          flag: 'disabled'
        }
      };
      await updateBusinessStatus(params);
      EleMessage.success('删除成功');
      handleRefresh();
    } catch (e) {
      if (e !== 'cancel') {
        EleMessage.error(e.message || '删除失败');
      }
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selections.value.length === 0) {
      EleMessage.warning('请选择要删除的数据');
      return;
    }

    try {
      await ElMessageBox.confirm(
        `确定删除选中的 ${selections.value.length} 条记录吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      // 批量删除逻辑
      const deletePromises = selections.value.map((item) =>
        updateBusinessStatus({
          bizInfoMap: {
            bk_biz_id: item.bk_biz_id,
            flag: 'disabled'
          }
        })
      );

      await Promise.all(deletePromises);
      EleMessage.success('批量删除成功');
      handleRefresh();
    } catch (e) {
      if (e !== 'cancel') {
        EleMessage.error(e.message || '批量删除失败');
      }
    }
  };

  // 查看详情
  const handleView = (row) => {
    currentBusiness.value = { ...row };
    detailVisible.value = true;
  };

  // 获取级别标签类型
  const getLevelType = (level) => {
    const levelTypes = {
      A: 'danger',
      B: 'warning',
      C: 'primary'
    };
    return levelTypes[level] || 'info';
  };

  // 格式化日期
  const formatDate = (dateValue) => {
    if (!dateValue) return '-';
    const date = new Date(dateValue);
    return date.toLocaleDateString('zh-CN');
  };

  // 检查是否有附件（图片）
  const hasAttachments = (attachments) => {
    if (!attachments) return false;
    if (typeof attachments === 'string') {
      return attachments.trim() !== '';
    }
    if (Array.isArray(attachments)) {
      return attachments.length > 0;
    }
    return false;
  };

  // 获取附件数量（图片）
  const getAttachmentCount = (attachments) => {
    if (!attachments) return 0;
    if (typeof attachments === 'string') {
      return attachments.split(',').filter((url) => url.trim() !== '').length;
    }
    if (Array.isArray(attachments)) {
      return attachments.length;
    }
    return 0;
  };

  // 检查是否有文档
  const hasDocuments = (documents) => {
    if (!documents) return false;
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        return Array.isArray(parsed) && parsed.length > 0;
      } catch {
        return documents.trim() !== '';
      }
    }
    if (Array.isArray(documents)) {
      return documents.length > 0;
    }
    return false;
  };

  // 获取文档数量
  const getDocumentCount = (documents) => {
    if (!documents) return 0;
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        return Array.isArray(parsed) ? parsed.length : 0;
      } catch {
        return documents.trim() !== '' ? 1 : 0;
      }
    }
    if (Array.isArray(documents)) {
      return documents.length;
    }
    return 0;
  };

  // 获取图片URL列表
  const getImageUrls = (attachments) => {
    if (!attachments) return [];
    if (typeof attachments === 'string') {
      return attachments.split(',').filter((url) => url.trim() !== '');
    }
    if (Array.isArray(attachments)) {
      return attachments;
    }
    return [];
  };

  // 获取文档列表
  const getDocuments = (documents) => {
    if (!documents) return [];
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        if (Array.isArray(parsed)) {
          return parsed.map((item) => ({
            url: item.url,
            fileName: item.fileName,
            ossId: item.ossId
          }));
        }
      } catch {
        return documents.trim() !== ''
          ? [{ url: documents, fileName: '文档', ossId: '' }]
          : [];
      }
    }
    if (Array.isArray(documents)) {
      return documents.map((item) => ({
        url: item.url,
        fileName: item.fileName,
        ossId: item.ossId
      }));
    }
    return [];
  };

  // 获取文档图标
  const getDocumentIcon = (filename) => {
    const lowerName = filename.toLowerCase();
    if (lowerName.endsWith('.pdf')) {
      return Reading;
    } else if (lowerName.endsWith('.doc') || lowerName.endsWith('.docx')) {
      return DocumentCopy;
    }
    return Document;
  };

  // 打开图片预览
  const openImagePreview = (url) => {
    previewImageUrl.value = url;
    imagePreviewVisible.value = true;
  };

  // 预览文档
  const previewDocument = (document) => {
    filePreviewRef.value?.previewFile(document);
  };

  // 下载文档
  const downloadDocument = async (document) => {
    const url = document.url;
    const fileName = document.fileName || '文档';

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('下载失败:', error);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };
</script>

<style scoped>
  ::v-deep .el-drawer__header {
    margin-bottom: 0px;
  }
  .more-items {
    font-size: 12px;
    color: #909399;
  }

  /* 图片预览样式 */
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .image-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
  }

  .image-item:hover {
    transform: translateY(-1px);
  }

  .image-icon {
    font-size: 18px;
    color: var(--el-color-success);
    flex-shrink: 0;
  }

  .image-count-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 2px;
  }

  .image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    max-width: 320px;
  }

  .gallery-item {
    position: relative;
    cursor: pointer;
  }

  .gallery-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.3s ease;
  }

  .gallery-thumbnail:hover {
    border-color: var(--el-color-primary);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  /* 文档预览样式 */
  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .file-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
  }

  .file-item:hover {
    transform: translateY(-1px);
  }

  .file-icon {
    font-size: 18px;
    flex-shrink: 0;
  }

  .document-icon {
    color: var(--el-color-primary);
  }

  .file-count-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 2px;
  }

  .attachment-options {
    max-width: 320px;
  }

  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .attachment-item:last-child {
    border-bottom: none;
  }

  .attachment-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .attachment-icon {
    font-size: 16px;
    color: var(--el-color-primary);
    flex-shrink: 0;
  }

  .attachment-name {
    font-size: 14px;
    color: var(--el-text-color-regular);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .attachment-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }
</style>
