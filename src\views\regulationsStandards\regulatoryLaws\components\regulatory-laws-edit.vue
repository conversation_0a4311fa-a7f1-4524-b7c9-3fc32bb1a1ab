<!-- 监管法规编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑监管法规' : '新建监管法规'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="文件名称" prop="file_name">
              <el-input
                v-model="form.file_name"
                placeholder="请输入文件名称"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="发布机构" prop="issuing_authority">
              <el-select
                v-model="form.issuing_authority"
                placeholder="请选择发布机构"
                style="width: 100%"
                filterable
                allow-create
                default-first-option
              >
                <el-option label="中国人民银行" value="中国人民银行" />
                <el-option label="原银保监会" value="原银保监会" />
                <el-option
                  label="国家金融监督管理总局"
                  value="国家金融监督管理总局"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="issue_time">
              <el-date-picker
                v-model="form.issue_time"
                type="date"
                placeholder="选择发布时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="生效时间" prop="effective_time">
              <el-date-picker
                v-model="form.effective_time"
                type="date"
                placeholder="选择生效时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效时间" prop="expiration_time">
              <el-date-picker
                v-model="form.expiration_time"
                type="date"
                placeholder="选择失效时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="管理领域" prop="management_domain">
              <el-select
                v-model="form.management_domain"
                placeholder="请选择管理领域"
                style="width: 100%"
                filterable
                allow-create
                default-first-option
              >
                <el-option label="科技外包" value="科技外包" />
                <el-option label="运营管理" value="运营管理" />
                <el-option label="网络及数据安全" value="网络及数据安全" />
                <el-option label="战略规划" value="战略规划" />
                <el-option label="业务连续性管理" value="业务连续性管理" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件状态" prop="file_status">
              <el-select
                v-model="form.file_status"
                placeholder="请选择文件状态"
                style="width: 100%"
              >
                <el-option label="生效中" value="生效中" />
                <el-option label="已失效" value="已失效" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="上传人员" prop="uploader">
              <el-input
                v-model="form.uploader"
                placeholder="自动填充"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="附件" prop="attachment">
          <div
            class="attachment-upload-container"
            @paste="handlePaste"
            @dragover.prevent
            @drop.prevent="handleDrop"
            tabindex="0"
          >
            <el-upload
              ref="attachmentUploadRef"
              v-model:file-list="attachmentFileList"
              :http-request="handleAttachmentUpload"
              :before-upload="beforeAttachmentUpload"
              :on-remove="handleAttachmentRemove"
              :on-preview="handleAttachmentPreview"
              list-type="text"
              accept=".pdf,.doc,.docx"
              :limit="10"
              multiple
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持 PDF/DOC/DOCX 格式，大小不超过 50MB，最多上传10个文件
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { useUserStore } from '@/store/modules/user';
  import { createInst, updateInst } from '@/api/cmdb';
  import { uploadAttachment } from '@/api/upload';

  defineOptions({ name: 'RegulatoryLawsEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const userStore = useUserStore();

  /** 模型实例ID */
  const bkObjId = 'regulatory_laws';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 附件文件列表 */
  const attachmentFileList = ref([]);

  /** 表单数据 */
  const form = ref({
    file_name: '',
    issuing_authority: '',
    issue_time: '',
    effective_time: '',
    expiration_time: '',
    management_domain: '',
    file_status: '',
    uploader: '',
    attachment: []
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {
      file_name: [
        { required: true, message: '请输入文件名称', trigger: 'blur' }
      ],
      issuing_authority: [
        { required: true, message: '请选择发布机构', trigger: 'change' }
      ],
      issue_time: [
        { required: true, message: '请选择发布时间', trigger: 'change' }
      ],
      effective_time: [
        { required: true, message: '请选择生效时间', trigger: 'change' }
      ],
      expiration_time: [
        { required: true, message: '请选择失效时间', trigger: 'change' }
      ],
      management_domain: [
        { required: true, message: '请选择管理领域', trigger: 'change' }
      ],
      file_status: [
        { required: true, message: '请选择文件状态', trigger: 'change' }
      ],
      attachment: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (!value || (Array.isArray(value) && value.length === 0)) {
              callback(new Error('请上传附件'));
            } else {
              callback();
            }
          },
          trigger: 'blur'
        }
      ]
    };
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        // 将附件数据转换为JSON字符串格式
        data.attachment = JSON.stringify(
          attachmentFileList.value.map((file) => ({
            ossId: file.ossId || '',
            fileName: file.name,
            url: file.url
          }))
        );

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.file_name}_${data.issue_time}`
            }
          });
          EleMessage.success('修改成功');
        } else {
          // 设置uploader为当前登录用户
          data.uploader = userStore.info.nickName || userStore.info.userName;
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: `${data.file_name}_${data.issue_time}`
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    attachmentFileList.value = [];
  };

  /** 监听弹窗打开状态 */
  watch(
    () => visible.value,
    (newValue) => {
      if (newValue && !isUpdate.value) {
        // 新增模式，设置上传人员
        form.value.uploader =
          userStore.info.nickName || userStore.info.userName;
      }
    }
  );

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
        // 处理附件列表回显
        if (value.attachment) {
          try {
            let attachments = [];
            if (typeof value.attachment === 'string') {
              // 尝试解析JSON字符串
              try {
                attachments = JSON.parse(value.attachment);
              } catch {
                // 如果解析失败，按逗号分隔的URL字符串处理（兼容旧格式）
                attachments = value.attachment
                  .split(',')
                  .filter((url) => url.trim())
                  .map((url) => ({
                    url: url,
                    fileName: getFileNameFromUrl(url),
                    ossId: ''
                  }));
              }
            } else if (Array.isArray(value.attachment)) {
              // 已经是数组格式
              attachments = value.attachment;
            }

            attachmentFileList.value = attachments.map((item, index) => ({
              name: item.fileName || getFileNameFromUrl(item.url) || '附件文件',
              url: item.url,
              ossId: item.ossId || '',
              uid: Date.now() + index
            }));
          } catch (error) {
            console.error('解析附件数据失败:', error);
            attachmentFileList.value = [];
          }
        }
      } else {
        // 新增模式，重置表单
        Object.assign(form.value, {
          file_name: '',
          issuing_authority: '',
          issue_time: '',
          effective_time: '',
          expiration_time: '',
          management_domain: '',
          file_status: '',
          uploader: userStore.info.nickName || userStore.info.userName,
          attachment: []
        });
        attachmentFileList.value = [];
      }
    },
    { immediate: true }
  );

  /** 从URL获取文件名 */
  const getFileNameFromUrl = (url) => {
    if (!url) return '附件文件';
    const filename = url.split('/').pop() || '附件文件';
    return filename.includes('.') ? filename : '附件文件.pdf';
  };

  /** 处理粘贴事件 */
  const handlePaste = (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === 'file') {
        const file = item.getAsFile();
        if (file && isValidFileType(file)) {
          uploadPastedFile(file);
        }
        break;
      }
    }
  };

  /** 处理拖拽上传 */
  const handleDrop = (event) => {
    const files = event.dataTransfer?.files;
    if (!files || files.length === 0) return;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (isValidFileType(file)) {
        uploadPastedFile(file);
      }
    }
  };

  /** 检查文件类型是否有效 */
  const isValidFileType = (file) => {
    const validTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    const validExtensions = ['.pdf', '.doc', '.docx'];

    return (
      validTypes.includes(file.type) ||
      validExtensions.some((ext) => file.name.toLowerCase().endsWith(ext))
    );
  };

  /** 上传粘贴/拖拽的文件 */
  const uploadPastedFile = async (file) => {
    // 检查文件数量限制
    if (attachmentFileList.value.length >= 10) {
      EleMessage.error('最多只能上传10个文件');
      return;
    }

    // 校验文件
    if (!beforeAttachmentUpload(file)) {
      return;
    }

    // 添加到文件列表
    const fileItem = {
      name: file.name,
      raw: file,
      status: 'uploading',
      uid: Date.now() + Math.random()
    };

    attachmentFileList.value.push(fileItem);

    // 执行上传
    try {
      await handleAttachmentUpload({
        file: file,
        onSuccess: (response) => {
          const index = attachmentFileList.value.findIndex(
            (f) => f.uid === fileItem.uid
          );
          if (index !== -1) {
            attachmentFileList.value[index].status = 'success';
            attachmentFileList.value[index].url = response.data.url;
            attachmentFileList.value[index].ossId = response.data.ossId || '';

            // 更新表单数据并触发验证
            form.value.attachment = attachmentFileList.value.map((file) => ({
              ossId: file.ossId || '',
              fileName: file.name,
              url: file.url
            }));

            // 手动触发附件字段验证
            formRef.value?.validateField('attachment');
          }
        },
        onError: (error) => {
          const index = attachmentFileList.value.findIndex(
            (f) => f.uid === fileItem.uid
          );
          if (index !== -1) {
            attachmentFileList.value.splice(index, 1);
          }
          EleMessage.error('文件上传失败: ' + error.message);
        }
      });
    } catch (error) {
      const index = attachmentFileList.value.findIndex(
        (f) => f.uid === fileItem.uid
      );
      if (index !== -1) {
        attachmentFileList.value.splice(index, 1);
      }
      EleMessage.error('文件上传失败');
    }
  };

  /** 附件上传前校验 */
  const beforeAttachmentUpload = (file) => {
    const validTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    const validExtensions = ['.pdf', '.doc', '.docx'];

    const isValidType =
      validTypes.includes(file.type) ||
      validExtensions.some((ext) => file.name.toLowerCase().endsWith(ext));
    const isLt50M = file.size / 1024 / 1024 < 50;

    if (!isValidType) {
      EleMessage.error('只能上传 PDF/DOC/DOCX 格式文件!');
      return false;
    }
    if (!isLt50M) {
      EleMessage.error('上传文件大小不能超过 50MB!');
      return false;
    }
    return true;
  };

  /** 自定义附件上传 */
  const handleAttachmentUpload = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', bkObjId);
      const response = await uploadAttachment(formData);

      if (response.code === 200) {
        onSuccess(response, file);

        // 更新文件列表中当前文件的信息
        const fileIndex = attachmentFileList.value.findIndex(
          (f) => f.uid === file.uid
        );
        if (fileIndex !== -1) {
          attachmentFileList.value[fileIndex].url = response.data.url;
          attachmentFileList.value[fileIndex].ossId = response.data.ossId || '';
        }

        // 更新表单数据并触发验证
        form.value.attachment = attachmentFileList.value.map((file) => ({
          ossId: file.ossId || '',
          fileName: file.name,
          url: file.url
        }));

        // 手动触发附件字段验证
        formRef.value?.validateField('attachment');

        EleMessage.success('附件上传成功');
      } else {
        onError(new Error(response.msg || '上传失败'));
      }
    } catch (error) {
      onError(error);
      EleMessage.error('附件上传失败: ' + error.message);
    }
  };

  /** 附件删除 */
  const handleAttachmentRemove = (file, fileList) => {
    attachmentFileList.value = fileList;

    // 更新表单数据并触发验证
    form.value.attachment = fileList.map((file) => ({
      ossId: file.ossId || '',
      fileName: file.name,
      url: file.url
    }));

    // 手动触发附件字段验证
    formRef.value?.validateField('attachment');
  };

  /** 附件预览 */
  const handleAttachmentPreview = (file) => {
    const url = file.url || file.response?.data?.url || '';
    if (url) {
      window.open(url, '_blank');
    }
  };
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .attachment-upload-container {
    position: relative;
    border: 2px dashed transparent;
    border-radius: 6px;
    transition: all 0.3s ease;
    outline: none;
    padding: 10px;
  }

  .attachment-upload-container:focus {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .attachment-upload-container:hover {
    border-color: var(--el-color-primary-light-3);
  }
</style>
