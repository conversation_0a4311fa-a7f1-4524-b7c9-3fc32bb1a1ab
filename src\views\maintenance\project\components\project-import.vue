<template>
  <ele-modal
    :width="460"
    title="批量导入"
    :body-style="{ paddingTop: '8px' }"
    v-model="visible"
  >
    <div v-loading="importLoading" class="project-import-upload">
      <el-upload
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        accept=".xlsx,.xls"
        :show-upload-list="false"
        :before-upload="beforeUpload"
      >
        <ele-text
          type="primary"
          :icon="CloudUploadOutlined"
          :icon-props="{ size: 52 }"
          style="margin-bottom: 10px"
        />
        <ele-text type="placeholder">将文件拖到此处, 或点击上传</ele-text>
      </el-upload>
    </div>
    <div style="display: flex; align-items: center">
      <ele-text size="sm" type="secondary" style="line-height: 17px; flex: 1">
        <span style="padding-right: 8px">只能上传 xls、xlsx 文件，</span>
        <el-link
          type="primary"
          underline="never"
          style="font-size: inherit; line-height: inherit; vertical-align: 0"
          @click="downloadTemplate"
        >
          下载模板
        </el-link>
      </ele-text>
    </div>
  </ele-modal>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { CloudUploadOutlined } from '@/components/icons';
  import { importTemplate, importExcel } from '@/api/cmdb/index';

  defineOptions({ name: 'ProjectImport' });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 模型实例ID */
  const bkObjId = 'project_detail';

  /** 导入请求状态 */
  const importLoading = ref(false);

  /** 文件选择处理 */
  const handleFileChange = async (file) => {
    if (!beforeUpload(file.raw)) return;

    try {
      const formData = new FormData();
      formData.append('bkObjId', bkObjId);
      formData.append('file', file.raw);

      const response = await importExcel(formData);
      handleImportSuccess(response);
    } catch (error) {
      handleImportError(error);
    }
  };

  /** 下载模板 */
  const downloadTemplate = async () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    try {
      const formData = new FormData();
      formData.append('bkObjId', bkObjId);

      const blob = await importTemplate(formData);

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '项目管理导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      loading.close();
    } catch (error) {
      loading.close();
      EleMessage.error({
        message: error.message || '模板下载失败',
        plain: true
      });
    }
  };

  /** 上传前检查 */
  const beforeUpload = (file) => {
    const isExcel =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      EleMessage.error({ message: '只能选择 excel 文件', plain: true });
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      EleMessage.error({ message: '大小不能超过 10MB', plain: true });
      return false;
    }

    importLoading.value = true;
    return true;
  };

  /** 导入成功回调 */
  const handleImportSuccess = (response) => {
    importLoading.value = false;
    if (response.code === 200) {
      const results = response.data || [];
      const successResults = results.filter((item) =>
        item.includes('创建实例成功')
      );
      const failResults = results.filter((item) =>
        item.includes('创建实例失败')
      );

      let message = '';
      if (successResults.length > 0) {
        message += `成功导入 ${successResults.length} 条数据`;
      }
      if (failResults.length > 0) {
        if (message) message += '\n\n失败信息：\n';
        else message = '导入失败：\n';
        message += failResults.join('\n');
      }
      if (!message) {
        message = '导入完成，但无详细结果信息';
      }

      ElMessageBox({
        type: failResults.length === 0 ? 'success' : 'warning',
        title: '导入结果',
        dangerouslyUseHTMLString: true,
        message: message.replace(/\n/g, '<br>'),
        customStyle: { maxWidth: '442px' },
        draggable: true
      })
        .then(() => {})
        .catch(() => {});
      visible.value = false;
      emit('done');
    } else {
      ElMessageBox({
        type: 'error',
        title: '导入结果',
        message: response.msg || response.message || '导入过程中发生错误',
        customStyle: { maxWidth: '442px' },
        draggable: true
      })
        .then(() => {})
        .catch(() => {});
    }
  };

  /** 导入失败回调 */
  const handleImportError = (error) => {
    importLoading.value = false;
    ElMessageBox({
      type: 'error',
      title: '导入结果',
      message: error.message || '网络错误或服务器异常，请稍后重试',
      customStyle: { maxWidth: '442px' },
      draggable: true
    })
      .then(() => {})
      .catch(() => {});
  };
</script>

<style lang="scss" scoped>
  .project-import-upload {
    margin-bottom: 12px;

    :deep(.el-upload > .el-upload-dragger) {
      padding: 0;
      height: 168px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: (border-color 0.2s, background-color 0.2s);

      &:not(.is-dragover) {
        background: var(--el-fill-color-light);
      }
    }

    :deep(.el-upload-list) {
      display: none;
    }

    :deep(.el-icon > svg) {
      stroke-width: 3;
    }
  }
</style>
