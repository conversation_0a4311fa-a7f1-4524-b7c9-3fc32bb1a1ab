<template>
  <ele-page>
    <!-- 系统概览卡片 -->
    <div class="mb-8">
      <system-overview @system-changed="handleSystemChange" />
    </div>

    <!-- 主要内容网格 -->
    <el-row :gutter="24">
      <!-- 左侧列 -->
      <el-col :lg="16" :md="24">
        <div class="space-y-6">
          <!-- 技术栈信息 -->
          <tech-stack :system-data="currentSystemData" />

          <!-- 部署方式 -->
          <deployment-info :system-data="currentSystemData" />

          <!-- 资源使用情况 -->
          <resource-usage :system-data="currentSystemData" />

          <!-- 监控信息 -->
          <monitoring-info :system-data="currentSystemData" />

          <!-- 系统事件记录 -->
          <event-record :system-data="currentSystemData" />

          <!-- 系统变更记录 -->
          <change-record :system-data="currentSystemData" />
        </div>
      </el-col>

      <!-- 右侧列 -->
      <el-col :lg="8" :md="24">
        <div class="space-y-6">
          <!-- 业务流程图 -->
          <business-flow-chart :system-data="currentSystemData" />

          <!-- 系统架构图 -->
          <system-architecture :system-data="currentSystemData" />

          <!-- 维保信息 -->
          <maintenance-info :system-data="currentSystemData" />

          <!-- 系统文档 -->
          <system-docs :system-data="currentSystemData" />

          <!-- 运维工单统计 -->
          <work-order-stats :system-data="currentSystemData" />
        </div>
      </el-col>
    </el-row>
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import SystemOverview from './components/system-overview.vue';
  import TechStack from './components/tech-stack.vue';
  import DeploymentInfo from './components/deployment.vue';
  import ResourceUsage from './components/resource-usage.vue';
  import MonitoringInfo from './components/monitoring-info.vue';
  import EventRecord from './components/event-record.vue';
  import ChangeRecord from './components/change-record.vue';
  import BusinessFlowChart from './components/business-flow-chart.vue';
  import SystemArchitecture from './components/system-architecture.vue';
  import MaintenanceInfo from './components/maintenance.vue';
  import SystemDocs from './components/system-docs.vue';
  import WorkOrderStats from './components/work-order.vue';

  defineOptions({ name: 'SystemDashboard' });

  // 当前选中的系统数据
  const currentSystemData = ref({});

  // 处理系统切换事件
  const handleSystemChange = (systemData) => {
    currentSystemData.value = systemData;
    console.log('系统已切换:', systemData);

    // 这里可以根据需要通知其他子组件系统已切换
    // 例如：刷新相关的监控数据、事件记录等
    refreshRelatedData(systemData);
  };

  // 刷新相关数据（可以在这里调用其他组件的刷新方法）
  const refreshRelatedData = (systemData) => {
    // 根据新选择的系统，可以刷新相关组件的数据
    // 例如：
    // - 监控信息可以根据系统ID获取对应的监控数据
    // - 事件记录可以筛选该系统的事件
    // - 变更记录可以显示该系统的变更历史
    console.log(
      '正在为系统刷新相关数据:',
      systemData.businessName || systemData.name
    );
  };
</script>

<style scoped>
  .space-y-6 > :not(:last-child) {
    margin-bottom: 1.5rem;
  }

  /* 全局卡片样式 */
  :deep(.el-card) {
    border-radius: 1rem;
    margin-top: 10px;
  }

  /* 全局标签间隔样式 */
  :deep(.el-tag) {
    margin-right: 5px;
    margin-bottom: 5px;
  }

  /* 标签容器间隔 */
  :deep(.el-tag + .el-tag) {
    margin-left: 5px;
  }
</style>
