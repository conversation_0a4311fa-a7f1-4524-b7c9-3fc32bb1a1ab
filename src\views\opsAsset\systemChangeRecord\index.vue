<template>
  <ele-page>
    <!-- 搜索组件 -->
    <simple-search
      :fields="searchFields"
      :default-field="defaultField"
      :advanced-conditions="advancedConditions"
      @search="handleSimpleSearch"
      @reset="handleSearchReset"
      @advanced-search="showAdvancedModal"
      @clear-advanced="clearAdvancedConditions"
      @remove-advanced-condition="removeAdvancedCondition"
    />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <cmdb-pro-table
        ref="tableRef"
        row-key="bk_inst_id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '系统变更记录' }"
        cache-key="systemChangeRecordTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            @click="showOriginalData"
          >
            原数据
          </el-button>
          <el-button type="warning" class="ele-btn-icon" @click="showSplitData">
            拆分数据
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="openDetail(row)">
            详情
          </el-link>
        </template>
      </cmdb-pro-table>
    </ele-card>

    <!-- 详情弹窗 -->
    <system-change-record-detail :data="currentDetail" v-model="showDetail" />

    <!-- 高级搜索弹窗 -->
    <advanced-search
      v-model="showAdvanced"
      :search-fields="searchFields"
      :initial-conditions="advancedConditions"
      @search="handleAdvancedSearch"
      @close="closeAdvancedModal"
    />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { searchInst } from '@/api/cmdb';
  import SimpleSearch from '@/components/Search/simple-search.vue';
  import AdvancedSearch from '@/components/Search/advanced-search.vue';
  import SystemChangeRecordDetail from './components/system-change-record-detail.vue';

  defineOptions({ name: 'SystemChangeRecord' });

  /** 模型实例ID */
  const bkObjId = 'system_modify';
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格选中数据 */
  const selections = ref([]);
  /** 当前详情数据 */
  const currentDetail = ref(null);
  /** 是否显示详情弹窗 */
  const showDetail = ref(false);
  /** 是否显示高级搜索弹窗 */
  const showAdvanced = ref(false);

  /** 拆分后的数据缓存 */
  const splitDataCache = ref([]);

  /** 当前是否显示拆分数据 */
  const isShowingSplitData = ref(false);

  /** 表格列配置 */
  const columns = computed(() => [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'change_month',
      label: '变更月份',
      align: 'center',
      width: 100
    },
    {
      prop: 'process_code',
      label: '流程编号',
      align: 'center',
      minWidth: 150,
      fixed: 'left'
    },
    {
      prop: 'process_name',
      label: '流程标题',
      align: 'center',
      minWidth: 200
    },
    {
      prop: 'apply_real_name',
      label: '申请人',
      align: 'center',
      width: 100
    },
    {
      prop: 'apply_user_code',
      label: '申请人编号',
      align: 'center',
      width: 120
    },
    {
      prop: 'technology_review_user',
      label: '技术复核人',
      align: 'center',
      width: 120
    },
    {
      prop: 'apply_date',
      label: '申请日期',
      align: 'center',
      width: 120,
      formatter: (row) => formatTime(row.apply_date)
    },
    {
      prop: 'system_level',
      label: '应用系统级别',
      align: 'center',
      width: 120
    },
    {
      prop: 'online_type',
      label: '系统变更类型',
      align: 'center',
      width: 120
    },
    {
      prop: 'change_system',
      label: '本次变更系统',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'multiple_system_change',
      label: '是否多系统变更',
      align: 'center',
      width: 140
    },
    {
      prop: 'source_system',
      label: '变更所属来源系统',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'urgency',
      label: '紧急程度',
      align: 'center',
      width: 100
    },
    {
      prop: 'process_status',
      label: '流程状态',
      align: 'center',
      width: 100
    },
    {
      prop: 'online_style',
      label: '变更性质',
      align: 'center',
      width: 100
    },
    {
      prop: 'requirement_code',
      label: '需求编号',
      align: 'center',
      width: 120
    },
    {
      prop: 'online_start',
      label: '变更实施开始时间',
      align: 'center',
      width: 160,
      formatter: (row) => formatTime(row.online_start)
    },
    {
      prop: 'online_end',
      label: '变更实施结束时间',
      align: 'center',
      width: 160,
      formatter: (row) => formatTime(row.online_end)
    },
    {
      prop: 'complete_date',
      label: '归档日期',
      align: 'center',
      width: 120,
      formatter: (row) => formatTime(row.complete_date)
    },
    {
      prop: 'change_operate_user',
      label: '变更实施操作人',
      align: 'center',
      width: 120
    },
    {
      prop: 'online_result',
      label: '实施结果记录',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'business_verify',
      label: '业务检核确认',
      align: 'center',
      width: 120
    },
    {
      prop: 'update_time',
      label: '最近更新时间',
      align: 'center',
      width: 140,
      formatter: (row) => formatTime(row.update_time)
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 80,
      align: 'center',
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 搜索字段配置 */
  const searchFields = ref([
    { prop: 'process_code', label: '流程编号', type: 'text' },
    { prop: 'process_name', label: '流程标题', type: 'text' },
    { prop: 'apply_real_name', label: '申请人', type: 'text' },
    { prop: 'apply_user_code', label: '申请人编号', type: 'text' },
    { prop: 'technology_review_user', label: '技术复核人', type: 'text' },
    { prop: 'system_level', label: '应用系统级别', type: 'text' },
    { prop: 'online_type', label: '系统变更类型', type: 'text' },
    { prop: 'change_system', label: '本次变更系统', type: 'text' },
    { prop: 'multiple_system_change', label: '是否多系统变更', type: 'text' },
    { prop: 'source_system', label: '变更所属来源系统', type: 'text' },
    { prop: 'urgency', label: '紧急程度', type: 'text' },
    { prop: 'process_status', label: '流程状态', type: 'text' },
    { prop: 'online_style', label: '变更性质', type: 'text' },
    { prop: 'requirement_code', label: '需求编号', type: 'text' },
    { prop: 'change_operate_user', label: '变更实施操作人', type: 'text' },
    { prop: 'business_verify', label: '业务检核确认', type: 'text' },
    { prop: 'change_month', label: '变更月份', type: 'text' },
    { prop: 'apply_date', label: '申请日期', type: 'date' },
    { prop: 'online_start', label: '变更实施开始时间', type: 'date' },
    { prop: 'online_end', label: '变更实施结束时间', type: 'date' },
    { prop: 'complete_date', label: '归档日期', type: 'date' },
    { prop: 'update_time', label: '最近更新时间', type: 'date' }
  ]);

  /** 默认搜索字段 */
  const defaultField = ref({
    prop: 'process_code',
    label: '流程编号'
  });

  /** 高级搜索条件 */
  const advancedConditions = ref([]);

  /** 当前搜索条件 */
  const currentSearchParams = ref({});

  /** 数据源 */
  const datasource = async ({ pages }) => {
    // 如果是拆分数据模式，直接使用缓存
    if (isShowingSplitData.value && splitDataCache.value.length > 0) {
      const startIndex = (pages.pageNum - 1) * pages.pageSize;
      const endIndex = startIndex + pages.pageSize;
      const pagedData = splitDataCache.value.slice(startIndex, endIndex);

      return {
        code: 200,
        msg: 'success',
        rows: pagedData,
        total: splitDataCache.value.length
      };
    }

    // 原数据模式：正常从API获取数据
    let conditions = [];

    // 处理简单搜索条件
    if (currentSearchParams.value.condition) {
      Object.keys(currentSearchParams.value.condition).forEach((field) => {
        const value = currentSearchParams.value.condition[field];
        if (typeof value === 'object' && value !== null) {
          // 处理操作符对象格式，例如 { $regex: "keyword" }
          Object.keys(value).forEach((operator) => {
            conditions.push({
              field: field,
              operator: operator,
              value: value[operator]
            });
          });
        } else {
          // 处理直接值格式
          conditions.push({
            field: field,
            operator: '$eq',
            value: value
          });
        }
      });
    }

    // 添加高级搜索条件（已经是正确格式）
    if (advancedConditions.value.length > 0) {
      conditions = conditions.concat(
        advancedConditions.value.filter(
          (condition) =>
            condition.field &&
            condition.operator &&
            condition.value !== '' &&
            condition.value !== null &&
            condition.value !== undefined
        )
      );
    }

    const page = {
      start: (pages.pageNum - 1) * pages.pageSize,
      limit: pages.pageSize,
      sort: 'bk_inst_id'
    };

    // 按照新的参考格式组织条件参数
    const conditionParam =
      conditions.length > 0
        ? {
            [bkObjId]: conditions
          }
        : undefined;

    const res = await searchInst({
      bk_obj_id: bkObjId,
      condition: conditionParam,
      page
    });

    return {
      code: res.code,
      msg: res.msg,
      rows: res.rows,
      total: res.total
    };
  };

  /** 处理简单搜索 */
  const handleSimpleSearch = (params) => {
    currentSearchParams.value = params;
    reload();
  };

  /** 处理搜索重置 */
  const handleSearchReset = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    reload();
  };

  /** 显示高级搜索弹窗 */
  const showAdvancedModal = () => {
    showAdvanced.value = true;
  };

  /** 关闭高级搜索弹窗 */
  const closeAdvancedModal = () => {
    showAdvanced.value = false;
  };

  /** 处理高级搜索 */
  const handleAdvancedSearch = (conditions) => {
    advancedConditions.value = conditions;
    reload();
  };

  /** 清空高级搜索条件 */
  const clearAdvancedConditions = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    selections.value = [];
    reload();
  };

  /** 移除单个高级搜索条件 */
  const removeAdvancedCondition = (condition) => {
    const index = advancedConditions.value.findIndex(
      (c) =>
        c.field === condition.field &&
        c.operator === condition.operator &&
        c.value === condition.value
    );
    if (index > -1) {
      advancedConditions.value.splice(index, 1);
      selections.value = [];
      reload();
    }
  };

  /** 打开详情弹窗 */
  const openDetail = (row) => {
    currentDetail.value = row;
    showDetail.value = true;
  };

  /** 显示原数据 */
  const showOriginalData = () => {
    isShowingSplitData.value = false;
    splitDataCache.value = [];
    reload();
  };

  /** 显示拆分数据 */
  const showSplitData = async () => {
    const loading = EleMessage.loading('正在获取全部数据并进行拆分，请稍候...');

    try {
      console.log('开始获取数据，使用参数:', {
        bk_obj_id: bkObjId,
        page: {
          start: 0,
          limit: 10000,
          sort: 'bk_inst_id'
        }
      });

      // 获取所有数据（不分页，获取全部）
      const res = await searchInst({
        bk_obj_id: bkObjId,
        page: {
          start: 0,
          limit: 10000, // 获取足够多的数据
          sort: 'bk_inst_id'
        }
      });

      console.log('API响应完整内容:', res); // 调试日志
      console.log('响应数据类型:', typeof res);
      console.log('响应code:', res?.code);
      console.log('响应rows数量:', res?.rows?.length);

      if (res.code === 200 && res.rows) {
        // 将所有数据按"变更来源所属系统"拆分
        const splitData = [];

        res.rows.forEach((record) => {
          const { source_system } = record;

          if (!source_system || typeof source_system !== 'string') {
            // 如果没有系统信息，保留原记录
            splitData.push(record);
            return;
          }

          // 按逗号分割系统名称
          const systems = source_system
            .split(',')
            .map((system) => system.trim())
            .filter((system) => system.length > 0);

          if (systems.length <= 1) {
            // 只有一个系统或没有系统，保留原记录
            splitData.push(record);
          } else {
            // 多个系统，为每个系统创建一条记录
            systems.forEach((system, index) => {
              splitData.push({
                ...record,
                source_system: system,
                bk_inst_id: `${record.bk_inst_id}_split_${index}`,
                bk_inst_name: `${record.process_name || '系统变更'}_${system}`
              });
            });
          }
        });

        // 缓存拆分数据
        splitDataCache.value = splitData;
        isShowingSplitData.value = true;

        loading.close();
        EleMessage.success(
          `拆分完成！原始数据 ${res.rows.length} 条，拆分后 ${splitData.length} 条`
        );
        reload();
      } else {
        loading.close();
        console.error('API返回错误详情:', {
          code: res?.code,
          msg: res?.msg,
          data: res?.data,
          rows: res?.rows,
          total: res?.total,
          fullResponse: res
        });
        EleMessage.error(
          `获取数据失败：${res?.msg || '未知错误'} (状态码: ${res?.code || 'undefined'})`
        );
      }
    } catch (e) {
      loading.close();
      console.error('请求异常详情:', {
        name: e.name,
        message: e.message,
        stack: e.stack,
        response: e.response,
        request: e.request
      });
      EleMessage.error(`获取数据失败：${e.message || '请求异常'}`);
    }
  };

  /** 刷新表格 */
  const reload = (params) => {
    tableRef.value?.reload?.(params);
  };

  /** 格式化时间 */
  const formatTime = (timeValue) => {
    if (!timeValue) return '-';
    try {
      const date = new Date(timeValue);
      if (isNaN(date.getTime())) return '-';
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return '-';
    }
  };
</script>

<style scoped>
  /* 工单管理样式 */
</style>
