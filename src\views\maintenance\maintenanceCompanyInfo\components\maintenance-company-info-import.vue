<template>
  <el-drawer
    v-model="visible"
    title="导入维保厂家"
    :size="600"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="import-container">
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        show-icon
        class="import-tips"
      >
        <div>
          <p>1. 请下载模板文件，按照模板格式填写数据</p>
          <p>2. 支持的文件格式：.xlsx、.xls</p>
          <p>3. 文件大小不超过 10MB</p>
          <p>4. 必填字段：项目名称、维保厂家名称</p>
        </div>
      </el-alert>

      <div class="template-download">
        <el-button type="primary" :icon="DownloadOutlined" @click="downloadTemplate">
          下载模板
        </el-button>
      </div>

      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        action=""
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :file-list="fileList"
        accept=".xlsx,.xls,.csv"
        :limit="1"
        :on-exceed="handleExceed"
      >
        <el-icon class="el-icon--upload">
          <UploadOutlined />
        </el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 .xlsx/.xls 文件，且不超过 10MB
          </div>
        </template>
      </el-upload>

      <div v-if="importResult" class="import-result">
        <el-alert
          :title="importResult.title"
          :type="importResult.type"
          :closable="false"
          show-icon
        >
          <div v-html="importResult.message"></div>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          :loading="importing"
          :disabled="!fileList.length"
          @click="handleImport"
        >
          开始导入
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { UploadOutlined, DownloadOutlined } from '@/components/icons';
  import { importTemplate, importExcel } from '@/api/cmdb/index';

  defineOptions({ name: 'MaintenanceCompanyInfoImport' });

  const props = defineProps({
    modelValue: Boolean
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'maintenance_company_info';

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 上传组件实例 */
  const uploadRef = ref(null);

  /** 文件列表 */
  const fileList = ref([]);

  /** 导入状态 */
  const importing = ref(false);

  /** 导入结果 */
  const importResult = ref(null);

  /** 文件变化处理 */
  const handleFileChange = (file, files) => {
    // 验证文件大小
    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过 10MB');
      files.splice(files.indexOf(file), 1);
      return false;
    }

    // 验证文件类型
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    if (!validTypes.includes(file.raw.type)) {
      ElMessage.error('只支持 .xlsx 和 .xls 格式的文件');
      files.splice(files.indexOf(file), 1);
      return false;
    }

    fileList.value = files;
    importResult.value = null;
  };

  /** 文件移除处理 */
  const handleFileRemove = () => {
    fileList.value = [];
    importResult.value = null;
  };

  /** 文件数量超出限制处理 */
  const handleExceed = () => {
    ElMessage.warning('只能上传一个文件，请移除现有文件后重新上传');
  };

  /** 下载模板 */
  const downloadTemplate = async () => {
    try {
      const loading = EleMessage.loading('正在下载模板...');
      const res = await importTemplate({
        bkObjId
      });

      if (res) {
        // 创建下载链接
        const blob = new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '维保厂家管理导入模板.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);

        EleMessage.success('模板下载成功');
      }
      loading.close();
    } catch (e) {
      EleMessage.error(e.message || '模板下载失败');
    }
  };

  /** 开始导入 */
  const handleImport = async () => {
    if (!fileList.value.length) {
      EleMessage.error('请先选择要导入的文件');
      return;
    }

    const confirmResult = await ElMessageBox.confirm(
      '确定要导入选中的文件吗？导入过程不可撤销。',
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => false);

    if (!confirmResult) return;

    importing.value = true;
    importResult.value = null;

    try {
      const formData = new FormData();
      formData.append('file', fileList.value[0].raw);
      formData.append('bkObjId', bkObjId);

      const res = await importExcel(formData);

      if (res.code === 200) {
        importResult.value = {
          type: 'success',
          title: '导入成功',
          message: '<p>数据导入成功</p>'
        };
        EleMessage.success('导入成功');
        emit('done');
        // 延迟关闭抽屉
        setTimeout(() => {
          visible.value = false;
        }, 1500);
      } else {
        importResult.value = {
          type: 'error',
          title: '导入失败',
          message: `<p>${res.msg || '导入失败'}</p>`
        };
        EleMessage.error(res.msg || '导入失败');
      }
    } catch (e) {
      importResult.value = {
        type: 'error',
        title: '导入失败',
        message: `<p>${e.message || '导入失败'}</p>`
      };
      EleMessage.error(e.message || '导入失败');
    } finally {
      importing.value = false;
    }
  };

  /** 关闭抽屉 */
  const handleClose = () => {
    visible.value = false;
    // 清空状态
    fileList.value = [];
    importResult.value = null;
    uploadRef.value?.clearFiles();
  };
</script>

<style scoped>
  .import-container {
    padding: 0 8px;
  }

  .import-tips {
    margin-bottom: 20px;
  }

  .import-tips p {
    margin: 4px 0;
    line-height: 1.6;
  }

  .template-download {
    margin-bottom: 24px;
    text-align: center;
  }

  .upload-demo {
    margin-bottom: 20px;
  }

  .import-result {
    margin-top: 20px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0;
  }

  :deep(.el-upload-dragger) {
    border: 2px dashed var(--el-border-color);
    border-radius: 6px;
    background-color: var(--el-fill-color-blank);
    text-align: center;
    padding: 40px 20px;
    transition: border-color 0.3s;
  }

  :deep(.el-upload-dragger:hover) {
    border-color: var(--el-color-primary);
  }

  :deep(.el-icon--upload) {
    font-size: 48px;
    color: var(--el-text-color-placeholder);
    margin-bottom: 16px;
  }

  :deep(.el-upload__text) {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  :deep(.el-upload__text em) {
    color: var(--el-color-primary);
    font-style: normal;
  }

  :deep(.el-upload__tip) {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    margin-top: 8px;
  }
</style>