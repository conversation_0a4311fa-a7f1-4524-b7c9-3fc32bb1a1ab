<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑维保人员"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-alert
        title="批量编辑说明"
        type="info"
        :closable="false"
        show-icon
        class="batch-edit-tips"
      >
        <div>
          <p>只有勾选的字段才会被更新，未勾选的字段保持原值不变</p>
          <p>当前选中了 {{ selectedRecords.length }} 条记录</p>
        </div>
      </el-alert>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="maintenance_staff_level">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.maintenance_staff_level" />
                    <span class="label-text">人员级别</span>
                  </div>
                </template>
                <el-input
                  v-model="form.maintenance_staff_level"
                  placeholder="请输入人员级别"
                  :disabled="!fieldEnabled.maintenance_staff_level"
                  clearable
                  :maxlength="50"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="company_name">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.company_name" />
                    <span class="label-text">所属厂家</span>
                  </div>
                </template>
                <el-input
                  v-model="form.company_name"
                  placeholder="请输入所属厂家"
                  :disabled="!fieldEnabled.company_name"
                  clearable
                  :maxlength="200"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <!-- 评价信息 -->
        <div class="form-section-title">评价信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="first_quarter_rating">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.first_quarter_rating" />
                    <span class="label-text">一季度评价得分</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.first_quarter_rating"
                  placeholder="请输入一季度评价得分"
                  :disabled="!fieldEnabled.first_quarter_rating"
                  type="number"
                  min="0"
                  max="100"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="second_quarter_rating">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.second_quarter_rating" />
                    <span class="label-text">二季度评价得分</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.second_quarter_rating"
                  placeholder="请输入二季度评价得分"
                  :disabled="!fieldEnabled.second_quarter_rating"
                  type="number"
                  min="0"
                  max="100"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="third_quarter_rating">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.third_quarter_rating" />
                    <span class="label-text">三季度评价得分</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.third_quarter_rating"
                  placeholder="请输入三季度评价得分"
                  :disabled="!fieldEnabled.third_quarter_rating"
                  type="number"
                  min="0"
                  max="100"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="yearly_rating">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.yearly_rating" />
                    <span class="label-text">年度评价得分</span>
                  </div>
                </template>
                <el-input
                  v-model.number="form.yearly_rating"
                  placeholder="请输入年度评价得分"
                  :disabled="!fieldEnabled.yearly_rating"
                  type="number"
                  min="0"
                  max="100"
                  clearable
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        批量保存
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst } from '@/api/cmdb';

  defineOptions({ name: 'MaintenanceStaffInfoBatchEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 选中的记录 */
    selectedRecords: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits(['update:modelValue', 'update:selectedRecords', 'done']);

  /** 模型实例ID */
  const bkObjId = 'maintenance_staff_info';

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 字段启用状态 */
  const fieldEnabled = ref({
    maintenance_staff_level: false,
    company_name: false,
    first_quarter_rating: false,
    second_quarter_rating: false,
    third_quarter_rating: false,
    yearly_rating: false
  });

  /** 表单数据 */
  const form = ref({
    maintenance_staff_level: '',
    company_name: '',
    first_quarter_rating: null,
    second_quarter_rating: null,
    third_quarter_rating: null,
    yearly_rating: null
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {};
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = async () => {
    if (!props.selectedRecords.length) {
      EleMessage.error('没有选中的记录');
      return;
    }

    // 检查是否有启用的字段
    const enabledFields = Object.keys(fieldEnabled.value).filter(
      (key) => fieldEnabled.value[key]
    );

    if (enabledFields.length === 0) {
      EleMessage.error('请至少选择一个要更新的字段');
      return;
    }

    const confirmResult = await ElMessageBox.confirm(
      `确定要批量更新选中的 ${props.selectedRecords.length} 条维保人员记录吗？`,
      '确认批量更新',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => false);

    if (!confirmResult) return;

    loading.value = true;
    try {
      // 构建更新数据，只包含启用的字段
      const updateData = {};
      enabledFields.forEach((field) => {
        updateData[field] = form.value[field];
      });

      const instIds = props.selectedRecords.map((item) => item.bk_inst_id);

      await batchUpdateInst({
        bkObjId,
        instIds: instIds,
        instInfoMap: updateData
      });

      EleMessage.success('批量更新成功');
      updateVisible(false);
      emit('done');
    } catch (e) {
      EleMessage.error(e.message || '批量更新失败');
    }
    loading.value = false;
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    // 重置字段启用状态
    Object.keys(fieldEnabled.value).forEach((key) => {
      fieldEnabled.value[key] = false;
    });
    // 重置表单数据
    Object.assign(form.value, {
      maintenance_staff_level: '',
      company_name: '',
      first_quarter_rating: null,
      second_quarter_rating: null,
      third_quarter_rating: null,
      yearly_rating: null
    });
  };

  /** 监听选中记录变化 */
  watch(
    () => props.selectedRecords,
    (newVal) => {
      if (!newVal || newVal.length === 0) {
        resetFields();
      }
    }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .batch-edit-tips {
    margin-bottom: 20px;
  }

  .batch-edit-tips p {
    margin: 4px 0;
    line-height: 1.6;
  }

  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }

  .field-with-checkbox {
    width: 100%;
  }

  .form-label-with-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .label-text {
    white-space: nowrap;
  }

  :deep(.el-form-item__label) {
    padding: 0 !important;
  }
</style>