<template>
  <ele-drawer
    v-model="visible"
    :size="1000"
    title="系统事件详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="event-detail" v-if="data">
      <el-tabs v-model="activeTab">
        <!-- 事件详情 Tab -->
        <el-tab-pane label="事件详情" name="detail">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="事件标题" :span="2">
                {{ data.event_name || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="响应/记录人">
                {{ data.recorder || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="发生时间">
                {{ data.occurrence_time || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件来源">
                {{ data.event_source || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件级别">
                <el-tag
                  v-if="data.event_level"
                  :type="getLevelType(data.event_level)"
                  size="small"
                >
                  {{ data.event_level }}
                </el-tag>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="影响范围">
                {{ data.impact_scope || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="影响业务">
                {{ data.affected_business || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件现象描述" :span="2">
                {{ data.event_desc || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件现象截图" :span="2">
                <div
                  v-if="hasAttachments(data.event_screenshot)"
                  class="image-list"
                >
                  <el-popover placement="bottom" width="auto" trigger="click">
                    <template #reference>
                      <div class="image-item">
                        <el-icon class="image-icon">
                          <Picture />
                        </el-icon>
                        <span
                          v-if="getAttachmentCount(data.event_screenshot) > 1"
                          class="image-count-text"
                        >
                          【{{ getAttachmentCount(data.event_screenshot) }}】
                        </span>
                      </div>
                    </template>
                    <div class="image-gallery">
                      <div
                        v-for="(url, index) in getImageUrls(
                          data.event_screenshot
                        )"
                        :key="index"
                        class="gallery-item"
                      >
                        <img
                          :src="url"
                          :alt="`事件截图${index + 1}`"
                          class="gallery-thumbnail"
                          @click="openImagePreview(url)"
                        />
                      </div>
                    </div>
                  </el-popover>
                </div>
                <span v-else>-</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 故障信息 -->
          <div class="detail-section">
            <h3 class="section-title">故障信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="故障类别">
                {{ getFaultCategoryLabel(data.fault_category) || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="根因故障系统">
                {{ data.root_cause_fault_system || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="系统级别">
                {{ data.system_level || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件原因" :span="2">
                {{ data.event_cause || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 变更信息 (仅当故障类别为"上线/变更引发"时显示) -->
          <div class="detail-section">
            <h3 class="section-title">处置信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="科创处置人员">
                {{ data.dev_handler || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="运维处置人员">
                {{ data.ops_handler || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件处理过程" :span="2">
                {{ data.event_handling_process || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件处理结果和依据" :span="2">
                {{ data.event_handling_results_and_basis || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="解决时间">
                {{ data.resolution_time || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="解决方案是否涉及生产变更">
                {{
                  data.solution_and_whether_production_change_is_involved || '-'
                }}
              </el-descriptions-item>
              <el-descriptions-item label="解决方案和是否涉及OA流程">
                {{ data.solution_and_whether_oa_process_is_involved || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="引发故障的变更单号">
                {{ data.change_order_no_causing_fault || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="变更申请人">
                {{ data.change_requester || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="开发供应商" :span="2">
                {{ data.development_supplier || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="是否进行复盘">
                {{ data.whether_review_is_conducted || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="复盘会召开日期">
                {{ data.review_meeting_date || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="是否形成事件报告">
                {{ data.whether_event_report_is_formed || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="事件报告" :span="2">
                <div v-if="hasDocuments(data.event_report)" class="file-list">
                  <el-popover placement="bottom" width="auto" trigger="click">
                    <template #reference>
                      <div class="file-item document-item">
                        <el-icon class="file-icon document-icon">
                          <component
                            :is="
                              getDocumentIcon(
                                getDocuments(data.event_report)[0]?.fileName || '事件报告'
                              )
                            "
                          />
                        </el-icon>
                        <span
                          v-if="getDocumentCount(data.event_report) > 1"
                          class="file-count-text"
                        >
                          【{{ getDocumentCount(data.event_report) }}】
                        </span>
                      </div>
                    </template>
                    <div class="attachment-options">
                      <div
                        v-for="(document, index) in getDocuments(data.event_report)"
                        :key="index"
                        class="attachment-item"
                      >
                        <div class="attachment-info">
                          <el-icon class="attachment-icon">
                            <component
                              :is="getDocumentIcon(document.fileName || '事件报告')"
                            />
                          </el-icon>
                          <span class="attachment-name">{{
                            document.fileName || `事件报告${index + 1}`
                          }}</span>
                        </div>
                        <div class="attachment-actions">
                          <el-button
                            type="primary"
                            size="small"
                            link
                            @click="previewDocument(document)"
                          >
                            预览
                          </el-button>
                          <el-button
                            type="default"
                            size="small"
                            link
                            @click="downloadDocument(document)"
                          >
                            下载
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </el-popover>
                </div>
                <span v-else>-</span>
              </el-descriptions-item>

              <el-descriptions-item label="备注" :span="2">
                {{ data.remarks || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 变更记录 Tab -->
        <el-tab-pane label="变更记录" name="changes">
          <div class="change-record-placeholder">
            <el-empty description="后续开发，敬请期待">
              <template #image>
                <el-icon size="60px" color="#c0c4cc"><Clock /></el-icon>
              </template>
            </el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </ele-drawer>

  <!-- 图片预览对话框 -->
  <el-dialog
    v-model="imagePreviewVisible"
    title="图片预览"
    width="80%"
    :destroy-on-close="true"
  >
    <div class="image-preview-container">
      <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
    </div>
  </el-dialog>

  <!-- 文件预览组件 -->
  <FilePreview ref="filePreviewRef" v-model="showFilePreview" />
</template>

<script setup>
  import { computed, ref, h } from 'vue';
  import {
    Picture,
    Document,
    DocumentCopy,
    Reading,
    Clock
  } from '@element-plus/icons-vue';
  import { ElMessageBox } from 'element-plus';
  import FilePreview from '@/components/FilePreview/index.vue';

  defineOptions({ name: 'SystemEventDetail' });

  const props = defineProps({
    /** 弹窗是否可见 */
    modelValue: {
      type: Boolean,
      default: false
    },
    /** 详情数据 */
    data: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗可见性 */
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  });

  /** 当前活跃标签页 */
  const activeTab = ref('detail');

  /** 图片预览相关 */
  const imagePreviewVisible = ref(false);
  const previewImageUrl = ref('');

  /** 文件预览相关 */
  const filePreviewRef = ref(null);
  const showFilePreview = ref(false);

  /** 检查是否有附件（图片） */
  const hasAttachments = (attachments) => {
    if (!attachments) return false;
    if (typeof attachments === 'string') {
      return attachments.trim() !== '';
    }
    if (Array.isArray(attachments)) {
      return attachments.length > 0;
    }
    return false;
  };

  /** 获取附件数量（图片） */
  const getAttachmentCount = (attachments) => {
    if (!attachments) return 0;
    if (typeof attachments === 'string') {
      return attachments.split(',').filter((url) => url.trim() !== '').length;
    }
    if (Array.isArray(attachments)) {
      return attachments.length;
    }
    return 0;
  };

  /** 检查是否有文档 */
  const hasDocuments = (documents) => {
    if (!documents) return false;
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        return Array.isArray(parsed) && parsed.length > 0;
      } catch {
        return documents.trim() !== '';
      }
    }
    if (Array.isArray(documents)) {
      return documents.length > 0;
    }
    return false;
  };

  /** 获取文档数量 */
  const getDocumentCount = (documents) => {
    if (!documents) return 0;
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        return Array.isArray(parsed) ? parsed.length : 0;
      } catch {
        return documents.trim() !== '' ? 1 : 0;
      }
    }
    if (Array.isArray(documents)) {
      return documents.length;
    }
    return 0;
  };

  /** 获取图片URL列表 */
  const getImageUrls = (attachments) => {
    if (!attachments) return [];
    if (typeof attachments === 'string') {
      return attachments.split(',').filter((url) => url.trim() !== '');
    }
    if (Array.isArray(attachments)) {
      return attachments;
    }
    return [];
  };

  /** 获取文档列表 */
  const getDocuments = (documents) => {
    if (!documents) return [];
    if (typeof documents === 'string') {
      try {
        const parsed = JSON.parse(documents);
        if (Array.isArray(parsed)) {
          return parsed.map((item) => ({
            url: item.url,
            fileName: item.fileName,
            ossId: item.ossId
          }));
        }
      } catch {
        return documents.trim() !== ''
          ? [{ url: documents, fileName: '事件报告', ossId: '' }]
          : [];
      }
    }
    if (Array.isArray(documents)) {
      return documents.map((item) => ({
        url: item.url,
        fileName: item.fileName,
        ossId: item.ossId
      }));
    }
    return [];
  };

  /** 打开图片预览 */
  const openImagePreview = (url) => {
    previewImageUrl.value = url;
    imagePreviewVisible.value = true;
  };

  /** 预览文档 */
  const previewDocument = (document) => {
    filePreviewRef.value?.previewFile(document);
  };

  /** 下载文档 */
  const downloadDocument = async (document) => {
    const url = document.url;
    const fileName = document.fileName || '事件报告';

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('下载失败:', error);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  /** 获取文档图标 */
  const getDocumentIcon = (filename) => {
    const lowerName = filename.toLowerCase();
    if (lowerName.endsWith('.pdf')) {
      return Reading;
    } else if (lowerName.endsWith('.doc') || lowerName.endsWith('.docx')) {
      return DocumentCopy;
    }
    return Document;
  };

  /** 获取故障类别标签 */
  const getFaultCategoryLabel = (category) => {
    const categoryMap = {
      0: '应用服务故障',
      1: '上线/变更引发',
      2: '应用设计缺陷',
      3: '业务需求缺陷',
      4: '外部系统异常',
      5: '数据库故障',
      6: '基础网络故障',
      7: '基础硬件故障',
      8: '中间件故障',
      9: '运营商线路异常',
      10: '产品/政策配置',
      11: '操作/咨询类'
    };
    return categoryMap[category] || category;
  };

  /** 获取截图列表 */
  const getScreenshots = (screenshotStr) => {
    if (!screenshotStr) return [];
    return screenshotStr.split(',').filter((url) => url.trim());
  };

  /** 获取报告文件名 */
  const getReportFileName = () => {
    if (!props.data?.event_report) return '';
    const url = props.data.event_report;
    const filename = url.split('/').pop() || '事件报告';
    return filename.includes('.') ? filename : '事件报告.pdf';
  };

  /** 获取级别类型 */
  const getLevelType = (level) => {
    const typeMap = {
      I: 'danger',
      II: 'warning',
      III: 'info',
      IV: 'success'
    };
    return typeMap[level] || 'info';
  };

  /** 预览图片 */
  const previewImage = (imageUrl) => {
    previewImageUrl.value = imageUrl;
    imagePreviewVisible.value = true;
  };

  /** 关闭弹窗 */
  const handleClose = () => {
    visible.value = false;
    // 重置标签页
    activeTab.value = 'detail';
  };
</script>

<style lang="scss" scoped>
  .event-detail {
    padding: 0 8px;
  }

  .detail-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    // border-bottom: 2px solid var(--el-color-primary);
  }

  :deep(.el-descriptions) {
    .el-descriptions__label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      background: var(--el-fill-color-light);
    }

    .el-descriptions__content {
      color: var(--el-text-color-primary);
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0;
  }

  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--el-fill-color-lighter);

    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .file-icon {
    font-size: 18px;
    flex-shrink: 0;
  }

  .image-icon {
    color: var(--el-color-success);
  }

  .document-icon {
    color: var(--el-color-primary);
  }

  .file-name {
    font-size: 14px;
    color: var(--el-text-color-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }

  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .change-record-placeholder {
    padding: 40px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  :deep(.el-tabs__content) {
    padding: 20px;
  }

  :deep(.el-tab-pane) {
    max-height: calc(100vh - 300px);
    overflow-y: auto;
  }

  /* 图片预览样式 */
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .image-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
  }

  .image-item:hover {
    transform: translateY(-1px);
  }

  .image-icon {
    font-size: 18px;
    color: var(--el-color-success);
    flex-shrink: 0;
  }

  .image-count-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 2px;
  }

  .image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    max-width: 320px;
  }

  .gallery-item {
    position: relative;
    cursor: pointer;
  }

  .gallery-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.3s ease;
  }

  .gallery-thumbnail:hover {
    border-color: var(--el-color-primary);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-count-text {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-left: 2px;
  }

  .attachment-options {
    max-width: 320px;
  }

  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .attachment-item:last-child {
    border-bottom: none;
  }

  .attachment-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .attachment-icon {
    font-size: 16px;
    color: var(--el-color-primary);
    flex-shrink: 0;
  }

  .attachment-name {
    font-size: 14px;
    color: var(--el-text-color-regular);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .attachment-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }
</style>
