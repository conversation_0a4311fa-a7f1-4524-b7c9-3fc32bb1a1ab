<template>
  <ele-page>
    <!-- 搜索组件 -->
    <simple-search
      :fields="searchFields"
      :default-field="defaultField"
      :advanced-conditions="advancedConditions"
      @search="handleSimpleSearch"
      @reset="handleSearchReset"
      @advanced-search="showAdvancedModal"
      @clear-advanced="clearAdvancedConditions"
      @remove-advanced-condition="removeAdvancedCondition"
    />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <cmdb-pro-table
        ref="tableRef"
        row-key="bk_inst_id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '演练记录' }"
        cache-key="drillRecordTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="success"
            class="ele-btn-icon"
            :icon="UploadOutlined"
            @click="openImport()"
          >
            导入
          </el-button>
          <el-button
            type="warning"
            class="ele-btn-icon"
            :icon="EditOutlined"
            :disabled="!selections.length"
            @click="openBatchEdit()"
          >
            批量编辑
          </el-button>
          <el-button
            type="danger"
            class="ele-btn-icon hidden-sm-and-down"
            :icon="DeleteOutlined"
            :disabled="!selections.length"
            @click="removeBatch()"
          >
            批量删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="info" underline="never" @click="openDetail(row)">
            详情
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            v-permission="'system:config:edit'"
            type="primary"
            underline="never"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['system:config:edit', 'system:config:remove']"
            direction="vertical"
          />
          <el-link
            v-permission="'system:config:remove'"
            type="danger"
            underline="never"
            @click="removeSingle(row)"
          >
            删除
          </el-link>
        </template>
      </cmdb-pro-table>
    </ele-card>

    <!-- 详情弹窗 -->
    <drill-record-detail :data="currentDetail" v-model="showDetail" />

    <!-- 高级搜索弹窗 -->
    <advanced-search
      v-model="showAdvanced"
      :search-fields="searchFields"
      :initial-conditions="advancedConditions"
      @search="handleAdvancedSearch"
      @close="closeAdvancedModal"
    />

    <!-- 编辑弹窗 -->
    <drill-record-edit :data="current" v-model="showEdit" @done="reload" />

    <!-- 导入弹窗 -->
    <drill-record-import v-model="showImport" @done="reload" />

    <!-- 批量编辑弹窗 -->
    <drill-record-batch-edit
      v-model="showBatchEdit"
      v-model:selectedRecords="selections"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import {
    PlusOutlined,
    DeleteOutlined,
    UploadOutlined,
    EditOutlined
  } from '@/components/icons';
  import { searchInst, deleteInst, batchDeleteInst } from '@/api/cmdb';
  import SimpleSearch from '@/components/Search/simple-search.vue';
  import AdvancedSearch from '@/components/Search/advanced-search.vue';
  import DrillRecordEdit from './components/drill-record-edit.vue';
  import DrillRecordDetail from './components/drill-record-detail.vue';
  import DrillRecordImport from './components/drill-record-import.vue';
  import DrillRecordBatchEdit from './components/drill-record-batch-edit.vue';

  /** 模型实例ID */
  const bkObjId = 'drill_record';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'system_name',
        columnKey: 'system_name',
        label: '系统名称',
        align: 'center',
        width: 160,
        fixed: 'left'
      },
      {
        prop: 'drill_date',
        label: '演练日期',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'affiliated_cycle',
        label: '所属周期',
        align: 'center',
        minWidth: 140
      },
      {
        prop: 'drill_scenario',
        label: '演练场景',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'pic',
        label: '负责人',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'result',
        label: '结果',
        align: 'center',
        minWidth: 80
      },
      {
        prop: 'next_drill',
        label: '下次演练',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 140
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 160,
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true,
        fixed: 'right'
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 获取当前日期计算周期选项 */
  const getCycleOptions = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;

    let options = [];

    if (month >= 7) {
      options = [
        {
          label: `${year}年下半年（7-12月）`,
          value: `${year}年下半年（7-12月）`
        },
        {
          label: `${year + 1}年上半年（1-6月）`,
          value: `${year + 1}年上半年（1-6月）`
        },
        {
          label: `${year + 1}年下半年（7-12月）`,
          value: `${year + 1}年下半年（7-12月）`
        }
      ];
    } else {
      options = [
        {
          label: `${year}年上半年（1-6月）`,
          value: `${year}年上半年（1-6月）`
        },
        {
          label: `${year}年下半年（7-12月）`,
          value: `${year}年下半年（7-12月）`
        },
        {
          label: `${year + 1}年上半年（1-6月）`,
          value: `${year + 1}年上半年（1-6月）`
        }
      ];
    }

    return options;
  };

  /** 搜索字段配置 */
  const searchFields = ref([
    { prop: 'system_name', label: '系统名称', type: 'text' },
    { prop: 'drill_date', label: '演练日期', type: 'daterange' },
    {
      prop: 'affiliated_cycle',
      label: '所属周期',
      type: 'select',
      options: getCycleOptions()
    },
    {
      prop: 'drill_scenario',
      label: '演练场景',
      type: 'select',
      options: [
        { label: '故障恢复演练', value: '故障恢复演练' },
        { label: '安全防护演练', value: '安全防护演练' },
        { label: '性能压力演练', value: '性能压力演练' },
        { label: '灾备切换演练', value: '灾备切换演练' },
        { label: '系统升级演练', value: '系统升级演练' },
        { label: '其他场景', value: '其他场景' }
      ]
    },
    { prop: 'pic', label: '负责人', type: 'text' },
    {
      prop: 'result',
      label: '结果',
      type: 'select',
      options: [
        { label: '成功', value: '成功' },
        { label: '部分成功', value: '部分成功' },
        { label: '失败', value: '失败' }
      ]
    },
    { prop: 'next_drill', label: '下次演练', type: 'text' },
    { prop: 'remark', label: '备注', type: 'text' }
  ]);

  /** 默认搜索字段 */
  const defaultField = ref({
    prop: 'system_name',
    label: '系统名称'
  });

  /** 高级搜索条件 */
  const advancedConditions = ref([]);

  /** 当前搜索条件 */
  const currentSearchParams = ref({});

  /** 是否显示高级搜索弹窗 */
  const showAdvanced = ref(false);

  /** 是否显示详情弹窗 */
  const showDetail = ref(false);

  /** 当前详情数据 */
  const currentDetail = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示导入弹窗 */
  const showImport = ref(false);

  /** 是否显示批量编辑弹窗 */
  const showBatchEdit = ref(false);

  /** 表格数据源 */
  const datasource = async ({ pages }) => {
    let conditions = [];

    if (currentSearchParams.value.condition) {
      Object.keys(currentSearchParams.value.condition).forEach((field) => {
        const value = currentSearchParams.value.condition[field];
        if (typeof value === 'object' && value !== null) {
          Object.keys(value).forEach((operator) => {
            conditions.push({
              field: field,
              operator: operator,
              value: value[operator]
            });
          });
        } else {
          conditions.push({
            field: field,
            operator: '$eq',
            value: value
          });
        }
      });
    }

    if (advancedConditions.value.length > 0) {
      conditions = conditions.concat(
        advancedConditions.value.filter(
          (condition) =>
            condition.field &&
            condition.operator &&
            condition.value !== '' &&
            condition.value !== null &&
            condition.value !== undefined
        )
      );
    }

    const page = {
      start: (pages.pageNum - 1) * pages.pageSize,
      limit: pages.pageSize,
      sort: 'bk_inst_id'
    };

    const conditionParam =
      conditions.length > 0
        ? {
            [bkObjId]: conditions
          }
        : undefined;

    const res = await searchInst({
      bk_obj_id: bkObjId,
      condition: conditionParam,
      page
    });

    return {
      code: res.code,
      msg: res.msg,
      rows: res.rows,
      total: res.total
    };
  };

  /** 处理简单搜索 */
  const handleSimpleSearch = (params) => {
    currentSearchParams.value = params;
    reload();
  };

  /** 处理搜索重置 */
  const handleSearchReset = () => {
    currentSearchParams.value = {};
    advancedConditions.value = [];
    reload();
  };

  /** 显示高级搜索弹窗 */
  const showAdvancedModal = () => {
    showAdvanced.value = true;
  };

  /** 关闭高级搜索弹窗 */
  const closeAdvancedModal = () => {
    showAdvanced.value = false;
  };

  /** 处理高级搜索 */
  const handleAdvancedSearch = (conditions) => {
    advancedConditions.value = conditions;
    reload();
  };

  /** 清空高级搜索条件 */
  const clearAdvancedConditions = () => {
    advancedConditions.value = [];
    reload();
  };

  /** 移除单个高级搜索条件 */
  const removeAdvancedCondition = (condition) => {
    const index = advancedConditions.value.findIndex(
      (c) =>
        c.field === condition.field &&
        c.operator === condition.operator &&
        c.value === condition.value
    );
    if (index > -1) {
      advancedConditions.value.splice(index, 1);
      reload();
    }
  };

  /** 打开详情弹窗 */
  const openDetail = (row) => {
    currentDetail.value = row;
    showDetail.value = true;
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 打开批量编辑弹窗 */
  const openBatchEdit = () => {
    if (!selections.value.length) {
      EleMessage.error('请选择要编辑的数据');
      return;
    }
    showBatchEdit.value = true;
  };

  /** 单条删除 */
  const removeSingle = (row) => {
    ElMessageBox.confirm(`确定要删除该条演练记录吗？`, '系统提示', {
      type: 'warning'
    })
      .then(async () => {
        const loading = EleMessage.loading('请稍候...');
        try {
          await deleteInst({
            bkObjId,
            instId: row.bk_inst_id
          });
          loading.close();
          EleMessage.success('删除成功');
          reload();
        } catch (e) {
          loading.close();
          EleMessage.error(e.message || '删除失败');
        }
      })
      .catch(() => {});
  };

  /** 批量删除 */
  const removeBatch = () => {
    if (!selections.value.length) {
      EleMessage.error('请选择要删除的数据');
      return;
    }

    ElMessageBox.confirm(
      `确定要删除选中的${selections.value.length}条演练记录吗？`,
      '系统提示',
      { type: 'warning' }
    )
      .then(async () => {
        const loading = EleMessage.loading('请稍候...');
        try {
          const instIds = selections.value.map((item) => item.bk_inst_id);
          await batchDeleteInst({
            bkObjId,
            instIds: instIds
          });
          loading.close();
          EleMessage.success('删除成功');
          reload();
        } catch (e) {
          loading.close();
          EleMessage.error(e.message || '删除失败');
        }
      })
      .catch(() => {});
  };

  /** 刷新表格 */
  const reload = () => {
    tableRef.value?.reload?.();
    selections.value = [];
  };
</script>
