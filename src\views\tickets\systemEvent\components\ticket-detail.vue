<template>
  <el-drawer
    :model-value="visible"
    title="工单详情"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
  >
    <div class="ele-body">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4>基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单ID">
            {{ ticketData?.ticket_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单类型">
            {{ ticketData?.ticket_type || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单等级">
            <el-tag
              v-if="ticketData?.level"
              :type="getLevelType(ticketData.level)"
              size="small"
            >
              {{ getLevelText(ticketData.level) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ ticketData?.create_user_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(ticketData?.ticket_start_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag
              v-if="ticketData?.ticket_status !== undefined"
              :type="getStatusType(ticketData.ticket_status)"
              size="small"
            >
              {{ getStatusText(ticketData.ticket_status) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="执行人" span="2">
            <div v-if="uniqueExecutorUsers?.length" class="user-list">
              <el-tag
                v-for="userId in uniqueExecutorUsers"
                :key="userId"
                size="small"
                style="margin-right: 8px; margin-bottom: 8px"
              >
                {{ getUserNickname(userId) }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="内容" span="2">
            {{ ticketData?.comment || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 填写记录详情 -->
      <div v-if="ticketData?.system_item" class="detail-section">
        <h4>填写记录详情</h4>

        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="事件标题">
            {{ ticketData.system_item.event_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="记录人">
            {{ ticketData.system_item.recorder || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="发生时间">
            {{ ticketData.system_item.occurrence_time || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="事件来源">
            {{ ticketData.system_item.event_source || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="事件级别">
            {{ ticketData.system_item.event_level || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="影响范围">
            {{ ticketData.system_item.impact_scope || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="影响业务">
            {{ ticketData.system_item.affected_business || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="事件截图">
            {{ ticketData.system_item.event_screenshot ? '已上传' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="事件描述" span="2">
            {{ ticketData.system_item.event_desc || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 故障信息 -->
        <div class="form-section-title">故障信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="故障类别">
            {{ ticketData.system_item.fault_category || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="根因故障系统">
            {{ ticketData.system_item.root_cause_fault_system || '-' }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="ticketData.system_item.fault_category === '1'"
            label="引发故障的变更单号"
          >
            {{ ticketData.system_item.change_order_no_causing_fault || '-' }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="ticketData.system_item.fault_category === '1'"
            label="变更申请人"
          >
            {{ ticketData.system_item.change_requester || '-' }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="ticketData.system_item.fault_category === '1'"
            label="开发供应商"
          >
            {{ ticketData.system_item.development_supplier || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="事件原因" span="2">
            {{ ticketData.system_item.event_cause || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 处理信息 -->
        <div class="form-section-title">处理信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="科创处置人员">
            {{ ticketData.system_item.dev_handler || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="运维处置人员">
            {{ ticketData.system_item.ops_handler || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="解决时间">
            {{ formatTime(ticketData.system_item.resolution_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="涉及生产变更">
            {{
              ticketData.system_item
                .solution_and_whether_production_change_is_involved || '-'
            }}
          </el-descriptions-item>
          <el-descriptions-item label="涉及OA流程">
            {{
              ticketData.system_item
                .solution_and_whether_oa_process_is_involved || '-'
            }}
          </el-descriptions-item>
          <el-descriptions-item label="是否进行复盘">
            {{ ticketData.system_item.whether_review_is_conducted || '-' }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="ticketData.system_item.whether_review_is_conducted === '是'"
            label="复盘会召开日期"
          >
            {{ ticketData.system_item.review_meeting_date || '-' }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="ticketData.system_item.whether_review_is_conducted === '是'"
            label="是否形成事件报告"
          >
            {{ ticketData.system_item.whether_event_report_is_formed || '-' }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="
              ticketData.system_item.whether_event_report_is_formed === '是'
            "
            label="事件报告"
          >
            {{ ticketData.system_item.event_report ? '已上传' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="事件处理过程" span="2">
            {{ ticketData.system_item.event_handling_process || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="事件处理结果和依据" span="2">
            {{ ticketData.system_item.event_handling_results_and_basis || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" span="2">
            {{ ticketData.system_item.remarks || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">关闭</el-button>
    </template>

    <!-- 转派工单抽屉 -->
    <TicketTransfer
      v-model="transferVisible"
      :ticket-data="ticketData"
      @done="handleOperationDone"
    />

    <!-- 审核工单抽屉 -->
    <TicketApprove
      v-model="approveVisible"
      :ticket-data="ticketData"
      @done="handleOperationDone"
    />

    <!-- 关闭工单确认对话框 -->
    <TicketClose
      v-model="closeVisible"
      :ticket-data="ticketData"
      @done="handleOperationDone"
    />
  </el-drawer>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { pageUsers } from '@/api/system/user';
  import TicketTransfer from './ticket-transfer.vue';
  import TicketApprove from './ticket-approve.vue';
  import TicketClose from './ticket-close.vue';

  defineOptions({ name: 'TicketDetail' });

  const props = defineProps({
    modelValue: Boolean,
    ticketData: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const visible = computed(() => props.modelValue);

  // 去重的执行人列表
  const uniqueExecutorUsers = computed(() => {
    if (!props.ticketData?.ticketFlowUserId?.length) return [];
    return [...new Set(props.ticketData.ticketFlowUserId)];
  });

  // 弹窗状态
  const transferVisible = ref(false);
  const approveVisible = ref(false);
  const closeVisible = ref(false);

  // 用户数据映射
  const userMap = ref(new Map());

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const handleTransfer = () => {
    transferVisible.value = true;
  };

  const handleApprove = () => {
    approveVisible.value = true;
  };

  const handleClose = () => {
    closeVisible.value = true;
  };

  const handleOperationDone = () => {
    // 操作完成后通知父组件刷新数据
    emit('done');
  };

  const getLevelType = (level) => {
    const levelTypes = {
      p1: 'danger',
      p2: 'warning',
      p3: 'primary',
      p4: 'info'
    };
    return levelTypes[level] || 'info';
  };

  const getStatusType = (status) => {
    const statusTypes = {
      0: 'warning',
      1: 'success',
      2: 'danger'
    };
    return statusTypes[status] || 'info';
  };

  const getStatusText = (status) => {
    const statusTexts = {
      0: '待处理',
      1: '已完成',
      2: '已关闭'
    };
    return statusTexts[status] || '未知';
  };

  const getStepStatusType = (status) => {
    const statusTypes = {
      0: 'warning',
      1: 'success'
    };
    return statusTypes[status] || 'info';
  };

  const getStepStatusText = (status) => {
    const statusTexts = {
      0: '进行中',
      1: '已完成'
    };
    return statusTexts[status] || '未知';
  };

  const getFieldLabel = (field) => {
    const fieldLabels = {
      bk_inst_name: '实例名称',
      bk_inst_id: '实例ID',
      system_name: '系统名称',
      event_level: '事件级别',
      fault_category: '故障类别'
    };
    return fieldLabels[field] || field;
  };

  const getLevelText = (level) => {
    const levelTexts = {
      p1: '紧急',
      p2: '高',
      p3: '中',
      p4: '低'
    };
    return levelTexts[level] || level;
  };

  // 获取所有用户数据
  const loadAllUsers = async () => {
    try {
      const res = await pageUsers({
        pageNum: 1,
        pageSize: 9999
      });
      const users = res.rows || [];
      userMap.value.clear();
      users.forEach((user) => {
        userMap.value.set(user.userId, user.nickName || user.userName);
      });
    } catch (e) {
      console.error('获取用户列表失败:', e);
    }
  };

  // 根据用户ID获取用户昵称
  const getUserNickname = (userId) => {
    return userMap.value.get(userId) || `用户${userId}`;
  };

  const formatTime = (time) => {
    if (!time) return '-';
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  onMounted(() => {
    loadAllUsers(); // 先加载用户数据
  });
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .detail-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .detail-section:last-child {
    border-bottom: none;
  }

  .detail-section h4,
  .detail-section h5 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .detail-section h5 {
    font-size: 14px;
    margin-top: 16px;
  }

  .user-list {
    display: flex;
    flex-wrap: wrap;
  }

  .history-item {
    padding: 8px 0;
  }

  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .step-number {
    font-weight: 600;
    color: #303133;
  }

  .system-item-info {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }

  .action-section .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }

  .form-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin: 16px 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }
</style>
