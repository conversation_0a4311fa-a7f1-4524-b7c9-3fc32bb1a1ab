<!-- 项目编辑抽屉 -->
<template>
  <el-drawer
    :model-value="visible"
    :title="isUpdate ? '编辑项目' : '新建项目'"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="项目名称" prop="project_name">
              <el-input
                v-model="form.project_name"
                placeholder="请输入项目名称"
                clearable
                :maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="项目预算" prop="ppb">
              <el-input-number
                v-model="form.ppb"
                placeholder="请输入项目预算"
                style="width: 100%"
                :min="0"
                :precision="2"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="project_maintainer">
              <el-input
                v-model="form.project_maintainer"
                placeholder="请输入项目负责人"
                clearable
                :maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="项目预算内存" prop="project_budget_memory">
              <el-input-number
                v-model="form.project_budget_memory"
                placeholder="请输入项目预算内存"
                style="width: 100%"
                :min="0"
                :precision="2"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目预算存储" prop="project_budget_disk">
              <el-input-number
                v-model="form.project_budget_disk"
                placeholder="请输入项目预算存储"
                style="width: 100%"
                :min="0"
                :precision="2"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="项目预算CPU" prop="project_budget_cpu">
              <el-input-number
                v-model="form.project_budget_cpu"
                placeholder="请输入项目预算CPU"
                style="width: 100%"
                :min="0"
                :precision="2"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目预算NAS" prop="project_budget_nas">
              <el-input-number
                v-model="form.project_budget_nas"
                placeholder="请输入项目预算NAS"
                style="width: 100%"
                :min="0"
                :precision="2"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="备注" prop="projcet_mark">
              <el-input
                v-model="form.projcet_mark"
                type="textarea"
                placeholder="请输入备注"
                :rows="3"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { createInst, updateInst } from '@/api/cmdb';

  defineOptions({ name: 'ProjectEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 模型实例ID */
  const bkObjId = 'project_detail';

  /** 是否是修改 */
  const isUpdate = computed(() => {
    return props.data != null;
  });

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单数据 */
  const form = ref({
    project_name: '',
    ppb: null,
    projcet_mark: '',
    project_budget_memory: null,
    project_budget_disk: null,
    project_budget_cpu: null,
    project_budget_nas: null,
    project_maintainer: ''
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {
      project_name: [
        { required: true, message: '请输入项目名称', trigger: 'blur' }
      ]
    };
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        const data = { ...form.value };

        if (isUpdate.value) {
          await updateInst({
            bkObjId,
            bkInstId: props.data.bk_inst_id,
            instInfoMap: {
              ...data,
              bk_inst_name: data.project_name
            }
          });
          EleMessage.success('修改成功');
        } else {
          await createInst({
            bkObjId,
            instInfoMap: {
              ...data,
              bk_inst_name: data.project_name
            }
          });
          EleMessage.success('创建成功');
        }

        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '操作失败');
      }
      loading.value = false;
    });
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
  };

  /** 监听编辑数据变化 */
  watch(
    () => props.data,
    (value) => {
      if (value) {
        Object.assign(form.value, value);
      } else {
        // 新增模式，重置表单
        Object.assign(form.value, {
          project_name: '',
          ppb: null,
          projcet_mark: '',
          project_budget_memory: null,
          project_budget_disk: null,
          project_budget_cpu: null,
          project_budget_nas: null,
          project_maintainer: ''
        });
      }
    },
    { immediate: true }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }
</style>
