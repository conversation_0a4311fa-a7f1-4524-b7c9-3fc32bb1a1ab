<template>
  <el-card shadow="hover" class="transition-all-300">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <el-icon class="text-warning mr-2"><WarningFilled /></el-icon>
          <span class="text-lg font-semibold">系统事件记录</span>
        </div>
        <el-link type="primary" :underline="false">查看全部</el-link>
      </div>
    </template>
    <div class="p-2">
      <el-row :gutter="24" class="mb-6">
        <el-col :lg="12" :md="24">
          <div>
            <h3 class="text-base font-medium mb-4">事件级别统计</h3>
            <div class="h-48">
              <div ref="eventLevelChart" class="w-full h-full"></div>
            </div>
          </div>
        </el-col>
        <el-col :lg="12" :md="24">
          <div>
            <h3 class="text-base font-medium mb-4">故障类别统计</h3>
            <div class="h-48">
              <div ref="faultCategoryChart" class="w-full h-full"></div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-table :data="eventData" style="width: 100%">
        <el-table-column prop="title" label="事件标题" width="200">
          <template #default="{ row }">
            <div class="flex items-center">
              <span
                class="w-2 h-2 rounded-full mr-2"
                :class="getLevelColor(row.level)"
              ></span>
              <span>{{ row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="事件级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.level)" size="small">
              {{ row.level }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="故障类别" width="150" />
        <el-table-column prop="time" label="时间" width="150" />
        <el-table-column prop="impact" label="影响范围">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.level)" size="small">
              {{ row.impact }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script setup>
  import { ref, onMounted, nextTick } from 'vue';
  import * as echarts from 'echarts';
  import { WarningFilled } from '@element-plus/icons-vue';

  const eventLevelChart = ref(null);
  const faultCategoryChart = ref(null);

  const eventData = [
    {
      title: '联合贷款事件',
      level: 'I级',
      category: '应用服务故障(1)',
      time: '2023-10-15 08:30',
      impact: '8笔放款'
    },
    {
      title: '自动审批事件',
      level: 'II级',
      category: '中间件故障(8)',
      time: '2023-10-14 16:45',
      impact: '保单OCKR识别'
    },
    {
      title: '业务反馈事件',
      level: 'III级',
      category: '基础网络故障(6)',
      time: '2023-10-14 10:15',
      impact: '给别人操作'
    }
  ];

  const getLevelColor = (level) => {
    const colors = {
      I级: 'bg-red-500',
      II级: 'bg-yellow-500',
      III级: 'bg-green-500'
    };
    return colors[level] || 'bg-gray-500';
  };

  const getLevelType = (level) => {
    const types = {
      I级: 'danger',
      II级: 'warning',
      III级: 'success'
    };
    return types[level] || '';
  };

  const initCharts = () => {
    nextTick(() => {
      // 事件级别统计图表
      if (eventLevelChart.value) {
        const chart1 = echarts.init(eventLevelChart.value);
        const option1 = {
          color: ['#F53F3F', '#FF7D00', '#00B42A', '#86909C'],
          tooltip: { trigger: 'item' },
          legend: { orient: 'vertical', left: 10 },
          series: [
            {
              name: '事件级别',
              type: 'pie',
              radius: '60%',
              data: [
                { value: 15, name: 'I级' },
                { value: 30, name: 'II级' },
                { value: 45, name: 'III级' },
                { value: 10, name: 'IV级' }
              ],
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2
              }
            }
          ]
        };
        chart1.setOption(option1);

        window.addEventListener('resize', () => {
          chart1.resize();
        });
      }

      // 故障类别统计图表
      if (faultCategoryChart.value) {
        const chart2 = echarts.init(faultCategoryChart.value);
        const option2 = {
          color: ['#165DFF', '#36CFC9', '#722ED1', '#FF7D00', '#F53F3F'],
          tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
          grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
          xAxis: {
            type: 'category',
            data: [
              '应用服务故障',
              '中间件故障',
              '基础网络故障',
              '数据库故障',
              '硬件故障'
            ],
            axisLabel: { interval: 0, rotate: 45, align: 'right' }
          },
          yAxis: { type: 'value', name: '数量' },
          series: [
            {
              name: '故障数量',
              type: 'bar',
              data: [12, 8, 15, 10, 6]
            }
          ]
        };
        chart2.setOption(option2);

        window.addEventListener('resize', () => {
          chart2.resize();
        });
      }
    });
  };

  onMounted(() => {
    initCharts();
  });
</script>

<style scoped>
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  .text-warning {
    color: #ff7d00;
  }
</style>
