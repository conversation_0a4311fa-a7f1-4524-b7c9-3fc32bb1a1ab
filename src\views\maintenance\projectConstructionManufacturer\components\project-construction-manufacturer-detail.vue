<template>
  <ele-drawer
    v-model="visible"
    :size="1000"
    title="项目建设厂家详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="detail-container" v-if="data">
      <!-- 基本信息 -->
      <div class="detail-section-title">基本信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="项目名称" :span="1">
          {{ data?.project_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="合同名称" :span="1">
          {{ data?.contract_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目金额" :span="1">
          {{ data?.project_amount_cny ? `${data.project_amount_cny} 元` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="免维期" :span="1">
          {{ data?.free_maintenance_duration || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目开始时间" :span="1">
          {{ formatDate(data?.project_start_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目验收时间" :span="1">
          {{ formatDate(data?.project_acceptance_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目免维开始时间" :span="1">
          {{ formatDate(data?.project_free_maintenance_start_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目免维结束时间" :span="1">
          {{ formatDate(data?.project_free_maintenance_end_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="运维是否提醒" :span="1">
          <el-tag
            :type="data?.is_operation_reminder === '是' ? 'success' : 'info'"
            size="small"
          >
            {{ data?.is_operation_reminder || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="合同是否有维保信息" :span="1">
          <el-tag
            :type="
              data?.has_maintenance_info_in_contract === '是'
                ? 'success'
                : 'info'
            "
            size="small"
          >
            {{ data?.has_maintenance_info_in_contract || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="免维方式" :span="2">
          {{ formatMaintenanceType(data?.free_maintenance_type) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 维保费用信息 -->
      <div class="detail-section-title">维保费用信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="合同约定维保费用" :span="1">
          {{
            data?.contract_maintenance_fee
              ? `${data.contract_maintenance_fee} 元`
              : '-'
          }}
        </el-descriptions-item>
        <el-descriptions-item label="维保比例" :span="1">
          {{ data?.maintenance_ratio ? `${data.maintenance_ratio}%` : '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 乙方信息 -->
      <div class="detail-section-title">乙方信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="乙方公司" :span="2">
          {{ data?.party_b_company || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="乙方负责人" :span="1">
          {{ data?.contract_b_owner || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="乙方负责人联系方式" :span="1">
          {{ data?.contract_b_tel || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 业务负责人信息 -->
      <div class="detail-section-title">业务负责人信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="业务负责人" :span="1">
          {{ data?.app_onwer || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="业务负责人联系方式" :span="1">
          {{ data?.app_owner_tel || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 科创负责人信息 -->
      <div class="detail-section-title">科创负责人信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="科创负责人" :span="1">
          {{ data?.tech_innovation_owner || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="科创负责人联系方式" :span="1">
          {{ data?.tech_innovation_owner_tel || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 信息技术部负责人信息 -->
      <div class="detail-section-title">信息技术部负责人信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="信息技术部负责人" :span="1">
          {{ data?.tech_department_owner || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="信息技术部负责人联系方式" :span="1">
          {{ data?.tech_department_owner_tel || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'ProjectConstructionManufacturerDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 格式化日期显示 */
  const formatDate = (dateTime) => {
    if (!dateTime) return '-';

    // 如果已经是 YYYY-MM-DD 格式，直接返回
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateTime)) {
      return dateTime;
    }

    // 如果是 YYYY-MM-DD HH:mm:ss 格式，截取日期部分
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateTime)) {
      return dateTime.split(' ')[0];
    }

    // 其他情况尝试转换为Date对象并格式化
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '-';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch {
      return '-';
    }
  };

  /** 格式化免维方式 */
  const formatMaintenanceType = (type) => {
    if (!type) return '-';

    if (typeof type === 'string') {
      return (
        type
          .split(',')
          .filter((item) => item.trim())
          .join('、') || '-'
      );
    }

    if (Array.isArray(type)) {
      return type.join('、') || '-';
    }

    return type;
  };

  /** 关闭抽屉 */
  const handleClose = () => {
    visible.value = false;
  };
</script>

<style scoped>
  .detail-container {
    padding: 0 8px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0;
  }

  .detail-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .detail-section-title:first-child {
    margin-top: 0;
  }

  :deep(.el-divider) {
    margin: 24px 0 16px;
  }

  :deep(.el-divider__text) {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  :deep(.el-descriptions) {
    margin-bottom: 0;
  }
</style>
