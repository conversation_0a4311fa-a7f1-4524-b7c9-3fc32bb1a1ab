<template>
  <el-drawer
    :model-value="visible"
    title="新建工单"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
        @submit.prevent=""
      >
        <el-form-item label="工单内容" prop="comment">
          <el-input
            v-model="form.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入工单内容"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="工单等级" prop="level">
          <el-select
            v-model="form.level"
            placeholder="请选择工单等级"
            style="width: 100%"
          >
            <el-option label="紧急 (P1)" value="p1" />
            <el-option label="高 (P2)" value="p2" />
            <el-option label="中 (P3)" value="p3" />
            <el-option label="低 (P4)" value="p4" />
          </el-select>
        </el-form-item>

        <el-form-item label="执行人" prop="executor_user_id">
          <el-select
            v-model="form.executor_user_id"
            placeholder="请选择执行人"
            style="width: 100%"
            filterable
            @change="handleExecutorChange"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        创建工单
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { useUserStore } from '@/store/modules/user';
  import { allocationTicket } from '@/api/ticket';
  import { pageUsers } from '@/api/system/user';

  defineOptions({ name: 'TicketCreate' });

  const props = defineProps({
    modelValue: Boolean
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  const userStore = useUserStore();

  const visible = computed(() => props.modelValue);

  const formRef = ref(null);
  const loading = ref(false);

  const userOptions = ref([]);

  const form = ref({
    comment: '',
    level: '',
    executor_user_name: '',
    executor_user_id: null
  });

  const rules = computed(() => ({
    comment: [{ required: true, message: '请输入工单内容', trigger: 'blur' }],
    level: [{ required: true, message: '请选择工单等级', trigger: 'change' }],
    executor_user_id: [
      { required: true, message: '请选择执行人', trigger: 'change' }
    ]
  }));

  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  const handleClosed = () => {
    resetFields();
  };

  const resetFields = () => {
    formRef.value?.resetFields();
    form.value = {
      comment: '',
      level: '',
      executor_user_name: '',
      executor_user_id: null
    };
    userOptions.value = [];
  };

  const handleSave = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (valid) => {
      if (!valid) return;

      loading.value = true;
      try {
        // 构建系统事件数据，所有字段设为空值
        const systemItem = {
          event_name: '',
          recorder: '',
          event_desc: '',
          occurrence_time: '',
          event_source: '',
          event_level: '',
          impact_scope: '',
          affected_business: '',
          event_screenshot: '',
          fault_category: '',
          root_cause_fault_system: '',
          system_level: '',
          event_cause: '',
          dev_handler: '',
          ops_handler: '',
          event_handling_process: '',
          event_handling_results_and_basis: '',
          resolution_time: '',
          solution_and_whether_production_change_is_involved: '',
          solution_and_whether_oa_process_is_involved: '',
          change_order_no_causing_fault: '',
          change_requester: '',
          development_supplier: '',
          whether_review_is_conducted: '',
          review_meeting_date: '',
          whether_event_report_is_formed: '',
          event_report: '',
          remarks: ''
        };

        // 构建请求数据
        const requestData = {
          ticket_type: '系统事件',
          level: form.value.level,
          comment: form.value.comment,
          follow_field: [
            'event_name',
            'occurrence_time',
            'event_source',
            'event_level',
            'affected_business',
            'event_screenshot',
            'fault_category',
            'root_cause_fault_system',
            'event_cause',
            'ops_handler',
            'event_handling_process',
            'event_handling_results_and_basis',
            'resolution_time',
            'solution_and_whether_production_change_is_involved',
            'whether_review_is_conducted'
          ],
          executor_user_name: form.value.executor_user_name,
          executor_user_id: form.value.executor_user_id,
          system_item: systemItem
        };

        await allocationTicket(requestData);

        EleMessage.success('工单创建成功');
        updateVisible(false);
        emit('done');
      } catch (e) {
        EleMessage.error(e.message || '创建工单失败');
      }
      loading.value = false;
    });
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const handleExecutorChange = (value) => {
    const user = userOptions.value.find((u) => u.value === value);
    if (user) {
      form.value.executor_user_name = user.label;
    }
  };

  const searchUsers = async () => {
    try {
      const res = await pageUsers({
        pageNum: 1,
        pageSize: 9999 // 获取所有用户
      });
      userOptions.value =
        res.rows?.map((user) => ({
          label: user.nickName || user.userName,
          value: user.userId
        })) || [];
    } catch (e) {
      console.error('获取用户列表失败:', e);
    }
  };

  watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        // 初始化用户列表
        searchUsers();
      }
    }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }
</style>
