<template>
  <ele-drawer
    v-model="visible"
    :size="1000"
    title="维保厂家详情"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="detail-container" v-if="data">
      <!-- 基本信息 -->
      <div class="detail-section-title">基本信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="项目名称" :span="1">
          {{ data?.project_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="维保标的" :span="1">
          {{ data?.maintained_project || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="维保厂家名称" :span="2">
          {{ data?.maintenance_company || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 联系信息 -->
      <div class="detail-section-title">联系信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="联系人" :span="1">
          {{ data?.contact || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系方式" :span="1">
          {{ data?.contact_tel || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="维保人员" :span="2">
          {{ data?.maintenance_engineer || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 维保信息 -->
      <div class="detail-section-title">维保信息</div>
      <el-descriptions
        :column="2"
        border
        size="large"
        :label-style="{ width: '140px', backgroundColor: '#fafafa' }"
      >
        <el-descriptions-item label="维保方式" :span="1">
          {{ data?.maintenance_mode || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="维保金额" :span="1">
          {{ data?.maintenance_fee ? `${data.maintenance_fee} 万` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="维保开始日期" :span="1">
          {{ formatDate(data?.maintenance_start_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="维保结束周期" :span="1">
          {{ formatDate(data?.maintenance_end_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="维保是否到期提醒" :span="1">
          <el-tag
            :type="data?.is_maintenance_due_reminder === '是' ? 'success' : 'info'"
            size="small"
          >
            {{ data?.is_maintenance_due_reminder || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="维保评价得分" :span="1">
          {{ data?.maintenance_rating || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { computed } from 'vue';

  defineOptions({ name: 'MaintenanceCompanyInfoDetail' });

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });

  const emit = defineEmits(['update:modelValue']);

  /** 弹窗是否打开 */
  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    }
  });

  /** 格式化日期显示 */
  const formatDate = (dateTime) => {
    if (!dateTime) return '-';

    // 如果已经是 YYYY-MM-DD 格式，直接返回
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateTime)) {
      return dateTime;
    }

    // 如果是 YYYY-MM-DD HH:mm:ss 格式，截取日期部分
    if (typeof dateTime === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateTime)) {
      return dateTime.split(' ')[0];
    }

    // 其他情况尝试转换为Date对象并格式化
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '-';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch {
      return '-';
    }
  };

  /** 关闭抽屉 */
  const handleClose = () => {
    visible.value = false;
  };
</script>

<style scoped>
  .detail-container {
    padding: 0 8px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0;
  }

  .detail-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .detail-section-title:first-child {
    margin-top: 0;
  }

  :deep(.el-divider) {
    margin: 24px 0 16px;
  }

  :deep(.el-divider__text) {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  :deep(.el-descriptions) {
    margin-bottom: 0;
  }
</style>