<template>
  <el-drawer
    :model-value="visible"
    title="批量编辑维保服务记录"
    :size="800"
    :destroy-on-close="true"
    direction="rtl"
    @update:model-value="updateVisible"
    @closed="handleClosed"
  >
    <div class="ele-body">
      <el-alert
        title="批量编辑说明"
        type="info"
        :closable="false"
        show-icon
        class="batch-edit-tips"
      >
        <div>
          <p>只有勾选的字段才会被更新，未勾选的字段保持原值不变</p>
          <p>当前选中了 {{ selectedRecords.length }} 条记录</p>
        </div>
      </el-alert>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-position="left"
        @submit.prevent=""
      >
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="work_category">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.work_category" />
                    <span class="label-text">工作类别</span>
                  </div>
                </template>
                <el-select
                  v-model="form.work_category"
                  placeholder="请选择工作类别"
                  style="width: 100%"
                  :disabled="!fieldEnabled.work_category"
                  clearable
                >
                  <el-option label="问题排查" value="问题排查" />
                  <el-option label="例行维护" value="例行维护" />
                  <el-option label="定期巡检" value="定期巡检" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field-with-checkbox">
              <el-form-item prop="result_verification_staff">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.result_verification_staff" />
                    <span class="label-text">结果验证人员</span>
                  </div>
                </template>
                <el-input
                  v-model="form.result_verification_staff"
                  placeholder="请输入结果验证人员"
                  :disabled="!fieldEnabled.result_verification_staff"
                  clearable
                  :maxlength="50"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="work_completion_time">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.work_completion_time" />
                    <span class="label-text">工作完成时间</span>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.work_completion_time"
                  type="datetime"
                  placeholder="选择工作完成时间"
                  style="width: 100%"
                  :disabled="!fieldEnabled.work_completion_time"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <!-- 工作描述 -->
        <div class="form-section-title">工作描述</div>
        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="work_basis_problem_description">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.work_basis_problem_description" />
                    <span class="label-text">工作依据/问题描述</span>
                  </div>
                </template>
                <el-input
                  v-model="form.work_basis_problem_description"
                  type="textarea"
                  placeholder="请输入工作依据或问题描述"
                  :disabled="!fieldEnabled.work_basis_problem_description"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  :maxlength="1000"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="work_content">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.work_content" />
                    <span class="label-text">工作内容</span>
                  </div>
                </template>
                <el-input
                  v-model="form.work_content"
                  type="textarea"
                  placeholder="请输入工作内容"
                  :disabled="!fieldEnabled.work_content"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  :maxlength="1000"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <div class="field-with-checkbox">
              <el-form-item prop="work_execution_result">
                <template #label>
                  <div class="form-label-with-checkbox">
                    <el-checkbox v-model="fieldEnabled.work_execution_result" />
                    <span class="label-text">工作执行结果</span>
                  </div>
                </template>
                <el-input
                  v-model="form.work_execution_result"
                  type="textarea"
                  placeholder="请输入工作执行结果"
                  :disabled="!fieldEnabled.work_execution_result"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  :maxlength="1000"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">
        批量保存
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus';
  import { batchUpdateInst } from '@/api/cmdb';

  defineOptions({ name: 'MaintenanceServiceRecordBatchEdit' });

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 选中的记录 */
    selectedRecords: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits(['update:modelValue', 'update:selectedRecords', 'done']);

  /** 模型实例ID */
  const bkObjId = 'maintenance_service_record';

  /** 弹窗是否打开 */
  const visible = computed(() => {
    return props.modelValue;
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 字段启用状态 */
  const fieldEnabled = ref({
    work_category: false,
    work_completion_time: false,
    result_verification_staff: false,
    work_basis_problem_description: false,
    work_content: false,
    work_execution_result: false
  });

  /** 表单数据 */
  const form = ref({
    work_category: '',
    work_completion_time: null,
    result_verification_staff: '',
    work_basis_problem_description: '',
    work_content: '',
    work_execution_result: ''
  });

  /** 表单验证规则 */
  const rules = computed(() => {
    return {};
  });

  /** 更新visible */
  const updateVisible = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭回调 */
  const handleClosed = () => {
    resetFields();
  };

  /** 保存 */
  const handleSave = async () => {
    if (!props.selectedRecords.length) {
      EleMessage.error('没有选中的记录');
      return;
    }

    // 检查是否有启用的字段
    const enabledFields = Object.keys(fieldEnabled.value).filter(
      (key) => fieldEnabled.value[key]
    );

    if (enabledFields.length === 0) {
      EleMessage.error('请至少选择一个要更新的字段');
      return;
    }

    const confirmResult = await ElMessageBox.confirm(
      `确定要批量更新选中的 ${props.selectedRecords.length} 条维保服务记录吗？`,
      '确认批量更新',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => false);

    if (!confirmResult) return;

    loading.value = true;
    try {
      // 构建更新数据，只包含启用的字段
      const updateData = {};
      enabledFields.forEach((field) => {
        updateData[field] = form.value[field];
      });

      const instIds = props.selectedRecords.map((item) => item.bk_inst_id);

      await batchUpdateInst({
        bkObjId,
        instIds: instIds,
        instInfoMap: updateData
      });

      EleMessage.success('批量更新成功');
      updateVisible(false);
      emit('done');
    } catch (e) {
      EleMessage.error(e.message || '批量更新失败');
    }
    loading.value = false;
  };

  /** 取消 */
  const handleCancel = () => {
    updateVisible(false);
  };

  /** 重置表单 */
  const resetFields = () => {
    formRef.value?.resetFields();
    // 重置字段启用状态
    Object.keys(fieldEnabled.value).forEach((key) => {
      fieldEnabled.value[key] = false;
    });
    // 重置表单数据
    Object.assign(form.value, {
      work_category: '',
      work_completion_time: null,
      result_verification_staff: '',
      work_basis_problem_description: '',
      work_content: '',
      work_execution_result: ''
    });
  };

  /** 监听选中记录变化 */
  watch(
    () => props.selectedRecords,
    (newVal) => {
      if (!newVal || newVal.length === 0) {
        resetFields();
      }
    }
  );
</script>

<style scoped>
  .ele-body {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .batch-edit-tips {
    margin-bottom: 20px;
  }

  .batch-edit-tips p {
    margin: 4px 0;
    line-height: 1.6;
  }

  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-section-title:first-child {
    margin-top: 0;
  }

  .field-with-checkbox {
    width: 100%;
  }

  .form-label-with-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .label-text {
    white-space: nowrap;
  }

  :deep(.el-form-item__label) {
    padding: 0 !important;
  }
</style>