<template>
  <div class="bg-gradient-blue rounded-2xl p-6 text-white shadow-lg">
    <!-- 标题行：系统名称与右侧控件 -->
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-[clamp(1.5rem,3vw,2rem)] font-bold text-left">
        {{ currentSystem.bk_biz_name || '系统加载中...' }}
      </h2>
      <div class="flex space-x-4">
        <!-- 应用切换下拉框 -->
        <el-select
          v-model="selectedSystemId"
          placeholder="切换应用"
          @change="handleSystemChange"
          class="w-64 custom-select"
          :loading="loading"
          filterable
          clearable
        >
          <el-option
            v-for="system in systemList"
            :key="system.bk_biz_id"
            :label="system.bk_biz_name"
            :value="system.bk_biz_id"
          />
        </el-select>
        <!-- 导出报告按钮 -->
        <el-button
          type="primary"
          :icon="Download"
          @click="handleExportReport"
          class="bg-white/20 border-white/30 hover:bg-white/30"
        >
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 介绍文本 -->
    <div class="text-left mb-6">
      <p class="text-blue-100 text-left w-full">
        {{ currentSystem.introduction || '系统介绍，功能描述......' }}
      </p>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mt-8">
      <div
        v-for="(item, index) in stats"
        :key="index"
        class="bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all-300"
      >
        <p class="text-blue-100 text-sm mb-1">{{ item.label }}</p>
        <p v-if="!item.isStatus" class="font-semibold">{{ item.value }}</p>
        <p v-else class="font-semibold flex items-center">
          <el-icon class="text-green-300 mr-1"><SuccessFilled /></el-icon>
          {{ item.value }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue';
  import { SuccessFilled, Download } from '@element-plus/icons-vue';
  import { searchBusiness } from '@/api/cmdb/index.js';
  import { ElMessage } from 'element-plus';

  // 响应式数据
  const loading = ref(false);
  const systemList = ref([]);
  const selectedSystemId = ref(null);
  const currentSystemData = ref({});

  // 计算属性：当前选中的系统信息
  const currentSystem = computed(() => {
    if (
      currentSystemData.value &&
      Object.keys(currentSystemData.value).length > 0
    ) {
      return {
        bk_biz_name: currentSystemData.value.bk_biz_name || '未知系统',
        introduction:
          currentSystemData.value.introduction || '系统介绍，功能描述......'
      };
    }
    return {
      name: '系统加载中...',
      description: '正在加载系统信息...'
    };
  });

  // 计算属性：系统统计数据
  const stats = computed(() => {
    const data = currentSystemData.value;
    return [
      {
        label: '系统等级',
        value: data.system_level || 'C',
        isStatus: false
      },
      {
        label: '开发负责人',
        value: data.system_developer || '',
        isStatus: false
      },
      {
        label: '运维负责人',
        value: data.system_maintainer || '',
        isStatus: false
      },
      {
        label: '开发厂商',
        value: data.development_vendor || '',
        isStatus: false
      },
      {
        label: '是否交维',
        value: data.whether_handed_over_for_maintenance
          ? '是'
          : data.whether_handed_over_for_maintenance === false
            ? '否'
            : '否',
        isStatus:
          data.whether_handed_over_for_maintenance === '是' ? true : false
      },
      {
        label: '交维日期',
        value: data.handover_date_for_maintenance || '',
        isStatus: false
      },
      {
        label: '是否演练过',
        value: data.whether_drilled
          ? '是'
          : data.whether_drilled === false
            ? '否'
            : '否',
        isStatus: data.whether_drilled === '是' ? true : false
      },
      {
        label: '演练日期',
        value: data.drill_date || '',
        isStatus: false
      }
    ];
  });

  // 获取应用系统列表
  const fetchSystemList = async () => {
    try {
      loading.value = true;
      const response = await searchBusiness();

      if (response.code === 200 && response.rows) {
        systemList.value = response.rows;

        // 默认选择第一个系统
        if (systemList.value.length > 0) {
          selectedSystemId.value = systemList.value[0].bk_biz_id;
          currentSystemData.value = systemList.value[0];
          // 发出初始系统数据到父组件
          emit('system-changed', systemList.value[0]);
        }
      } else {
        ElMessage.warning('获取应用系统列表失败');
      }
    } catch (error) {
      console.error('获取应用系统列表失败:', error);
      ElMessage.error('获取应用系统列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 处理导出报告
  const handleExportReport = () => {
    // TODO: 实现导出逻辑
    ElMessage.info('导出功能开发中...');
  };

  // 处理系统切换
  const handleSystemChange = (systemId) => {
    const selectedSystem = systemList.value.find(
      (system) => system.bk_biz_id === systemId
    );
    if (selectedSystem) {
      currentSystemData.value = selectedSystem;
      // 发出事件通知父组件系统已切换
      emit('system-changed', selectedSystem);
    }
  };

  // 暴露给父组件的事件
  const emit = defineEmits(['system-changed']);

  // 组件挂载时获取数据
  onMounted(() => {
    fetchSystemList();
  });

  // 暴露当前系统数据给父组件
  defineExpose({
    currentSystem: currentSystemData,
    refreshData: fetchSystemList
  });
</script>

<style scoped>
  /* 渐变背景 */
  .bg-gradient-blue {
    background: linear-gradient(135deg, #165dff 0%, #0e42d2 100%);
  }

  /* 过渡动画 */
  .transition-all-300 {
    transition: all 0.3s ease;
  }

  /* 文本颜色 */
  .text-blue-100 {
    color: rgba(255, 255, 255, 0.8);
  }

  .text-green-300 {
    color: #86efac;
  }

  /* 阴影 */
  .shadow-lg {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* 背景模糊 */
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  /* 网格布局响应式 */
  .grid {
    display: grid;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  @media (min-width: 768px) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .md\:flex-row {
      flex-direction: row;
    }

    .md\:items-center {
      align-items: center;
    }

    .md\:mt-0 {
      margin-top: 0;
    }
  }

  @media (min-width: 1024px) {
    .lg\:grid-cols-8 {
      grid-template-columns: repeat(8, minmax(0, 1fr));
    }
  }

  /* Flexbox */
  .flex {
    display: flex;
  }

  .flex-col {
    flex-direction: column;
  }

  .items-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .space-x-4 > * + * {
    margin-left: 1rem;
  }

  /* 间距 */
  .gap-4 {
    gap: 1rem;
  }

  .p-6 {
    padding: 1.5rem;
  }

  .p-4 {
    padding: 1rem;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  .mb-1 {
    margin-bottom: 0.25rem;
  }

  .mt-8 {
    margin-top: 2rem;
  }

  .mt-6 {
    margin-top: 1.5rem;
  }

  .mr-1 {
    margin-right: 0.25rem;
  }

  /* 文本样式 */
  .text-white {
    color: #ffffff;
  }

  .text-left {
    text-align: left;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .font-bold {
    font-weight: 700;
  }

  .font-semibold {
    font-weight: 600;
  }

  .max-w-2xl {
    max-width: 42rem;
  }

  .w-full {
    width: 100%;
  }

  /* 圆角 */
  .rounded-2xl {
    border-radius: 1rem;
  }

  .rounded-xl {
    border-radius: 0.75rem;
  }

  /* 背景透明度和模糊 */
  .bg-white\/10 {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .hover\:bg-white\/20:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  /* 响应式文字大小 */
  .text-\[clamp\(1\.5rem\,3vw\,2rem\)\] {
    font-size: clamp(1.5rem, 3vw, 2rem);
  }

  /* 自定义下拉框样式 */
  .custom-select :deep(.el-input__wrapper) {
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.75rem;
    backdrop-filter: blur(4px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .custom-select :deep(.el-input__wrapper):hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 6px 12px -2px rgba(0, 0, 0, 0.15);
  }

  .custom-select :deep(.el-input__wrapper.is-focus) {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
  }

  .custom-select :deep(.el-input__inner) {
    color: white !important;
    font-weight: 500;
  }

  .custom-select :deep(.el-input__inner::placeholder) {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .custom-select :deep(.el-select__selected-item) {
    color: white !important;
  }

  .custom-select :deep(.el-select__caret) {
    color: rgba(255, 255, 255, 0.8);
  }

  .custom-select :deep(.el-select__caret):hover {
    color: white;
  }
</style>
